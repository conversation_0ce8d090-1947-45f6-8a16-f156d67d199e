!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var n in i)("object"==typeof exports?exports:t)[n]=i[n]}}(globalThis,()=>(()=>{var t={80753:function(t,e,i){"use strict";i.d(e,{O1:()=>l,Yt:()=>n,bf:()=>u,cD:()=>a,cc:()=>s,ci:()=>d,gR:()=>p,hz:()=>h,im:()=>c,v_:()=>r,w9:()=>o});let n=["redirect_url","after_sign_in_url","after_sign_up_url","sign_in_force_redirect_url","sign_in_fallback_redirect_url","sign_up_force_redirect_url","sign_up_fallback_redirect_url"],r="__clerk_modal_state",a="__clerk_synced",s="suffixed_cookies",o="__clerk_satellite_url",l={FORM_IDENTIFIER_NOT_FOUND:"form_identifier_not_found",FORM_PASSWORD_INCORRECT:"form_password_incorrect",FORM_PASSWORD_PWNED:"form_password_pwned",INVALID_STRATEGY_FOR_USER:"strategy_for_user_invalid",NOT_ALLOWED_TO_SIGN_UP:"not_allowed_to_sign_up",OAUTH_ACCESS_DENIED:"oauth_access_denied",OAUTH_EMAIL_DOMAIN_RESERVED_BY_SAML:"oauth_email_domain_reserved_by_saml",NOT_ALLOWED_ACCESS:"not_allowed_access",SAML_USER_ATTRIBUTE_MISSING:"saml_user_attribute_missing",USER_LOCKED:"user_locked",EXTERNAL_ACCOUNT_NOT_FOUND:"external_account_not_found",SIGN_UP_MODE_RESTRICTED:"sign_up_mode_restricted",SIGN_UP_MODE_RESTRICTED_WAITLIST:"sign_up_restricted_waitlist",ENTERPRISE_SSO_USER_ATTRIBUTE_MISSING:"enterprise_sso_user_attribute_missing",ENTERPRISE_SSO_EMAIL_ADDRESS_DOMAIN_MISMATCH:"enterprise_sso_email_address_domain_mismatch",ENTERPRISE_SSO_HOSTED_DOMAIN_MISMATCH:"enterprise_sso_hosted_domain_mismatch",SAML_EMAIL_ADDRESS_DOMAIN_MISMATCH:"saml_email_address_domain_mismatch",INVITATION_ACCOUNT_NOT_EXISTS:"invitation_account_not_exists",ORGANIZATION_MEMBERSHIP_QUOTA_EXCEEDED_FOR_SSO:"organization_membership_quota_exceeded_for_sso",CAPTCHA_INVALID:"captcha_invalid",FRAUD_DEVICE_BLOCKED:"device_blocked",FRAUD_ACTION_BLOCKED:"action_blocked",SIGNUP_RATE_LIMIT_EXCEEDED:"signup_rate_limit_exceeded"},c=["email_address","phone_number","username"],u=["email_address","phone_number","username","first_name","last_name"],h=350,d={PUBLIC:"public",RESTRICTED:"restricted",WAITLIST:"waitlist"},p="2025-04-10"},24152:function(t,e,i){"use strict";i.d(e,{$C:()=>E,C:()=>p,Dg:()=>s,FI:()=>x,G6:()=>c,HE:()=>u,O7:()=>d,PQ:()=>O,RE:()=>U,Rz:()=>f,WC:()=>m,Ws:()=>g,Xp:()=>a,_5:()=>S,an:()=>r,cT:()=>b,ep:()=>v,k2:()=>l,lb:()=>_,qO:()=>y,qW:()=>k,rn:()=>h,sY:()=>A,xZ:()=>w,yI:()=>o}),i(92037);let n="ClerkJS:";function r(t,e){throw Error("".concat(n,' Network error at "').concat(t,'" - ').concat(e,". Please try again."))}function a(){throw Error("".concat(n," Something went wrong initializing Clerk."))}function s(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";throw Error("".concat(n," Something went wrong initializing Clerk in development mode.").concat(t&&" ".concat(t)))}function o(t){throw Error("".concat(n," Missing path option. The ").concat(t,' component was mounted with path routing so you need to specify the path where the component is mounted on e.g. path="/sign-in".'))}function l(t){throw Error("".concat(n," You must wrap your application in a <").concat(t,"> component."))}function c(){throw Error("".concat(n," Clerk is undefined"))}function u(){throw Error("".concat(n," The target element is empty. Provide a valid DOM element."))}function h(){throw Error("".concat(n," Missing FAPI client in resources."))}function d(t){throw Error("".concat(n," Something went wrong initializing Clerk during the ").concat(t," flow. Please contact support."))}function p(t){throw Error("".concat(n," You need to start a ").concat(t," flow by calling ").concat(t,".create() first."))}function f(t,e){throw Error("".concat(n,' Strategy "').concat(e,'" is not a valid strategy for ').concat(t,"."))}function _(t){throw Error("".concat(n," You need to start a ").concat(t," flow by calling ").concat(t,".create({ identifier: 'your web3 wallet address' }) first"))}function v(){throw Error("".concat(n," You need to start a SignIn flow by calling SignIn.create({ strategy: 'passkey' }) first"))}function m(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";throw Error("".concat(n," Missing '").concat(t,"' option"))}function g(t,e){throw Error("".concat(n," Response: ").concat(t||0," not supported yet.\nFor more information contact us at ").concat(e))}function y(){throw Error("".concat(n," Missing dev browser jwt. Please contact support."))}function w(){throw Error("".concat(n," Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl."))}function b(){throw Error("".concat(n," The signInUrl needs to be on a different origin than your satellite application."))}function S(){throw Error("".concat(n," The signInUrl needs to have a absolute url format."))}function k(){throw Error("".concat(n," Missing signInUrl. A satellite application needs to specify the signInUrl for development instances."))}function A(){throw Error("".concat(n," Invalid redirect_url. A valid http or https url should be used for the redirection."))}function x(t){throw Error("".concat(n," Unable to retrieve a third party script").concat(t?" ".concat(t):"","."))}function O(t){throw Error("".concat(n," Invalid routing strategy, path cannot be used in tandem with ").concat(t,"."))}function U(t){throw Error("".concat(n," Calling ").concat(t,".reload is not currently supported. Please contact support."))}function E(t){throw Error("".concat(n," Missing publicKey. When calling 'navigator.credentials.").concat(t,"()' it is required to pass a publicKey object."))}},50005:function(t,e,i){"use strict";i.d(e,{Y:()=>a,U:()=>r});var n=i(86225);i(64310);let r={TokenUpdate:"token:update",UserSignOut:"user:signOut",EnvironmentUpdate:"environment:update",SessionTokenResolved:"session:tokenResolved"},a=(0,n.n)()},4843:function(t,e,i){"use strict";i.d(e,{C9:()=>l,_G:()=>o,iL:()=>s,QE:()=>c});var n=i(17431),r=i(90750),a=i(73139);class s{constructor(){(0,n._)(this,"getPlans",async t=>{let{data:e}=await a.i._fetch({path:"/commerce/products",method:"GET",search:{payerType:(null==t?void 0:t.subscriberType)||""}}),i=e.find(t=>t.is_default);return(null==i?void 0:i.plans.map(t=>new a.Cs(t)))||[]}),(0,n._)(this,"getSubscriptions",async t=>{let{orgId:e,...i}=t;return await a.i._fetch({path:e?"/organizations/".concat(e,"/commerce/subscriptions"):"/me/commerce/subscriptions",method:"GET",search:(0,r.C)(i)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new a.gU(t))}})}),(0,n._)(this,"getStatements",async t=>{let{orgId:e,...i}=t;return await a.i._fetch({path:e?"/organizations/".concat(e,"/commerce/statements"):"/me/commerce/statements",method:"GET",search:(0,r.C)(i)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new a.j3(t))}})}),(0,n._)(this,"getPaymentAttempts",async t=>{let{orgId:e,...i}=t;return await a.i._fetch({path:e?"/organizations/".concat(e,"/commerce/payment_attempts"):"/me/commerce/payment_attempts",method:"GET",search:(0,r.C)(i)}).then(t=>{let{data:e,total_count:i}=t;return{total_count:i,data:e.map(t=>new a.Pm(t))}})}),(0,n._)(this,"startCheckout",async t=>{var e;let{orgId:i,...n}=t,r=null===(e=await a.i._fetch({path:i?"/organizations/".concat(i,"/commerce/checkouts"):"/me/commerce/checkouts",method:"POST",body:n}))||void 0===e?void 0:e.response;return new a.Dl(r,i)})}}let o=async t=>{var e;let{orgId:i,...n}=t,r=null===(e=await a.i._fetch({path:i?"/organizations/".concat(i,"/commerce/payment_sources/initialize"):"/me/commerce/payment_sources/initialize",method:"POST",body:n}))||void 0===e?void 0:e.response;return new a.zv(r)},l=async t=>{var e;let{orgId:i,...n}=t,r=null===(e=await a.i._fetch({path:i?"/organizations/".concat(i,"/commerce/payment_sources"):"/me/commerce/payment_sources",method:"POST",body:n}))||void 0===e?void 0:e.response;return new a.wg(r)},c=async t=>{let{orgId:e,...i}=t;return await a.i._fetch({path:e?"/organizations/".concat(e,"/commerce/payment_sources"):"/me/commerce/payment_sources",method:"GET",search:(0,r.C)(i)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new a.wg(t))}})}},60104:function(t,e,i){"use strict";i.d(e,{T:()=>s});var n=i(17431),r=i(46630),a=i(73139);class s extends a.i{fromJSON(t){return t&&(this.claimedAt=this.withDefault(t.claimed_at?(0,r.V)(t.claimed_at):null,this.claimedAt),this.reverification=this.withDefault(t.reverification,this.reverification),this.singleSessionMode=this.withDefault(t.single_session_mode,this.singleSessionMode),this.preferredChannels=this.withDefault(t.preferred_channels,this.preferredChannels)),this}__internal_toSnapshot(){var t;return{claimed_at:this.claimedAt?this.claimedAt.getTime():null,id:null!==(t=this.id)&&void 0!==t?t:"",object:"auth_config",reverification:this.reverification,single_session_mode:this.singleSessionMode}}constructor(t=null){super(),(0,n._)(this,"claimedAt",null),(0,n._)(this,"reverification",!1),(0,n._)(this,"singleSessionMode",!1),(0,n._)(this,"preferredChannels",null),this.fromJSON(t)}}},28069:function(t,e,i){"use strict";i.d(e,{i:()=>u});var n=i(17431);i(87945),i(56113),i(50725);var r=i(72708),a=i(41402),s=i(24152),o=i(97976),l=i(73139);class c{static getInstance(){return c.instance||(c.instance=new c(l.KU,o.E)),c.instance}async execute(t,e){if(this.captchaAttemptsExceeded())throw new l.w$("Security verification failed. Please try again by refreshing the page, clearing your browser cookies, or using a different web browser.",{code:"captcha_client_attempts_exceeded"});try{return this.inflightException&&await this.inflightException,await e()}catch(r){var i;let n;if(!(0,l.kD)(r)||(null===(i=r.errors[0])||void 0===i?void 0:i.code)!=="requires_captcha")throw r;if(this.inflightException)return await this.inflightException,await e();this.inflightException=new Promise(t=>n=t);try{let e=await this.managedChallenge(t);(null==e?void 0:e.captchaError)!=="modal_component_not_ready"&&(await this.client.getOrCreateInstance().__internal_sendCaptchaToken(e),this.captchaRetryCount=0)}catch(t){throw this.captchaRetryCount++,t}finally{n(),this.inflightException=null}return await e()}}managedChallenge(t){return new this.CaptchaChallengeImpl(t).managedInModal({action:"verify"})}constructor(t,e){(0,n._)(this,"client",void 0),(0,n._)(this,"CaptchaChallengeImpl",void 0),(0,n._)(this,"inflightException",void 0),(0,n._)(this,"captchaRetryCount",void 0),(0,n._)(this,"MAX_RETRY_ATTEMPTS",void 0),(0,n._)(this,"captchaAttemptsExceeded",void 0),this.client=t,this.CaptchaChallengeImpl=e,this.inflightException=null,this.captchaRetryCount=0,this.MAX_RETRY_ATTEMPTS=3,this.captchaAttemptsExceeded=()=>this.captchaRetryCount>=this.MAX_RETRY_ATTEMPTS}}(0,n._)(c,"instance",void 0);class u{static get fapiClient(){return u.clerk.getFapiClient()}async reload(t){let{rotatingTokenNonce:e}=t||{};return this._baseGet({forceUpdateClient:!0,rotatingTokenNonce:e})}isNew(){return!this.id}static async _fetch(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c.getInstance().execute(this.clerk,()=>this._baseFetch(t,e))}static async _baseFetch(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};u.fapiClient||(0,s.rn)();let{fetchMaxTries:n}=i;try{e=await u.fapiClient.request(t,{fetchMaxTries:n})}catch(t){if(this.shouldRethrowOfflineNetworkErrors())throw new l.w$((null==t?void 0:t.message)||t,{code:"network_error"});if(!(0,r.af)())return console.warn(t),null;throw t}let{payload:o,status:c,statusText:h,headers:d}=e;if(d){let t=d.get("x-country");this.clerk.__internal_setCountry(t?t.toLowerCase():null)}if(("GET"!==t.method||i.forceUpdateClient)&&this._updateClient(o),c>=200&&c<=299)return o;if(c>=400){var p,f;let t=null==o?void 0:o.errors,e=null==t?void 0:null===(p=t[0])||void 0===p?void 0:p.long_message,i=null==t?void 0:null===(f=t[0])||void 0===f?void 0:f.code;401===c&&"requires_captcha"!==i&&await u.clerk.handleUnauthenticated(),function(t,e){if(!e||!e[0])return;let i=e[0],n=i.long_message;if("origin_invalid"===i.code&&(0,a.mA)(u.clerk.publishableKey)){let i=u.clerk.frontendApi.replace("clerk.","");throw new l.gO('Clerk: Production Keys are only allowed for domain "'.concat(i,'". \nAPI Error: ').concat(n),{data:e,status:t})}}(c,t);let n={data:t,status:c};if(429===c&&d){let t=d.get("retry-after");if(t){let e=parseInt(t,10);isNaN(e)||(n.retryAfter=e)}}throw new l.gO(e||h,n)}return null}static _updateClient(t){var e;if(!t)return;let i=t.client||(null===(e=t.meta)||void 0===e?void 0:e.client);i&&u.clerk&&u.clerk.updateClient(l.KU.getOrCreateInstance().fromJSON(i))}path(t){let e=this.pathRoot;if(this.isNew())return e;let i=e.replace(/[^/]$/,"$&/")+encodeURIComponent(this.id);return t?i.replace(/[^/]$/,"$&/")+encodeURIComponent(t):i}withDefault(t,e){return null!=t?t:e}async _baseGet(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=await u._fetch({method:"GET",path:this.path(),rotatingTokenNonce:t.rotatingTokenNonce},t);return this.fromJSON((null==e?void 0:e.response)||e)}async _baseMutate(t){let{action:e,body:i,method:n,path:r}=t,a=await u._fetch({method:n,path:r||this.path(e),body:i});return this.fromJSON((null==a?void 0:a.response)||a)}async _baseMutateBypass(t){let{action:e,body:i,method:n,path:r}=t,a=await u._baseFetch({method:n,path:r||this.path(e),body:i});return this.fromJSON((null==a?void 0:a.response)||a)}async _basePost(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._baseMutate({...t,method:"POST"})}async _basePostBypass(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._baseMutateBypass({...t,method:"POST"})}async _basePut(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._baseMutate({...t,method:"PUT"})}async _basePatch(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._baseMutate({...t,method:"PATCH"})}async _baseDelete(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await this._baseMutate({...t,method:"DELETE"})}static shouldRethrowOfflineNetworkErrors(){var t,e;let i=null===(e=u.clerk)||void 0===e?void 0:null===(t=e.__internal_getOption)||void 0===t?void 0:t.call(e,"experimental");return(null==i?void 0:i.rethrowOfflineNetworkErrors)||!1}constructor(){(0,n._)(this,"id",void 0),(0,n._)(this,"pathRoot","")}}(0,n._)(u,"clerk",void 0)},29401:function(t,e,i){"use strict";i.d(e,{K:()=>o});var n=i(17431);i(50725);var r=i(46630),a=i(7292),s=i(73139);class o extends s.i{static getOrCreateInstance(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return o.instance||(o.instance=new o(t)),o.instance}static clearInstance(){o.instance=null}static isClientResource(t){return!!t&&t instanceof o}get signUpAttempt(){return this.signUp}get signInAttempt(){return this.signIn}get activeSessions(){return this.sessions.filter(t=>"active"===t.status)}get signedInSessions(){return this.sessions.filter(t=>"active"===t.status||"pending"===t.status)}create(){return this._basePut()}fetch(){let{fetchMaxTries:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._baseGet({fetchMaxTries:t})}async destroy(){return this._baseDelete({path:"/client"}).then(()=>{a.O.clear(),this.id="",this.sessions=[],this.signUp=new s.Mo(null),this.signIn=new s.cL(null),this.lastActiveSessionId=null,this.cookieExpiresAt=null,this.createdAt=null,this.updatedAt=null})}removeSessions(){return this._baseDelete({path:this.path()+"/sessions"}).then(t=>(a.O.clear(),t))}clearCache(){return this.sessions.forEach(t=>t.clearCache())}isEligibleForTouch(){return!!this.cookieExpiresAt&&this.cookieExpiresAt.getTime()-Date.now()<=6912e5}buildTouchUrl(t){let{redirectUrl:e}=t;return s.i.fapiClient.buildUrl({method:"GET",path:"/client/touch",pathPrefix:"v1",search:{redirect_url:e.toString()}}).toString()}__internal_sendCaptchaToken(t){return this._basePostBypass({body:t,path:this.path()+"/verify"})}fromJSON(t){return t&&(this.id=t.id,this.sessions=(t.sessions||[]).map(t=>new s.z_(t)),this.signUp=new s.Mo(t.sign_up),this.signIn=new s.cL(t.sign_in),this.lastActiveSessionId=t.last_active_session_id,this.captchaBypass=t.captcha_bypass||!1,this.cookieExpiresAt=t.cookie_expires_at?(0,r.V)(t.cookie_expires_at):null,this.createdAt=(0,r.V)(t.created_at||void 0),this.updatedAt=(0,r.V)(t.updated_at||void 0)),this}__internal_toSnapshot(){var t,e,i,n;return{object:"client",id:this.id||"",sessions:this.sessions.map(t=>t.__internal_toSnapshot()),sign_up:this.signUp.__internal_toSnapshot(),sign_in:this.signIn.__internal_toSnapshot(),last_active_session_id:this.lastActiveSessionId,captcha_bypass:this.captchaBypass,cookie_expires_at:this.cookieExpiresAt?this.cookieExpiresAt.getTime():null,created_at:null!==(i=null===(t=this.createdAt)||void 0===t?void 0:t.getTime())&&void 0!==i?i:null,updated_at:null!==(n=null===(e=this.updatedAt)||void 0===e?void 0:e.getTime())&&void 0!==n?n:null}}path(){return this.pathRoot}constructor(t=null){super(),(0,n._)(this,"pathRoot","/client"),(0,n._)(this,"sessions",[]),(0,n._)(this,"signUp",new s.Mo),(0,n._)(this,"signIn",new s.cL),(0,n._)(this,"lastActiveSessionId",null),(0,n._)(this,"captchaBypass",!1),(0,n._)(this,"cookieExpiresAt",null),(0,n._)(this,"createdAt",null),(0,n._)(this,"updatedAt",null),this.fromJSON(t)}}(0,n._)(o,"instance",void 0)},74498:function(t,e,i){"use strict";i.d(e,{l:()=>a});var n=i(17431),r=i(73139);class a extends r.i{fromJSON(t){return t&&(this.afterCreateOrganizationUrl=this.withDefault(t.after_create_organization_url,this.afterCreateOrganizationUrl),this.afterJoinWaitlistUrl=this.withDefault(t.after_join_waitlist_url,this.afterJoinWaitlistUrl),this.afterLeaveOrganizationUrl=this.withDefault(t.after_leave_organization_url,this.afterLeaveOrganizationUrl),this.afterSignInUrl=this.withDefault(t.after_sign_in_url,this.afterSignInUrl),this.afterSignOutAllUrl=this.withDefault(t.after_sign_out_all_url,this.afterSignOutAllUrl),this.afterSignOutOneUrl=this.withDefault(t.after_sign_out_one_url,this.afterSignOutOneUrl),this.afterSignUpUrl=this.withDefault(t.after_sign_up_url,this.afterSignUpUrl),this.afterSwitchSessionUrl=this.withDefault(t.after_switch_session_url,this.afterSwitchSessionUrl),this.applicationName=this.withDefault(t.application_name,this.applicationName),this.branded=this.withDefault(t.branded,this.branded),this.captchaHeartbeat=this.withDefault(t.captcha_heartbeat,this.captchaHeartbeat),this.captchaHeartbeatIntervalMs=this.withDefault(t.captcha_heartbeat_interval_ms,this.captchaHeartbeatIntervalMs),this.captchaOauthBypass=this.withDefault(t.captcha_oauth_bypass,this.captchaOauthBypass),this.captchaProvider=this.withDefault(t.captcha_provider,this.captchaProvider),this.captchaPublicKey=this.withDefault(t.captcha_public_key,this.captchaPublicKey),this.captchaPublicKeyInvisible=this.withDefault(t.captcha_public_key_invisible,this.captchaPublicKeyInvisible),this.captchaWidgetType=this.withDefault(t.captcha_widget_type,this.captchaWidgetType),this.clerkJSVersion=this.withDefault(t.clerk_js_version,this.clerkJSVersion),this.createOrganizationUrl=this.withDefault(t.create_organization_url,this.createOrganizationUrl),this.faviconImageUrl=this.withDefault(t.favicon_image_url,this.faviconImageUrl),this.googleOneTapClientId=this.withDefault(t.google_one_tap_client_id,this.googleOneTapClientId),this.homeUrl=this.withDefault(t.home_url,this.homeUrl),this.id=this.withDefault(t.id,this.id),this.instanceEnvironmentType=this.withDefault(t.instance_environment_type,this.instanceEnvironmentType),this.logoImageUrl=this.withDefault(t.logo_image_url,this.logoImageUrl),this.organizationProfileUrl=this.withDefault(t.organization_profile_url,this.organizationProfileUrl),this.preferredSignInStrategy=this.withDefault(t.preferred_sign_in_strategy,this.preferredSignInStrategy),this.privacyPolicyUrl=this.withDefault(t.privacy_policy_url,this.privacyPolicyUrl),this.showDevModeWarning=this.withDefault(t.show_devmode_warning,this.showDevModeWarning),this.signInUrl=this.withDefault(t.sign_in_url,this.signInUrl),this.signUpUrl=this.withDefault(t.sign_up_url,this.signUpUrl),this.supportEmail=this.withDefault(t.support_email,this.supportEmail),this.termsUrl=this.withDefault(t.terms_url,this.termsUrl),this.theme=this.withDefault(t.theme,this.theme),this.userProfileUrl=this.withDefault(t.user_profile_url,this.userProfileUrl),this.waitlistUrl=this.withDefault(t.waitlist_url,this.waitlistUrl)),this}__internal_toSnapshot(){return{object:"display_config",after_create_organization_url:this.afterCreateOrganizationUrl,after_join_waitlist_url:this.afterJoinWaitlistUrl,after_leave_organization_url:this.afterLeaveOrganizationUrl,after_sign_in_url:this.afterSignInUrl,after_sign_out_all_url:this.afterSignOutAllUrl,after_sign_out_one_url:this.afterSignOutOneUrl,after_sign_up_url:this.afterSignUpUrl,after_switch_session_url:this.afterSwitchSessionUrl,application_name:this.applicationName,branded:this.branded,captcha_heartbeat_interval_ms:this.captchaHeartbeatIntervalMs,captcha_heartbeat:this.captchaHeartbeat,captcha_oauth_bypass:this.captchaOauthBypass,captcha_provider:this.captchaProvider,captcha_public_key_invisible:this.captchaPublicKeyInvisible,captcha_public_key:this.captchaPublicKey,captcha_widget_type:this.captchaWidgetType,clerk_js_version:this.clerkJSVersion,create_organization_url:this.createOrganizationUrl,favicon_image_url:this.faviconImageUrl,google_one_tap_client_id:this.googleOneTapClientId,home_url:this.homeUrl,id:this.id,instance_environment_type:this.instanceEnvironmentType,logo_image_url:this.logoImageUrl,organization_profile_url:this.organizationProfileUrl,preferred_sign_in_strategy:this.preferredSignInStrategy,privacy_policy_url:this.privacyPolicyUrl,show_devmode_warning:this.showDevModeWarning,sign_in_url:this.signInUrl,sign_up_url:this.signUpUrl,support_email:this.supportEmail,terms_url:this.termsUrl,theme:this.theme,user_profile_url:this.userProfileUrl,waitlist_url:this.waitlistUrl}}constructor(t=null){super(),(0,n._)(this,"afterCreateOrganizationUrl",""),(0,n._)(this,"afterJoinWaitlistUrl",""),(0,n._)(this,"afterLeaveOrganizationUrl",""),(0,n._)(this,"afterSignInUrl",""),(0,n._)(this,"afterSignOutAllUrl",""),(0,n._)(this,"afterSignOutOneUrl",""),(0,n._)(this,"afterSignOutUrl",""),(0,n._)(this,"afterSignUpUrl",""),(0,n._)(this,"afterSwitchSessionUrl",""),(0,n._)(this,"applicationName",""),(0,n._)(this,"backendHost",""),(0,n._)(this,"branded",!1),(0,n._)(this,"captchaHeartbeat",!1),(0,n._)(this,"captchaHeartbeatIntervalMs",void 0),(0,n._)(this,"captchaOauthBypass",["oauth_google","oauth_microsoft","oauth_apple"]),(0,n._)(this,"captchaProvider","turnstile"),(0,n._)(this,"captchaPublicKey",null),(0,n._)(this,"captchaPublicKeyInvisible",null),(0,n._)(this,"captchaWidgetType",null),(0,n._)(this,"clerkJSVersion",void 0),(0,n._)(this,"createOrganizationUrl",""),(0,n._)(this,"experimental__forceOauthFirst",void 0),(0,n._)(this,"faviconImageUrl",""),(0,n._)(this,"googleOneTapClientId",void 0),(0,n._)(this,"homeUrl",""),(0,n._)(this,"id",""),(0,n._)(this,"instanceEnvironmentType",""),(0,n._)(this,"logoImageUrl",""),(0,n._)(this,"organizationProfileUrl",""),(0,n._)(this,"preferredSignInStrategy","password"),(0,n._)(this,"privacyPolicyUrl",""),(0,n._)(this,"showDevModeWarning",!1),(0,n._)(this,"signInUrl",""),(0,n._)(this,"signUpUrl",""),(0,n._)(this,"supportEmail",""),(0,n._)(this,"termsUrl",""),(0,n._)(this,"theme",{}),(0,n._)(this,"userProfileUrl",""),(0,n._)(this,"waitlistUrl",""),this.fromJSON(t)}}},82986:function(t,e,i){"use strict";i.d(e,{b:()=>s});var n=i(17431);i(50725),i(92037);var r=i(96111),a=i(73139);class s extends a.i{create(){return this._basePost({body:{email_address:this.emailAddress}})}fromJSON(t){return t&&(this.id=t.id,this.emailAddress=t.email_address,this.verification=new a.GX(t.verification),this.matchesSsoConnection=t.matches_sso_connection,this.linkedTo=(t.linked_to||[]).map(t=>new a.YN(t))),this}__internal_toSnapshot(){return{object:"email_address",id:this.id,email_address:this.emailAddress,verification:this.verification.__internal_toSnapshot(),linked_to:this.linkedTo.map(t=>t.__internal_toSnapshot()),matches_sso_connection:this.matchesSsoConnection}}constructor(t,e){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"emailAddress",""),(0,n._)(this,"matchesSsoConnection",!1),(0,n._)(this,"linkedTo",[]),(0,n._)(this,"verification",void 0),(0,n._)(this,"prepareVerification",t=>this._basePost({action:"prepare_verification",body:{...t}})),(0,n._)(this,"attemptVerification",t=>{let{code:e}=t||{};return this._basePost({action:"attempt_verification",body:{code:e}})}),(0,n._)(this,"createEmailLinkFlow",()=>{let{run:t,stop:e}=(0,r.W)();return{startEmailLinkFlow:async i=>{let{redirectUrl:n}=i;return await this.prepareVerification({strategy:"email_link",redirectUrl:n}),new Promise((i,n)=>{t(()=>this.reload().then(t=>{"verified"===t.verification.status&&(e(),i(t))}).catch(t=>{e(),n(t)}))})},cancelEmailLinkFlow:e}}),(0,n._)(this,"createEnterpriseSSOLinkFlow",()=>{let{run:t,stop:e}=(0,r.W)();return{startEnterpriseSSOLinkFlow:async i=>{let{redirectUrl:n}=i;if(!(await this.prepareVerification({strategy:"enterprise_sso",redirectUrl:n})).verification.externalVerificationRedirectURL)throw Error("Unexpected: External verification redirect URL is missing");return new Promise((i,n)=>{t(()=>this.reload().then(t=>{"verified"===t.verification.status&&(e(),i(t))}).catch(t=>{e(),n(t)}))})},cancelEnterpriseSSOLinkFlow:e}}),(0,n._)(this,"destroy",()=>this._baseDelete()),(0,n._)(this,"toString",()=>this.emailAddress),this.pathRoot=e,this.fromJSON(t)}}},39357:function(t,e,i){"use strict";i.d(e,{q:()=>l});var n=i(17431),r=i(50005),a=i(73139);class s extends a.i{fromJSON(t){return t&&(this.enabled=this.withDefault(t.enabled,!1)),this}__internal_toSnapshot(){return{enabled:this.enabled}}constructor(t=null){super(),(0,n._)(this,"enabled",!1),this.fromJSON(t)}}class o extends a.i{fromJSON(t){return t&&(t.actions&&(this.actions.adminDelete=this.withDefault(t.actions.admin_delete,this.actions.adminDelete)),t.domains&&(this.domains.enabled=this.withDefault(t.domains.enabled,this.domains.enabled),this.domains.enrollmentModes=this.withDefault(t.domains.enrollment_modes,this.domains.enrollmentModes),this.domains.defaultRole=this.withDefault(t.domains.default_role,this.domains.defaultRole)),this.enabled=this.withDefault(t.enabled,this.enabled),this.maxAllowedMemberships=this.withDefault(t.max_allowed_memberships,this.maxAllowedMemberships),this.forceOrganizationSelection=this.withDefault(t.force_organization_selection,this.forceOrganizationSelection)),this}__internal_toSnapshot(){return{actions:{admin_delete:this.actions.adminDelete},domains:{enabled:this.domains.enabled,enrollment_modes:this.domains.enrollmentModes,default_role:this.domains.defaultRole},enabled:this.enabled,max_allowed_memberships:this.maxAllowedMemberships}}constructor(t=null){super(),(0,n._)(this,"actions",{adminDelete:!1}),(0,n._)(this,"domains",{enabled:!1,enrollmentModes:[],defaultRole:null}),(0,n._)(this,"enabled",!1),(0,n._)(this,"maxAllowedMemberships",1),(0,n._)(this,"forceOrganizationSelection",void 0),this.fromJSON(t)}}class l extends a.i{static getInstance(){return l.instance||(l.instance=new l),l.instance}fromJSON(t){return t&&(this.authConfig=new a.Tg(t.auth_config),this.displayConfig=new a.lR(t.display_config),this.maintenanceMode=this.withDefault(t.maintenance_mode,this.maintenanceMode),this.organizationSettings=new o(t.organization_settings),this.userSettings=new a.PG(t.user_settings),this.commerceSettings=new a.mM(t.commerce_settings),this.apiKeysSettings=new s(t.api_keys_settings)),this}fetch(){let{touch:t,fetchMaxTries:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{touch:!1};return(t?this._basePatch({}):this._baseGet({fetchMaxTries:e})).then(t=>(r.Y.emit(r.U.EnvironmentUpdate,null),t))}__internal_toSnapshot(){var t;return{object:"environment",auth_config:this.authConfig.__internal_toSnapshot(),display_config:this.displayConfig.__internal_toSnapshot(),id:null!==(t=this.id)&&void 0!==t?t:"",maintenance_mode:this.maintenanceMode,organization_settings:this.organizationSettings.__internal_toSnapshot(),user_settings:this.userSettings.__internal_toSnapshot(),commerce_settings:this.commerceSettings.__internal_toSnapshot(),api_keys_settings:this.apiKeysSettings.__internal_toSnapshot()}}constructor(t=null){super(),(0,n._)(this,"authConfig",new a.Tg),(0,n._)(this,"displayConfig",new a.lR),(0,n._)(this,"maintenanceMode",!1),(0,n._)(this,"pathRoot","/environment"),(0,n._)(this,"userSettings",new a.PG),(0,n._)(this,"organizationSettings",new o),(0,n._)(this,"commerceSettings",new a.mM),(0,n._)(this,"apiKeysSettings",new s),(0,n._)(this,"isDevelopmentOrStaging",()=>!this.isProduction()),(0,n._)(this,"isProduction",()=>"production"===this.displayConfig.instanceEnvironmentType),(0,n._)(this,"isSingleSession",()=>this.authConfig.singleSessionMode),(0,n._)(this,"onWindowLocationHost",()=>this.displayConfig.backendHost===window.location.host),this.fromJSON(t)}}(0,n._)(l,"instance",void 0)},83767:function(t,e,i){"use strict";i.d(e,{N:()=>n.N,ZC:()=>n.ZC,gO:()=>n.gO,kD:()=>n.kD,sZ:()=>n.sZ,uX:()=>n.uX,w$:()=>n.w$});var n=i(73531)},56516:function(t,e,i){"use strict";i.d(e,{z:()=>o});var n=i(17431);i(87945),i(56113);var r=i(65027),a=i(28069),s=i(69596);class o extends a.i{fromJSON(t){return t&&(this.id=t.id,this.identificationId=t.identification_id,this.providerUserId=t.provider_user_id,this.approvedScopes=t.approved_scopes,this.imageUrl=t.image_url,this.emailAddress=t.email_address,this.firstName=t.first_name,this.lastName=t.last_name,this.provider=(t.provider||"").replace("oauth_",""),this.username=t.username,this.phoneNumber=t.phone_number,this.publicMetadata=t.public_metadata,this.label=t.label,t.verification&&(this.verification=new s.GX(t.verification))),this}__internal_toSnapshot(){var t;return{object:"external_account",id:this.id,identification_id:this.identificationId,provider:this.provider,provider_user_id:this.providerUserId,email_address:this.emailAddress,approved_scopes:this.approvedScopes,first_name:this.firstName,last_name:this.lastName,image_url:this.imageUrl,username:this.username,phone_number:this.phoneNumber,public_metadata:this.publicMetadata,label:this.label,verification:(null===(t=this.verification)||void 0===t?void 0:t.__internal_toSnapshot())||null}}providerSlug(){return this.provider}providerTitle(){return[(0,r.MI)(this.providerSlug()),"Account"].join(" ")}accountIdentifier(){return this.username||this.emailAddress||this.label}constructor(t,e){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"identificationId",void 0),(0,n._)(this,"provider",void 0),(0,n._)(this,"providerUserId",""),(0,n._)(this,"emailAddress",""),(0,n._)(this,"approvedScopes",""),(0,n._)(this,"firstName",""),(0,n._)(this,"lastName",""),(0,n._)(this,"imageUrl",""),(0,n._)(this,"username",""),(0,n._)(this,"phoneNumber",""),(0,n._)(this,"publicMetadata",{}),(0,n._)(this,"label",""),(0,n._)(this,"verification",null),(0,n._)(this,"reauthorize",t=>{let{additionalScopes:e,redirectUrl:i}=t||{};return this._basePatch({action:"reauthorize",body:{additional_scope:e,redirect_url:i}})}),(0,n._)(this,"destroy",()=>this._baseDelete()),this.pathRoot=e,this.fromJSON(t)}}},71956:function(t,e,i){"use strict";i.d(e,{Y:()=>a});var n=i(17431),r=i(28069);class a extends r.i{fromJSON(t){return t&&(this.id=t.id,this.type=t.type),this}__internal_toSnapshot(){return{object:"identification_link",id:this.id,type:this.type}}constructor(t){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"type",void 0),this.fromJSON(t)}}},36890:function(t,e,i){"use strict";i.d(e,{E:()=>a});var n=i(17431);i(50725);var r=i(73139);class a extends r.i{static async create(t){var e;let i,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=n;return"string"==typeof n.file?(s=n.file,i=new Headers({"Content-Type":"application/octet-stream"})):n.file&&(s=new FormData).append("file",n.file),new a(null===(e=await r.i._fetch({path:t,method:"POST",body:s,headers:i}))||void 0===e?void 0:e.response)}static async delete(t){var e;return new a(null===(e=await r.i._fetch({path:t,method:"DELETE"}))||void 0===e?void 0:e.response)}fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.publicUrl=t.public_url),this}constructor(t){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"name",null),(0,n._)(this,"publicUrl",null),this.fromJSON(t)}}},19210:function(t,e,i){"use strict";i.d(e,{B:()=>a});var n=i(17431);i(56113);var r=i(73139);class a extends r.i{fromJSON(t){return t&&(this.id=t.id,this.phoneNumber=t.phone_number,this.reservedForSecondFactor=t.reserved_for_second_factor,this.defaultSecondFactor=t.default_second_factor,this.verification=new r.GX(t.verification),this.linkedTo=(t.linked_to||[]).map(t=>new r.YN(t)),this.backupCodes=t.backup_codes),this}__internal_toSnapshot(){return{object:"phone_number",id:this.id||"",phone_number:this.phoneNumber,reserved_for_second_factor:this.reservedForSecondFactor,default_second_factor:this.defaultSecondFactor,verification:this.verification.__internal_toSnapshot(),linked_to:this.linkedTo.map(t=>t.__internal_toSnapshot()),backup_codes:this.backupCodes}}constructor(t,e){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"phoneNumber",""),(0,n._)(this,"reservedForSecondFactor",!1),(0,n._)(this,"defaultSecondFactor",!1),(0,n._)(this,"linkedTo",[]),(0,n._)(this,"verification",void 0),(0,n._)(this,"backupCodes",void 0),(0,n._)(this,"create",()=>this._basePost({body:{phone_number:this.phoneNumber}})),(0,n._)(this,"prepareVerification",()=>this._basePost({action:"prepare_verification",body:{strategy:"phone_code"}})),(0,n._)(this,"attemptVerification",t=>{let{code:e}=t||{};return this._basePost({action:"attempt_verification",body:{code:e}})}),(0,n._)(this,"setReservedForSecondFactor",t=>{let{reserved:e}=t||{};return this._basePatch({body:{reserved_for_second_factor:e}})}),(0,n._)(this,"makeDefaultSecondFactor",()=>this._basePatch({body:{default_second_factor:!0}})),(0,n._)(this,"destroy",()=>this._baseDelete()),(0,n._)(this,"toString",()=>{let t=this.phoneNumber.match(/^(\d{3})(\d{3})(\d{4})$/);return t?"("+t[1]+") "+t[2]+"-"+t[3]:this.phoneNumber}),this.pathRoot=e,this.fromJSON(t)}}},52724:function(t,e,i){"use strict";i.d(e,{g:()=>o});var n=i(17431),r=i(46630),a=i(28069),s=i(69596);class o extends a.i{fromJSON(t){return t&&(this.id=t.id,this.provider=t.provider,this.providerUserId=t.provider_user_id,this.active=t.active,this.emailAddress=t.email_address,this.firstName=t.first_name,this.lastName=t.last_name,t.verification&&(this.verification=new s.GX(t.verification)),t.saml_connection&&(this.samlConnection=new l(t.saml_connection))),this}__internal_toSnapshot(){var t,e;return{object:"saml_account",id:this.id,provider:this.provider,provider_user_id:this.providerUserId,active:this.active,email_address:this.emailAddress,first_name:this.firstName,last_name:this.lastName,verification:(null===(t=this.verification)||void 0===t?void 0:t.__internal_toSnapshot())||null,saml_connection:null===(e=this.samlConnection)||void 0===e?void 0:e.__internal_toSnapshot()}}constructor(t,e){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"provider","saml_custom"),(0,n._)(this,"providerUserId",null),(0,n._)(this,"active",!1),(0,n._)(this,"emailAddress",""),(0,n._)(this,"firstName",""),(0,n._)(this,"lastName",""),(0,n._)(this,"verification",null),(0,n._)(this,"samlConnection",null),this.pathRoot=e,this.fromJSON(t)}}class l extends a.i{fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.domain=t.domain,this.active=t.active,this.provider=t.provider,this.syncUserAttributes=t.sync_user_attributes,this.allowSubdomains=t.allow_subdomains,this.allowIdpInitiated=t.allow_idp_initiated,this.disableAdditionalIdentifications=t.disable_additional_identifications,this.createdAt=(0,r.V)(t.created_at),this.updatedAt=(0,r.V)(t.updated_at)),this}__internal_toSnapshot(){return{object:"saml_account_connection",id:this.id,name:this.name,domain:this.domain,active:this.active,provider:this.provider,sync_user_attributes:this.syncUserAttributes,allow_subdomains:this.allowSubdomains,allow_idp_initiated:this.allowIdpInitiated,disable_additional_identifications:this.disableAdditionalIdentifications,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}constructor(t){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"name",void 0),(0,n._)(this,"domain",void 0),(0,n._)(this,"active",void 0),(0,n._)(this,"provider",void 0),(0,n._)(this,"syncUserAttributes",void 0),(0,n._)(this,"allowSubdomains",void 0),(0,n._)(this,"allowIdpInitiated",void 0),(0,n._)(this,"disableAdditionalIdentifications",void 0),(0,n._)(this,"createdAt",void 0),(0,n._)(this,"updatedAt",void 0),this.fromJSON(t)}}},67045:function(t,e,i){"use strict";i.d(e,{z:()=>S});var n=i(34056),r=i(12499),a=i(17970),s=i(91412),o=i(17431);i(65223),i(92037),i(50725);var l=i(71353);i(64310);var c=i(73531),u=i(60153),h=i(32208),d=i(46630),p=i(93367),f=i(24152),_=i(50005),v=i(7292),m=i(73139),g=i(65027);class y extends m.i{fromJSON(t){return t&&(this.id=t.id,this.status=t.status,this.session=new m.z_(t.session),this.level=t.level,this.supportedFirstFactors=(0,g.aw)(t.supported_first_factors),this.supportedSecondFactors=(0,g.aw)(t.supported_second_factors),this.firstFactorVerification=new m.GX(t.first_factor_verification),this.secondFactorVerification=new m.GX(t.second_factor_verification)),this}constructor(t=null){super(),(0,o._)(this,"status",void 0),(0,o._)(this,"level",void 0),(0,o._)(this,"session",void 0),(0,o._)(this,"supportedFirstFactors",[]),(0,o._)(this,"supportedSecondFactors",[]),(0,o._)(this,"firstFactorVerification",new m.GX(null)),(0,o._)(this,"secondFactorVerification",new m.GX(null)),this.fromJSON(t)}}var w=new WeakMap,b=new WeakSet;class S extends m.i{static isSessionResource(t){return!!t&&t instanceof S}fromJSON(t){return t&&(this.id=t.id,this.status=t.status,this.expireAt=(0,d.V)(t.expire_at),this.abandonAt=(0,d.V)(t.abandon_at),this.factorVerificationAge=t.factor_verification_age,this.lastActiveAt=(0,d.V)(t.last_active_at||void 0),this.lastActiveOrganizationId=t.last_active_organization_id,this.actor=t.actor||null,this.createdAt=(0,d.V)(t.created_at),this.updatedAt=(0,d.V)(t.updated_at),this.user=new m.n5(t.user),this.tasks=t.tasks||null,t.public_user_data&&(this.publicUserData=new m.VO(t.public_user_data)),this.lastActiveToken=t.last_active_token?new m.WU(t.last_active_token):null),this}__internal_toSnapshot(){var t,e;return{object:"session",id:this.id,status:this.status,expire_at:this.expireAt.getTime(),abandon_at:this.abandonAt.getTime(),factor_verification_age:this.factorVerificationAge,last_active_at:this.lastActiveAt.getTime(),last_active_organization_id:this.lastActiveOrganizationId,actor:this.actor,tasks:this.tasks,user:(null===(t=this.user)||void 0===t?void 0:t.__internal_toSnapshot())||null,public_user_data:this.publicUserData.__internal_toSnapshot(),last_active_token:(null===(e=this.lastActiveToken)||void 0===e?void 0:e.__internal_toSnapshot())||null,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}async _getToken(t){if(!this.user)return null;let{leewayInSeconds:e,template:i,skipCache:n=!1}=t||{},r=void 0===(null==t?void 0:t.organizationId)?this.lastActiveOrganizationId:null==t?void 0:t.organizationId;if(!i&&Number(e)>=60)throw Error("Leeway can not exceed the token lifespan (60 seconds)");let s=(0,a._)(this,b,k).call(this,i,r),o=n?void 0:v.O.get({tokenId:s},e),l=!i&&r===this.lastActiveOrganizationId;if(o){let t=await o.tokenResolver;return l&&_.Y.emit(_.U.TokenUpdate,{token:t}),t.getRawString()||null}let c=i?"".concat(this.path(),"/tokens/").concat(i):"".concat(this.path(),"/tokens"),u=i?{}:{organizationId:r},h=m.WU.create(c,u);return v.O.set({tokenId:s,tokenResolver:h}),h.then(t=>(l&&(_.Y.emit(_.U.TokenUpdate,{token:t}),t.jwt&&(this.lastActiveToken=t,_.Y.emit(_.U.SessionTokenResolved,null))),t.getRawString()||null))}get currentTask(){var t;let[e]=null!==(t=this.tasks)&&void 0!==t?t:[];return e}constructor(t){super(),(0,s._)(this,b),(0,o._)(this,"pathRoot","/client/sessions"),(0,o._)(this,"id",void 0),(0,o._)(this,"status",void 0),(0,o._)(this,"lastActiveAt",void 0),(0,o._)(this,"lastActiveToken",void 0),(0,o._)(this,"lastActiveOrganizationId",void 0),(0,o._)(this,"actor",void 0),(0,o._)(this,"user",void 0),(0,o._)(this,"publicUserData",void 0),(0,o._)(this,"factorVerificationAge",null),(0,o._)(this,"tasks",null),(0,o._)(this,"expireAt",void 0),(0,o._)(this,"abandonAt",void 0),(0,o._)(this,"createdAt",void 0),(0,o._)(this,"updatedAt",void 0),(0,o._)(this,"end",()=>(v.O.clear(),this._basePost({action:"end"}))),(0,o._)(this,"remove",()=>(v.O.clear(),this._basePost({action:"remove"}))),(0,o._)(this,"touch",()=>this._basePost({action:"touch",body:{active_organization_id:this.lastActiveOrganizationId}})),(0,o._)(this,"clearCache",()=>v.O.clear()),(0,o._)(this,"getToken",async t=>(0,u.X)(()=>this._getToken(t),{factor:1.55,initialDelay:3e3,maxDelayBetweenRetries:5e4,jitter:!1,shouldRetry:(t,e)=>!(0,c.ix)(t)&&e<=8})),(0,o._)(this,"checkAuthorization",t=>{var e,i,n,r,a,s;let o=((null===(e=this.user)||void 0===e?void 0:e.organizationMemberships)||[]).find(t=>t.organization.id===this.lastActiveOrganizationId);return(0,l.QL)({userId:null===(i=this.user)||void 0===i?void 0:i.id,factorVerificationAge:this.factorVerificationAge,orgId:null==o?void 0:o.id,orgRole:null==o?void 0:o.role,orgPermissions:null==o?void 0:o.permissions,features:(null===(r=this.lastActiveToken)||void 0===r?void 0:null===(n=r.jwt)||void 0===n?void 0:n.claims.fea)||"",plans:(null===(s=this.lastActiveToken)||void 0===s?void 0:null===(a=s.jwt)||void 0===a?void 0:a.claims.pla)||""})(t)}),(0,r._)(this,w,{writable:!0,value:t=>{t&&v.O.set({tokenId:(0,a._)(this,b,k).call(this),tokenResolver:Promise.resolve(t)})}}),(0,o._)(this,"startVerification",async t=>{var e;let{level:i}=t;return new y(null===(e=await m.i._fetch({method:"POST",path:"/client/sessions/".concat(this.id,"/verify"),body:{level:i}}))||void 0===e?void 0:e.response)}),(0,o._)(this,"prepareFirstFactorVerification",async t=>{var e;let i;switch(t.strategy){case"email_code":i={emailAddressId:t.emailAddressId};break;case"phone_code":i={phoneNumberId:t.phoneNumberId,default:t.default};break;case"passkey":i={};break;default:(0,f.Rz)("Session.prepareFirstFactorVerification",t.strategy)}return new y(null===(e=await m.i._fetch({method:"POST",path:"/client/sessions/".concat(this.id,"/verify/prepare_first_factor"),body:{...i,strategy:t.strategy}}))||void 0===e?void 0:e.response)}),(0,o._)(this,"attemptFirstFactorVerification",async t=>{var e;let i;return i="passkey"===t.strategy?{publicKeyCredential:JSON.stringify((0,p.zQ)(t.publicKeyCredential))}:{...t},new y(null===(e=await m.i._fetch({method:"POST",path:"/client/sessions/".concat(this.id,"/verify/attempt_first_factor"),body:{...i,strategy:t.strategy}}))||void 0===e?void 0:e.response)}),(0,o._)(this,"verifyWithPasskey",async()=>{let{nonce:t=null}=(await this.prepareFirstFactorVerification({strategy:"passkey"})).firstFactorVerification,e=S.clerk.__internal_isWebAuthnSupported||h.iW,i=S.clerk.__internal_getPublicCredentials||p.t1;if(!e())throw new c.RK("Passkeys are not supported",{code:"passkey_not_supported"});let n=t?(0,p.N7)(JSON.parse(t)):null;n||(0,f.$C)("get");let{publicKeyCredential:r,error:a}=await i({publicKeyOptions:n,conditionalUI:!1});if(!r)throw a;return this.attemptFirstFactorVerification({strategy:"passkey",publicKeyCredential:r})}),(0,o._)(this,"prepareSecondFactorVerification",async t=>{var e;return new y(null===(e=await m.i._fetch({method:"POST",path:"/client/sessions/".concat(this.id,"/verify/prepare_second_factor"),body:t}))||void 0===e?void 0:e.response)}),(0,o._)(this,"attemptSecondFactorVerification",async t=>{var e;return new y(null===(e=await m.i._fetch({method:"POST",path:"/client/sessions/".concat(this.id,"/verify/attempt_second_factor"),body:t}))||void 0===e?void 0:e.response)}),this.fromJSON(t),(0,n._)(this,w).call(this,this.lastActiveToken)}}function k(t,e){let i=void 0===e?this.lastActiveOrganizationId:e;return[this.id,t,i,this.updatedAt.getTime()].filter(Boolean).join("-")}},28076:function(t,e,i){"use strict";i.d(e,{A:()=>o});var n=i(17431),r=i(46630),a=i(73139);let s=t=>({id:t.id,deviceType:t.device_type,browserName:t.browser_name,browserVersion:t.browser_version,country:t.country,city:t.city,isMobile:t.is_mobile,ipAddress:t.ip_address});class o extends a.i{static retrieve(){var t;let e=null===(t=a.i.clerk.session)||void 0===t?void 0:t.id;return this.clerk.getFapiClient().request({method:"GET",path:"/me/sessions/active",sessionId:e}).then(t=>t.payload.map(t=>new o(t,"/me/sessions"))).catch(()=>[])}revoke(){return this._basePost({action:"revoke",body:{}})}fromJSON(t){var e;return t&&(this.id=t.id,this.status=t.status,this.expireAt=(0,r.V)(t.expire_at),this.abandonAt=(0,r.V)(t.abandon_at),this.lastActiveAt=(0,r.V)(t.last_active_at||void 0),this.latestActivity=s(null!==(e=t.latest_activity)&&void 0!==e?e:{}),this.actor=t.actor),this}constructor(t,e){super(),(0,n._)(this,"pathRoot",""),(0,n._)(this,"id",void 0),(0,n._)(this,"status",void 0),(0,n._)(this,"abandonAt",void 0),(0,n._)(this,"expireAt",void 0),(0,n._)(this,"lastActiveAt",void 0),(0,n._)(this,"latestActivity",void 0),(0,n._)(this,"actor",void 0),this.pathRoot=e,this.fromJSON(t)}}},53898:function(t,e,i){"use strict";i.d(e,{c:()=>f});var n=i(17431);i(50725),i(87945),i(56113);var r=i(73531),a=i(96111),s=i(65027),o=i(32208),l=i(26917),c=i(73837),u=i(93367),h=i(30529),d=i(24152),p=i(73139);class f extends p.i{fromJSON(t){return t&&(this.id=t.id,this.status=t.status,this.supportedIdentifiers=t.supported_identifiers,this.identifier=t.identifier,this.supportedFirstFactors=(0,s.aw)(t.supported_first_factors),this.supportedSecondFactors=(0,s.aw)(t.supported_second_factors),this.firstFactorVerification=new p.GX(t.first_factor_verification),this.secondFactorVerification=new p.GX(t.second_factor_verification),this.createdSessionId=t.created_session_id,this.userData=new p.mt(t.user_data)),this}__internal_toSnapshot(){return{object:"sign_in",id:this.id||"",status:this.status||null,supported_identifiers:this.supportedIdentifiers,supported_first_factors:(0,s.zb)(this.supportedFirstFactors),supported_second_factors:(0,s.zb)(this.supportedSecondFactors),first_factor_verification:this.firstFactorVerification.__internal_toSnapshot(),second_factor_verification:this.secondFactorVerification.__internal_toSnapshot(),identifier:this.identifier,created_session_id:this.createdSessionId,user_data:this.userData.__internal_toSnapshot()}}constructor(t=null){super(),(0,n._)(this,"pathRoot","/client/sign_ins"),(0,n._)(this,"id",void 0),(0,n._)(this,"status",null),(0,n._)(this,"supportedIdentifiers",[]),(0,n._)(this,"supportedFirstFactors",[]),(0,n._)(this,"supportedSecondFactors",null),(0,n._)(this,"firstFactorVerification",new p.GX(null)),(0,n._)(this,"secondFactorVerification",new p.GX(null)),(0,n._)(this,"identifier",null),(0,n._)(this,"createdSessionId",null),(0,n._)(this,"userData",new p.mt(null)),(0,n._)(this,"create",t=>this._basePost({path:this.pathRoot,body:t})),(0,n._)(this,"resetPassword",t=>this._basePost({body:t,action:"reset_password"})),(0,n._)(this,"prepareFirstFactor",t=>{let e;switch(t.strategy){case"passkey":e={};break;case"email_link":e={emailAddressId:t.emailAddressId,redirectUrl:t.redirectUrl};break;case"email_code":case"reset_password_email_code":e={emailAddressId:t.emailAddressId};break;case"phone_code":e={phoneNumberId:t.phoneNumberId,default:t.default,channel:t.channel};break;case"web3_metamask_signature":case"web3_coinbase_wallet_signature":case"web3_okx_wallet_signature":e={web3WalletId:t.web3WalletId};break;case"reset_password_phone_code":e={phoneNumberId:t.phoneNumberId};break;case"saml":e={redirectUrl:t.redirectUrl,actionCompleteRedirectUrl:t.actionCompleteRedirectUrl};break;case"enterprise_sso":e={redirectUrl:t.redirectUrl,actionCompleteRedirectUrl:t.actionCompleteRedirectUrl,oidcPrompt:t.oidcPrompt};break;default:(0,d.Rz)("SignIn.prepareFirstFactor",t.strategy)}return this._basePost({body:{...e,strategy:t.strategy},action:"prepare_first_factor"})}),(0,n._)(this,"attemptFirstFactor",t=>{let e;return e="passkey"===t.strategy?{publicKeyCredential:JSON.stringify((0,u.zQ)(t.publicKeyCredential))}:{...t},this._basePost({body:{...e,strategy:t.strategy},action:"attempt_first_factor"})}),(0,n._)(this,"createEmailLinkFlow",()=>{let{run:t,stop:e}=(0,a.W)();return{startEmailLinkFlow:async i=>{let{emailAddressId:n,redirectUrl:r}=i;return this.id||(0,d.C)("SignIn"),await this.prepareFirstFactor({strategy:"email_link",emailAddressId:n,redirectUrl:r}),new Promise((i,n)=>{t(()=>this.reload().then(t=>{let n=t.firstFactorVerification.status;("verified"===n||"expired"===n)&&(e(),i(t))}).catch(t=>{e(),n(t)}))})},cancelEmailLinkFlow:e}}),(0,n._)(this,"prepareSecondFactor",t=>this._basePost({body:t,action:"prepare_second_factor"})),(0,n._)(this,"attemptSecondFactor",t=>this._basePost({body:t,action:"attempt_second_factor"})),(0,n._)(this,"authenticateWithRedirectOrPopup",async(t,e)=>{let{strategy:i,redirectUrl:n,redirectUrlComplete:r,identifier:a,oidcPrompt:s}=t||{},{firstFactorVerification:o}=("saml"===i||"enterprise_sso"===i)&&this.id?await this.prepareFirstFactor({strategy:i,redirectUrl:f.clerk.buildUrlWithAuth(n),actionCompleteRedirectUrl:r,oidcPrompt:s}):await this.create({strategy:i,identifier:a,redirectUrl:f.clerk.buildUrlWithAuth(n),actionCompleteRedirectUrl:r,oidcPrompt:s}),{status:l,externalVerificationRedirectURL:c}=o;"unverified"===l&&c?e(c):(0,d.Ws)(l,f.fapiClient.buildEmailAddress("support"))}),(0,n._)(this,"authenticateWithRedirect",async t=>this.authenticateWithRedirectOrPopup(t,l.T7)),(0,n._)(this,"authenticateWithPopup",async t=>{let{popup:e}=t||{};return e||(0,d.WC)("popup"),(0,c.G)(f.clerk,"signIn",this.authenticateWithRedirectOrPopup,t,t=>{e.location.href=t.toString()})}),(0,n._)(this,"authenticateWithWeb3",async t=>{var e;let i;let{identifier:n,generateSignature:r,strategy:a="web3_metamask_signature"}=t||{},s=a.replace("web3_","").replace("_signature","");"function"!=typeof r&&(0,d.WC)("generateSignature"),await this.create({identifier:n});let o=null===(e=this.supportedFirstFactors)||void 0===e?void 0:e.find(t=>t.strategy===a);o||(0,d.lb)("SignIn"),await this.prepareFirstFactor(o);let{message:l}=this.firstFactorVerification;l||(0,d.lb)("SignIn");try{i=await r({identifier:n,nonce:l,provider:s})}catch(t){if("coinbase_wallet"===s&&4001===t.code)i=await r({identifier:n,nonce:l,provider:s});else throw t}return this.attemptFirstFactor({signature:i,strategy:a})}),(0,n._)(this,"authenticateWithMetamask",async()=>{let t=await (0,l.M8)();return this.authenticateWithWeb3({identifier:t,generateSignature:l.wO,strategy:"web3_metamask_signature"})}),(0,n._)(this,"authenticateWithCoinbaseWallet",async()=>{let t=await (0,l.$0)();return this.authenticateWithWeb3({identifier:t,generateSignature:l.dR,strategy:"web3_coinbase_wallet_signature"})}),(0,n._)(this,"authenticateWithOKXWallet",async()=>{let t=await (0,l.ZE)();return this.authenticateWithWeb3({identifier:t,generateSignature:l.C3,strategy:"web3_okx_wallet_signature"})}),(0,n._)(this,"authenticateWithPasskey",async t=>{let{flow:e}=t||{},i=f.clerk.__internal_isWebAuthnSupported||o.iW,n=f.clerk.__internal_getPublicCredentials||u.t1,a=f.clerk.__internal_isWebAuthnAutofillSupported||o.h_;if(!i())throw new r.RK("Passkeys are not supported",{code:"passkey_not_supported"});if("autofill"===e||"discoverable"===e)await this.create({strategy:"passkey"});else{let t=this.supportedFirstFactors.find(t=>"passkey"===t.strategy);t||(0,d.ep)(),await this.prepareFirstFactor(t)}let{nonce:s}=this.firstFactorVerification,l=s?(0,u.N7)(JSON.parse(s)):null;l||(0,d.$C)("get");let c=!1;"autofill"===e&&(c=await a());let{publicKeyCredential:h,error:p}=await n({publicKeyOptions:l,conditionalUI:c});if(!h)throw p;return this.attemptFirstFactor({publicKeyCredential:h,strategy:"passkey"})}),(0,n._)(this,"validatePassword",(t,e)=>{var i,n;if(null===(i=f.clerk.__unstable__environment)||void 0===i?void 0:i.userSettings.passwordSettings)return(0,h.z)({...null===(n=f.clerk.__unstable__environment)||void 0===n?void 0:n.userSettings.passwordSettings,validatePassword:!0})(t,e)}),this.fromJSON(t)}}},90240:function(t,e,i){"use strict";i.d(e,{M:()=>p});var n=i(17431);i(50725),i(87945),i(56113),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223);var r=i(73531),a=i(96111),s=i(26917),o=i(73837),l=i(97976),c=i(30529),u=i(26310),h=i(24152),d=i(73139);class p extends d.i{fromJSON(t){return t&&(this.id=t.id,this.status=t.status,this.requiredFields=t.required_fields,this.optionalFields=t.optional_fields,this.missingFields=t.missing_fields,this.unverifiedFields=t.unverified_fields,this.verifications=new d.EJ(t.verifications),this.username=t.username,this.firstName=t.first_name,this.lastName=t.last_name,this.emailAddress=t.email_address,this.phoneNumber=t.phone_number,this.hasPassword=t.has_password,this.unsafeMetadata=t.unsafe_metadata,this.createdSessionId=t.created_session_id,this.createdUserId=t.created_user_id,this.abandonAt=t.abandon_at,this.web3wallet=t.web3_wallet,this.legalAcceptedAt=t.legal_accepted_at),this}__internal_toSnapshot(){var t;return{object:"sign_up",id:this.id||"",status:this.status||null,required_fields:this.requiredFields,optional_fields:this.optionalFields,missing_fields:this.missingFields,unverified_fields:this.unverifiedFields,verifications:this.verifications.__internal_toSnapshot(),username:this.username,first_name:this.firstName,last_name:this.lastName,email_address:this.emailAddress,phone_number:this.phoneNumber,has_password:this.hasPassword,unsafe_metadata:this.unsafeMetadata,created_session_id:this.createdSessionId,created_user_id:this.createdUserId,abandon_at:this.abandonAt,web3_wallet:this.web3wallet,legal_accepted_at:this.legalAcceptedAt,external_account:this.externalAccount,external_account_strategy:null===(t=this.externalAccount)||void 0===t?void 0:t.strategy}}clientBypass(){var t;return null===(t=p.clerk.client)||void 0===t?void 0:t.captchaBypass}shouldBypassCaptchaForAttempt(t){if(!t.strategy)return!1;let e=p.clerk.__unstable__environment.displayConfig.captchaOauthBypass;return!!(e.some(e=>e===t.strategy)||t.transfer&&e.some(t=>t===p.clerk.client.signIn.firstFactorVerification.strategy))}constructor(t=null){super(),(0,n._)(this,"pathRoot","/client/sign_ups"),(0,n._)(this,"id",void 0),(0,n._)(this,"status",null),(0,n._)(this,"requiredFields",[]),(0,n._)(this,"optionalFields",[]),(0,n._)(this,"missingFields",[]),(0,n._)(this,"unverifiedFields",[]),(0,n._)(this,"verifications",new d.EJ(null)),(0,n._)(this,"username",null),(0,n._)(this,"firstName",null),(0,n._)(this,"lastName",null),(0,n._)(this,"emailAddress",null),(0,n._)(this,"phoneNumber",null),(0,n._)(this,"web3wallet",null),(0,n._)(this,"externalAccount",void 0),(0,n._)(this,"hasPassword",!1),(0,n._)(this,"unsafeMetadata",{}),(0,n._)(this,"createdSessionId",null),(0,n._)(this,"createdUserId",null),(0,n._)(this,"abandonAt",null),(0,n._)(this,"legalAcceptedAt",null),(0,n._)(this,"create",async t=>{let e=t;if(!this.clientBypass()&&!this.shouldBypassCaptchaForAttempt(e)){let t=new l.E(p.clerk),i=await t.managedOrInvisible({action:"signup"});if(!i)throw new d.w$("",{code:"captcha_unavailable"});e={...e,...i}}if(e.transfer&&this.shouldBypassCaptchaForAttempt(e)){var i;e.strategy=null===(i=p.clerk.client)||void 0===i?void 0:i.signIn.firstFactorVerification.strategy}return this._basePost({path:this.pathRoot,body:(0,u.q)(e)})}),(0,n._)(this,"prepareVerification",t=>this._basePost({body:t,action:"prepare_verification"})),(0,n._)(this,"attemptVerification",t=>this._basePost({body:t,action:"attempt_verification"})),(0,n._)(this,"prepareEmailAddressVerification",t=>this.prepareVerification(t||{strategy:"email_code"})),(0,n._)(this,"attemptEmailAddressVerification",t=>this.attemptVerification({...t,strategy:"email_code"})),(0,n._)(this,"createEmailLinkFlow",()=>{let{run:t,stop:e}=(0,a.W)();return{startEmailLinkFlow:async i=>{let{redirectUrl:n}=i;return this.id||(0,h.C)("SignUp"),await this.prepareEmailAddressVerification({strategy:"email_link",redirectUrl:n}),new Promise((i,n)=>{t(()=>this.reload().then(t=>{let n=t.verifications.emailAddress.status;("verified"===n||"expired"===n)&&(e(),i(t))}).catch(t=>{e(),n(t)}))})},cancelEmailLinkFlow:e}}),(0,n._)(this,"preparePhoneNumberVerification",t=>this.prepareVerification(t||{strategy:"phone_code"})),(0,n._)(this,"attemptPhoneNumberVerification",t=>this.attemptVerification({...t,strategy:"phone_code"})),(0,n._)(this,"prepareWeb3WalletVerification",t=>this.prepareVerification({strategy:"web3_metamask_signature",...t})),(0,n._)(this,"attemptWeb3WalletVerification",async t=>{let{signature:e,strategy:i="web3_metamask_signature"}=t;return this.attemptVerification({signature:e,strategy:i})}),(0,n._)(this,"authenticateWithWeb3",async t=>{let e;let{generateSignature:i,identifier:n,unsafeMetadata:r,strategy:a="web3_metamask_signature",legalAccepted:s}=t||{},o=a.replace("web3_","").replace("_signature","");"function"!=typeof i&&(0,h.WC)("generateSignature");let l=n||this.web3wallet;await this.create({web3Wallet:l,unsafeMetadata:r,legalAccepted:s}),await this.prepareWeb3WalletVerification({strategy:a});let{message:c}=this.verifications.web3Wallet;c||(0,h.lb)("SignUp");try{e=await i({identifier:n,nonce:c,provider:o})}catch(t){if("coinbase_wallet"===o&&4001===t.code)e=await i({identifier:n,nonce:c,provider:o});else throw t}return this.attemptWeb3WalletVerification({signature:e,strategy:a})}),(0,n._)(this,"authenticateWithMetamask",async t=>{let e=await (0,s.M8)();return this.authenticateWithWeb3({identifier:e,generateSignature:s.wO,unsafeMetadata:null==t?void 0:t.unsafeMetadata,strategy:"web3_metamask_signature",legalAccepted:null==t?void 0:t.legalAccepted})}),(0,n._)(this,"authenticateWithCoinbaseWallet",async t=>{let e=await (0,s.$0)();return this.authenticateWithWeb3({identifier:e,generateSignature:s.dR,unsafeMetadata:null==t?void 0:t.unsafeMetadata,strategy:"web3_coinbase_wallet_signature",legalAccepted:null==t?void 0:t.legalAccepted})}),(0,n._)(this,"authenticateWithOKXWallet",async t=>{let e=await (0,s.ZE)();return this.authenticateWithWeb3({identifier:e,generateSignature:s.C3,unsafeMetadata:null==t?void 0:t.unsafeMetadata,strategy:"web3_okx_wallet_signature",legalAccepted:null==t?void 0:t.legalAccepted})}),(0,n._)(this,"authenticateWithRedirectOrPopup",async(t,e)=>{let{redirectUrl:i,redirectUrlComplete:n,strategy:a,continueSignUp:s=!1,unsafeMetadata:o,emailAddress:l,legalAccepted:c,oidcPrompt:u}=t,d=()=>{let t={strategy:a,redirectUrl:p.clerk.buildUrlWithAuth(i),actionCompleteRedirectUrl:n,unsafeMetadata:o,emailAddress:l,legalAccepted:c,oidcPrompt:u};return s&&this.id?this.update(t):this.create(t)},{verifications:f}=await d().catch(async t=>{if((0,r.kD)(t)&&(0,r.pG)(t))return await p.clerk.__unstable__environment.reload(),d();throw t}),{externalAccount:_}=f,{status:v,externalVerificationRedirectURL:m}=_;"unverified"===v&&m?e(m):(0,h.Ws)(v,p.fapiClient.buildEmailAddress("support"))}),(0,n._)(this,"authenticateWithRedirect",async t=>this.authenticateWithRedirectOrPopup(t,s.T7)),(0,n._)(this,"authenticateWithPopup",async t=>{let{popup:e}=t||{};return e||(0,h.WC)("popup"),(0,o.G)(p.clerk,"signUp",this.authenticateWithRedirectOrPopup,t,t=>{e.location.href=t instanceof URL?t.toString():t})}),(0,n._)(this,"update",t=>this._basePatch({body:(0,u.q)(t)})),(0,n._)(this,"upsert",t=>this.id?this.update(t):this.create(t)),(0,n._)(this,"validatePassword",(t,e)=>{var i,n;if(null===(i=p.clerk.__unstable__environment)||void 0===i?void 0:i.userSettings.passwordSettings)return(0,c.z)({...null===(n=p.clerk.__unstable__environment)||void 0===n?void 0:n.userSettings.passwordSettings,validatePassword:!0})(t,e)}),this.fromJSON(t)}}},20523:function(t,e,i){"use strict";i.d(e,{W:()=>s});var n=i(17431);i(50725);var r=i(26917),a=i(73139);class s extends a.i{static async create(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new s(await a.i._fetch({path:t,method:"POST",body:e}),t)}fromJSON(t){return t&&(this.jwt=(0,r.Jx)(t.jwt)),this}__internal_toSnapshot(){return{object:"token",id:this.id||"",jwt:this.getRawString()}}constructor(t,e){super(),(0,n._)(this,"pathRoot","tokens"),(0,n._)(this,"jwt",void 0),(0,n._)(this,"getRawString",()=>{var t;return(null===(t=this.jwt)||void 0===t?void 0:t.claims.__raw)||""}),e&&(this.pathRoot=e),(null==t?void 0:t.jwt)&&(this.jwt=(0,r.Jx)(t.jwt))}}},49613:function(t,e,i){"use strict";i.d(e,{n:()=>h});var n=i(17431),r=i(46630),a=i(26310),s=i(95878),o=i(50005),l=i(4843),c=i(73139);class u extends c.i{fromJSON(t){return t&&(this.id=t.id,this.codes=t.codes,this.updatedAt=(0,r.V)(t.updated_at),this.createdAt=(0,r.V)(t.created_at)),this}constructor(t){super(),(0,n._)(this,"pathRoot","/me"),(0,n._)(this,"id",void 0),(0,n._)(this,"codes",[]),(0,n._)(this,"updatedAt",null),(0,n._)(this,"createdAt",null),this.fromJSON(t)}}class h extends c.i{static isUserResource(t){return!!t&&t instanceof h}path(){return this.pathRoot}get verifiedExternalAccounts(){return this.externalAccounts.filter(t=>{var e;return(null===(e=t.verification)||void 0===e?void 0:e.status)=="verified"})}get unverifiedExternalAccounts(){return this.externalAccounts.filter(t=>{var e;return(null===(e=t.verification)||void 0===e?void 0:e.status)!="verified"})}get verifiedWeb3Wallets(){return this.web3Wallets.filter(t=>{var e;return(null===(e=t.verification)||void 0===e?void 0:e.status)=="verified"})}get hasVerifiedEmailAddress(){return this.emailAddresses.filter(t=>"verified"===t.verification.status).length>0}get hasVerifiedPhoneNumber(){return this.phoneNumbers.filter(t=>"verified"===t.verification.status).length>0}fromJSON(t){return t&&(this.id=t.id,this.externalId=t.external_id||null,this.firstName=t.first_name||null,this.lastName=t.last_name||null,(this.firstName||this.lastName)&&(this.fullName=(0,s.Pp)({firstName:this.firstName,lastName:this.lastName})),this.imageUrl=t.image_url||"",this.hasImage=t.has_image||!1,this.username=t.username||null,this.passwordEnabled=t.password_enabled||!1,this.emailAddresses=(t.email_addresses||[]).map(t=>new c.b5(t,this.path()+"/email_addresses")),this.primaryEmailAddressId=t.primary_email_address_id||null,this.primaryEmailAddress=this.emailAddresses.find(t=>{let{id:e}=t;return e===this.primaryEmailAddressId})||null,this.phoneNumbers=(t.phone_numbers||[]).map(t=>new c.Bh(t,this.path()+"/phone_numbers")),this.primaryPhoneNumberId=t.primary_phone_number_id||null,this.primaryPhoneNumber=this.phoneNumbers.find(t=>{let{id:e}=t;return e===this.primaryPhoneNumberId})||null,this.web3Wallets=(t.web3_wallets||[]).map(t=>new c.q2(t,this.path()+"/web3_wallets")),this.primaryWeb3WalletId=t.primary_web3_wallet_id||null,this.primaryWeb3Wallet=this.web3Wallets.find(t=>{let{id:e}=t;return e===this.primaryWeb3WalletId})||null,this.externalAccounts=(t.external_accounts||[]).map(t=>new c.z(t,this.path()+"/external_accounts")),this.passkeys=(t.passkeys||[]).map(t=>new c.Vk(t)),this.organizationMemberships=(t.organization_memberships||[]).map(t=>new c.fr(t)),this.samlAccounts=(t.saml_accounts||[]).map(t=>new c.gt(t,this.path()+"/saml_accounts")),this.enterpriseAccounts=(t.enterprise_accounts||[]).map(t=>new c.Ye(t,this.path()+"/enterprise_accounts")),this.publicMetadata=t.public_metadata||{},this.unsafeMetadata=t.unsafe_metadata||{},this.totpEnabled=t.totp_enabled||!1,this.backupCodeEnabled=t.backup_code_enabled||!1,this.twoFactorEnabled=t.two_factor_enabled||!1,this.createOrganizationEnabled=t.create_organization_enabled||!1,this.createOrganizationsLimit=t.create_organizations_limit||null,this.deleteSelfEnabled=t.delete_self_enabled||!1,t.last_sign_in_at&&(this.lastSignInAt=(0,r.V)(t.last_sign_in_at)),t.legal_accepted_at&&(this.legalAcceptedAt=(0,r.V)(t.legal_accepted_at)),this.updatedAt=(0,r.V)(t.updated_at||void 0),this.createdAt=(0,r.V)(t.created_at||void 0)),this}__internal_toSnapshot(){var t,e,i,n;return{object:"user",id:this.id,external_id:this.externalId,first_name:this.firstName,last_name:this.lastName,username:this.username,public_metadata:this.publicMetadata,unsafe_metadata:this.unsafeMetadata,image_url:this.imageUrl,has_image:this.hasImage,email_addresses:this.emailAddresses.map(t=>t.__internal_toSnapshot()),phone_numbers:this.phoneNumbers.map(t=>t.__internal_toSnapshot()),web3_wallets:this.web3Wallets.map(t=>t.__internal_toSnapshot()),external_accounts:this.externalAccounts.map(t=>t.__internal_toSnapshot()),passkeys:this.passkeys.map(t=>t.__internal_toSnapshot()),organization_memberships:this.organizationMemberships.map(t=>t.__internal_toSnapshot()),saml_accounts:this.samlAccounts.map(t=>t.__internal_toSnapshot()),enterprise_accounts:this.enterpriseAccounts.map(t=>t.__internal_toSnapshot()),totp_enabled:this.totpEnabled,backup_code_enabled:this.backupCodeEnabled,two_factor_enabled:this.twoFactorEnabled,create_organization_enabled:this.createOrganizationEnabled,create_organizations_limit:this.createOrganizationsLimit,delete_self_enabled:this.deleteSelfEnabled,primary_email_address_id:this.primaryEmailAddressId,primary_phone_number_id:this.primaryPhoneNumberId,primary_web3_wallet_id:this.primaryWeb3WalletId,password_enabled:this.passwordEnabled,profile_image_id:this.imageUrl,last_sign_in_at:(null===(t=this.lastSignInAt)||void 0===t?void 0:t.getTime())||null,legal_accepted_at:(null===(e=this.legalAcceptedAt)||void 0===e?void 0:e.getTime())||null,updated_at:(null===(i=this.updatedAt)||void 0===i?void 0:i.getTime())||null,created_at:(null===(n=this.createdAt)||void 0===n?void 0:n.getTime())||null}}constructor(t){super(),(0,n._)(this,"pathRoot","/me"),(0,n._)(this,"id",""),(0,n._)(this,"externalId",null),(0,n._)(this,"username",null),(0,n._)(this,"emailAddresses",[]),(0,n._)(this,"phoneNumbers",[]),(0,n._)(this,"web3Wallets",[]),(0,n._)(this,"externalAccounts",[]),(0,n._)(this,"enterpriseAccounts",[]),(0,n._)(this,"passkeys",[]),(0,n._)(this,"samlAccounts",[]),(0,n._)(this,"organizationMemberships",[]),(0,n._)(this,"passwordEnabled",!1),(0,n._)(this,"firstName",null),(0,n._)(this,"lastName",null),(0,n._)(this,"fullName",null),(0,n._)(this,"primaryEmailAddressId",null),(0,n._)(this,"primaryEmailAddress",null),(0,n._)(this,"primaryPhoneNumberId",null),(0,n._)(this,"primaryPhoneNumber",null),(0,n._)(this,"primaryWeb3WalletId",null),(0,n._)(this,"primaryWeb3Wallet",null),(0,n._)(this,"imageUrl",""),(0,n._)(this,"hasImage",!1),(0,n._)(this,"twoFactorEnabled",!1),(0,n._)(this,"totpEnabled",!1),(0,n._)(this,"backupCodeEnabled",!1),(0,n._)(this,"publicMetadata",{}),(0,n._)(this,"unsafeMetadata",{}),(0,n._)(this,"createOrganizationEnabled",!1),(0,n._)(this,"createOrganizationsLimit",null),(0,n._)(this,"deleteSelfEnabled",!1),(0,n._)(this,"lastSignInAt",null),(0,n._)(this,"legalAcceptedAt",null),(0,n._)(this,"updatedAt",null),(0,n._)(this,"createdAt",null),(0,n._)(this,"cachedSessionsWithActivities",null),(0,n._)(this,"isPrimaryIdentification",t=>{switch(t.constructor){case c.b5:return this.primaryEmailAddressId===t.id;case c.Bh:return this.primaryPhoneNumberId===t.id;case c.q2:return this.primaryWeb3WalletId===t.id;default:return!1}}),(0,n._)(this,"createEmailAddress",t=>{let{email:e}=t||{};return new c.b5({email_address:e},this.path()+"/email_addresses/").create()}),(0,n._)(this,"createPasskey",()=>c.Vk.registerPasskey()),(0,n._)(this,"createPhoneNumber",t=>{let{phoneNumber:e}=t||{};return new c.Bh({phone_number:e},this.path()+"/phone_numbers/").create()}),(0,n._)(this,"createWeb3Wallet",t=>{let{web3Wallet:e}=t||{};return new c.q2({web3_wallet:e},this.path()+"/web3_wallets/").create()}),(0,n._)(this,"createExternalAccount",async t=>{var e;let{strategy:i,redirectUrl:n,additionalScopes:r}=t||{},a=null===(e=await c.i._fetch({path:"/me/external_accounts",method:"POST",body:{strategy:i,redirect_url:n,additional_scope:r}}))||void 0===e?void 0:e.response;return new c.z(a,this.path()+"/external_accounts")}),(0,n._)(this,"createTOTP",async()=>{var t;let e=null===(t=await c.i._fetch({path:"/me/totp",method:"POST"}))||void 0===t?void 0:t.response;return new c.GF(e)}),(0,n._)(this,"verifyTOTP",async t=>{var e;let{code:i}=t,n=null===(e=await c.i._fetch({path:"/me/totp/attempt_verification",method:"POST",body:{code:i}}))||void 0===e?void 0:e.response;return new c.GF(n)}),(0,n._)(this,"disableTOTP",async()=>{var t;let e=null===(t=await c.i._fetch({path:"/me/totp",method:"DELETE"}))||void 0===t?void 0:t.response;return new c.FF(e)}),(0,n._)(this,"createBackupCode",async()=>{var t;return new u(null===(t=await c.i._fetch({path:this.path()+"/backup_codes/",method:"POST"}))||void 0===t?void 0:t.response)}),(0,n._)(this,"update",t=>this._basePatch({body:(0,a.q)(t)})),(0,n._)(this,"updatePassword",t=>this._basePost({body:t,path:"".concat(this.path(),"/change_password")})),(0,n._)(this,"removePassword",t=>this._basePost({body:t,path:"".concat(this.path(),"/remove_password")})),(0,n._)(this,"delete",()=>this._baseDelete({path:"/me"}).then(t=>(o.Y.emit(o.U.UserSignOut,null),t))),(0,n._)(this,"getSessions",async()=>{if(this.cachedSessionsWithActivities)return this.cachedSessionsWithActivities;let t=await c.Ax.retrieve();return this.cachedSessionsWithActivities=t,t}),(0,n._)(this,"setProfileImage",t=>{let{file:e}=t||{};return null===e?c.Ee.delete("".concat(this.path(),"/profile_image")):c.Ee.create("".concat(this.path(),"/profile_image"),{file:e})}),(0,n._)(this,"getOrganizationInvitations",t=>c.ff.retrieve(t)),(0,n._)(this,"getOrganizationSuggestions",t=>c.wJ.retrieve(t)),(0,n._)(this,"getOrganizationMemberships",t=>c.fr.retrieve(t)),(0,n._)(this,"leaveOrganization",async t=>{var e;let i=null===(e=await c.i._fetch({path:"".concat(this.path(),"/organization_memberships/").concat(t),method:"DELETE"}))||void 0===e?void 0:e.response;return new c.FF(i)}),(0,n._)(this,"initializePaymentSource",t=>(0,l._G)(t)),(0,n._)(this,"addPaymentSource",t=>(0,l.C9)(t)),(0,n._)(this,"getPaymentSources",t=>(0,l.QE)(t)),this.fromJSON(t)}}},69596:function(t,e,i){"use strict";i.d(e,{EJ:()=>u,GX:()=>l,P$:()=>c});var n=i(17431);i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223);var r=i(73531),a=i(46630),s=i(93367),o=i(73139);class l extends o.i{fromJSON(t){return t&&(this.status=t.status,this.verifiedAtClient=t.verified_at_client,this.strategy=t.strategy,this.nonce=t.nonce||null,this.message=t.message||null,t.external_verification_redirect_url?this.externalVerificationRedirectURL=new URL(t.external_verification_redirect_url):this.externalVerificationRedirectURL=null,this.attempts=t.attempts,this.expireAt=(0,a.V)(t.expire_at||void 0),this.error=t.error?(0,r.nU)(t.error):null,this.channel=t.channel||void 0),this}__internal_toSnapshot(){var t,e;return{object:"verification",id:this.id||"",status:this.status,strategy:this.strategy,nonce:this.nonce,message:this.message,external_verification_redirect_url:(null===(t=this.externalVerificationRedirectURL)||void 0===t?void 0:t.toString())||null,attempts:this.attempts,expire_at:(null===(e=this.expireAt)||void 0===e?void 0:e.getTime())||null,error:(0,r.LT)(this.error),verified_at_client:this.verifiedAtClient}}constructor(t){super(),(0,n._)(this,"pathRoot",""),(0,n._)(this,"status",null),(0,n._)(this,"strategy",null),(0,n._)(this,"nonce",null),(0,n._)(this,"message",null),(0,n._)(this,"externalVerificationRedirectURL",null),(0,n._)(this,"attempts",null),(0,n._)(this,"expireAt",null),(0,n._)(this,"error",null),(0,n._)(this,"verifiedAtClient",null),(0,n._)(this,"channel",void 0),(0,n._)(this,"verifiedFromTheSameClient",()=>{var t,e;return this.verifiedAtClient===(null===(e=o.i.clerk)||void 0===e?void 0:null===(t=e.client)||void 0===t?void 0:t.id)}),this.fromJSON(t)}}class c extends l{fromJSON(t){return super.fromJSON(t),(null==t?void 0:t.nonce)&&(this.publicKey=(0,s.GH)(JSON.parse(t.nonce))),this}constructor(t){super(t),(0,n._)(this,"publicKey",null),this.fromJSON(t)}}class u{__internal_toSnapshot(){return{email_address:this.emailAddress.__internal_toSnapshot(),phone_number:this.phoneNumber.__internal_toSnapshot(),web3_wallet:this.web3Wallet.__internal_toSnapshot(),external_account:this.externalAccount.__internal_toSnapshot()}}constructor(t){(0,n._)(this,"emailAddress",void 0),(0,n._)(this,"phoneNumber",void 0),(0,n._)(this,"web3Wallet",void 0),(0,n._)(this,"externalAccount",void 0),t?(this.emailAddress=new h(t.email_address),this.phoneNumber=new h(t.phone_number),this.web3Wallet=new h(t.web3_wallet),this.externalAccount=new l(t.external_account)):(this.emailAddress=new h(null),this.phoneNumber=new h(null),this.web3Wallet=new h(null),this.externalAccount=new l(null))}}class h extends l{__internal_toSnapshot(){return{...super.__internal_toSnapshot(),next_action:this.nextAction,supported_strategies:this.supportedStrategies}}constructor(t){super(t),(0,n._)(this,"nextAction",void 0),(0,n._)(this,"supportedStrategies",void 0),t?(this.nextAction=t.next_action,this.supportedStrategies=t.supported_strategies):(this.nextAction="",this.supportedStrategies=[])}}},32687:function(t,e,i){"use strict";i.d(e,{y:()=>s});var n=i(17431);i(50725);var r=i(46630),a=i(73139);class s extends a.i{fromJSON(t){return t&&(this.id=t.id,this.updatedAt=(0,r.V)(t.updated_at),this.createdAt=(0,r.V)(t.created_at)),this}static async join(t){var e;return new s(null===(e=await a.i._fetch({path:"/waitlist",method:"POST",body:t}))||void 0===e?void 0:e.response)}constructor(t){super(),(0,n._)(this,"pathRoot","/waitlist"),(0,n._)(this,"id",""),(0,n._)(this,"updatedAt",null),(0,n._)(this,"createdAt",null),this.fromJSON(t)}}},34737:function(t,e,i){"use strict";i.d(e,{q:()=>a});var n=i(17431),r=i(73139);class a extends r.i{create(){return this._basePost({body:{web3_wallet:this.web3Wallet}})}destroy(){return this._baseDelete()}toString(){return this.web3Wallet}fromJSON(t){return t&&(this.id=t.id,this.web3Wallet=t.web3_wallet,this.verification=new r.GX(t.verification)),this}__internal_toSnapshot(){return{object:"web3_wallet",id:this.id,web3_wallet:this.web3Wallet,verification:this.verification.__internal_toSnapshot()}}constructor(t,e){super(),(0,n._)(this,"id",void 0),(0,n._)(this,"web3Wallet",""),(0,n._)(this,"verification",void 0),(0,n._)(this,"prepareVerification",t=>this._basePost({action:"prepare_verification",body:{...t}})),(0,n._)(this,"attemptVerification",t=>{let{signature:e}=t;return this._basePost({action:"attempt_verification",body:{signature:e}})}),this.pathRoot=e,this.fromJSON(t)}}},73139:function(t,e,i){"use strict";i.d(e,{Ax:()=>X.A,Bh:()=>R.B,qN:()=>tl,wg:()=>g,GF:()=>tn,N:()=>O.N,Ye:()=>P,$h:()=>j,P$:()=>E.P$,z:()=>U.z,KU:()=>c.K,Vk:()=>Z,ff:()=>ta,pm:()=>p,j3:()=>_,wJ:()=>B,n5:()=>tr.n,PG:()=>s,kD:()=>O.kD,gO:()=>O.gO,sZ:()=>O.sZ,FF:()=>S,ZC:()=>O.ZC,cp:()=>F,zv:()=>y,uX:()=>O.uX,b5:()=>A.b,gt:()=>$.g,w$:()=>O.w$,Ee:()=>T.E,Tg:()=>l.T,YN:()=>I.Y,gU:()=>b,mM:()=>o,Dl:()=>d,lR:()=>k.l,fr:()=>K,yk:()=>to.y,Cs:()=>w,cL:()=>Q.c,i:()=>n.i,q2:()=>ts.q,EJ:()=>E.EJ,Mo:()=>te.M,mt:()=>tt,z_:()=>J.z,GX:()=>E.GX,Pm:()=>m,VO:()=>Y,WU:()=>ti.W,qA:()=>x.q});var n=i(28069),r=i(17431);i(65223),i(56711);let a={enabled:!1,first_factors:[],name:"phone_number",required:!1,second_factors:[],used_for_first_factor:!1,used_for_second_factor:!1,verifications:[],verify_at_sign_up:!1};class s extends n.i{get authenticatableSocialStrategies(){return this.social?Object.entries(this.social).filter(t=>{let[,e]=t;return e.enabled&&e.authenticatable}).map(t=>{let[,e]=t;return e.strategy}).sort():[]}get enabledFirstFactorIdentifiers(){return this.attributes?Object.entries(this.attributes).filter(t=>{let[e,i]=t;return i.used_for_first_factor&&!e.startsWith("web3")}).map(t=>{let[e]=t;return e}):[]}get socialProviderStrategies(){return this.social?Object.entries(this.social).filter(t=>{let[,e]=t;return e.enabled}).map(t=>{let[,e]=t;return e.strategy}).sort():[]}get web3FirstFactors(){return this.attributes?Object.entries(this.attributes).filter(t=>{let[e,i]=t;return i.used_for_first_factor&&e.startsWith("web3")}).map(t=>{let[,e]=t;return e.first_factors}).flat():[]}get alternativePhoneCodeChannels(){return this.attributes?Object.entries(this.attributes).filter(t=>{let[e,i]=t;return i.used_for_first_factor&&"phone_number"===e}).map(t=>{var e;let[,i]=t;return(null==i?void 0:null===(e=i.channels)||void 0===e?void 0:e.filter(t=>"sms"!==t))||[]}).flat():[]}get instanceIsPasswordBased(){var t,e,i;return!!((null===(e=this.attributes)||void 0===e?void 0:null===(t=e.password)||void 0===t?void 0:t.enabled)&&(null===(i=this.attributes.password)||void 0===i?void 0:i.required))}get hasValidAuthFactor(){var t,e,i,n,r,a;return!!((null===(e=this.attributes)||void 0===e?void 0:null===(t=e.email_address)||void 0===t?void 0:t.enabled)||(null===(n=this.attributes)||void 0===n?void 0:null===(i=n.phone_number)||void 0===i?void 0:i.enabled)||(null===(r=this.attributes.password)||void 0===r?void 0:r.required)&&(null===(a=this.attributes.username)||void 0===a?void 0:a.required))}fromJSON(t){var e,i,n,r,a,s,o,l,c;return t&&(this.attributes=this.withDefault(t.attributes?Object.fromEntries(Object.entries(t.attributes).map(t=>[t[0],{...t[1],name:t[0]}])):null,this.attributes),this.actions=this.withDefault(t.actions,this.actions),this.enterpriseSSO=this.withDefault(t.enterprise_sso,this.enterpriseSSO),this.passkeySettings=this.withDefault(t.passkey_settings,this.passkeySettings),this.passwordSettings=t.password_settings?{...t.password_settings,min_length:Math.max(null!==(s=null===(e=t.password_settings)||void 0===e?void 0:e.min_length)&&void 0!==s?s:8,8),max_length:(null===(i=t.password_settings)||void 0===i?void 0:i.max_length)===0?72:Math.min(null!==(o=null===(n=t.password_settings)||void 0===n?void 0:n.max_length)&&void 0!==o?o:72,72)}:this.passwordSettings,this.saml=this.withDefault(t.saml,this.saml),this.signIn=this.withDefault(t.sign_in,this.signIn),this.signUp=this.withDefault(t.sign_up,this.signUp),this.social=this.withDefault(t.social,this.social),this.usernameSettings=t.username_settings?{...t.username_settings,min_length:Math.max(null!==(l=null===(r=t.username_settings)||void 0===r?void 0:r.min_length)&&void 0!==l?l:4,4),max_length:Math.min(null!==(c=null===(a=t.username_settings)||void 0===a?void 0:a.max_length)&&void 0!==c?c:64,64)}:this.usernameSettings),this}__internal_toSnapshot(){return{actions:this.actions,attributes:this.attributes,passkey_settings:this.passkeySettings,password_settings:this.passwordSettings,saml:this.saml,sign_in:this.signIn,sign_up:this.signUp,social:this.social}}constructor(t=null){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"actions",{create_organization:!1,delete_self:!1}),(0,r._)(this,"attributes",{email_address:{enabled:!0,first_factors:["email_code"],name:"email_address",required:!0,second_factors:[],used_for_first_factor:!0,used_for_second_factor:!1,verifications:["email_code"],verify_at_sign_up:!0},phone_number:{...a,name:"phone_number"},username:{...a,name:"username"},web3_wallet:{...a,name:"web3_wallet"},first_name:{...a,name:"first_name"},last_name:{...a,name:"last_name"},password:{enabled:!0,first_factors:[],name:"password",required:!0,second_factors:[],used_for_first_factor:!1,used_for_second_factor:!1,verifications:[],verify_at_sign_up:!1},authenticator_app:{...a,name:"authenticator_app"},backup_code:{...a,name:"backup_code"},passkey:{...a,name:"passkey"}}),(0,r._)(this,"enterpriseSSO",{enabled:!1}),(0,r._)(this,"passkeySettings",{allow_autofill:!1,show_sign_in_button:!1}),(0,r._)(this,"passwordSettings",{}),(0,r._)(this,"saml",{enabled:!1}),(0,r._)(this,"signIn",{second_factor:{required:!1,enabled:!1}}),(0,r._)(this,"signUp",{allowlist_only:!1,captcha_enabled:!1,legal_consent_enabled:!1,mode:"public",progressive:!0}),(0,r._)(this,"social",{}),(0,r._)(this,"usernameSettings",{}),this.fromJSON(t)}}class o extends n.i{fromJSON(t){return t&&(this.billing.stripePublishableKey=t.billing.stripe_publishable_key||"",this.billing.enabled=t.billing.enabled||!1,this.billing.hasPaidUserPlans=t.billing.has_paid_user_plans||!1,this.billing.hasPaidOrgPlans=t.billing.has_paid_org_plans||!1),this}__internal_toSnapshot(){return{billing:{stripe_publishable_key:this.billing.stripePublishableKey,enabled:this.billing.enabled,has_paid_user_plans:this.billing.hasPaidUserPlans,has_paid_org_plans:this.billing.hasPaidOrgPlans}}}constructor(t=null){super(),(0,r._)(this,"billing",{stripePublishableKey:"",enabled:!1,hasPaidUserPlans:!1,hasPaidOrgPlans:!1}),this.fromJSON(t)}}var l=i(60104),c=i(29401),u=i(60153),h=i(26917);class d extends n.i{fromJSON(t){return t&&(this.id=t.id,this.externalClientSecret=t.external_client_secret,this.externalGatewayId=t.external_gateway_id,this.statement_id=t.statement_id,this.paymentSource=t.payment_source?new g(t.payment_source):void 0,this.plan=new w(t.plan),this.planPeriod=t.plan_period,this.planPeriodStart=t.plan_period_start,this.status=t.status,this.totals=(0,h.R6)(t.totals),this.isImmediatePlanChange=t.is_immediate_plan_change),this}constructor(t,e){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"externalClientSecret",void 0),(0,r._)(this,"externalGatewayId",void 0),(0,r._)(this,"statement_id",void 0),(0,r._)(this,"paymentSource",void 0),(0,r._)(this,"plan",void 0),(0,r._)(this,"planPeriod",void 0),(0,r._)(this,"planPeriodStart",void 0),(0,r._)(this,"status",void 0),(0,r._)(this,"totals",void 0),(0,r._)(this,"isImmediatePlanChange",void 0),(0,r._)(this,"confirm",t=>{let{orgId:e,...i}=t;return(0,u.X)(()=>this._basePatch({path:e?"/organizations/".concat(e,"/commerce/checkouts/").concat(this.id,"/confirm"):"/me/commerce/checkouts/".concat(this.id,"/confirm"),body:i}),{factor:1.1,maxDelayBetweenRetries:2e3,initialDelay:2e3,jitter:!1,shouldRetry(t,e){var i,n;if(!(0,O.kD)(t)||e>=4)return!1;let r=null==t?void 0:t.status,a=409===r&&(null===(n=t.errors)||void 0===n?void 0:null===(i=n[0])||void 0===i?void 0:i.code)==="checkout_already_in_progress";return r>=500||a}})}),this.fromJSON(t),this.pathRoot=e?"/organizations/".concat(e,"/commerce/checkouts"):"/me/commerce/checkouts"}}class p extends n.i{fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.description=t.description,this.slug=t.slug,this.avatarUrl=t.avatar_url),this}__internal_toSnapshot(){return{object:"commerce_feature",id:this.id,name:this.name,description:this.description,slug:this.slug,avatar_url:this.avatarUrl}}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"description",void 0),(0,r._)(this,"slug",void 0),(0,r._)(this,"avatarUrl",void 0),this.fromJSON(t)}}var f=i(46630);class _ extends n.i{fromJSON(t){return t&&(this.id=t.id,this.status=t.status,this.timestamp=(0,f.V)(t.timestamp),this.totals=(0,h.R6)(t.totals),this.groups=t.groups.map(t=>new v(t))),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"status",void 0),(0,r._)(this,"timestamp",void 0),(0,r._)(this,"totals",void 0),(0,r._)(this,"groups",void 0),this.fromJSON(t)}}class v{fromJSON(t){return t&&(this.id=t.id,this.timestamp=(0,f.V)(t.timestamp),this.items=t.items.map(t=>new m(t))),this}constructor(t){(0,r._)(this,"id",void 0),(0,r._)(this,"timestamp",void 0),(0,r._)(this,"items",void 0),this.fromJSON(t)}}class m extends n.i{fromJSON(t){return t&&(this.id=t.id,this.amount=(0,h.bj)(t.amount),this.paidAt=t.paid_at?(0,f.V)(t.paid_at):void 0,this.failedAt=t.failed_at?(0,f.V)(t.failed_at):void 0,this.updatedAt=(0,f.V)(t.updated_at),this.paymentSource=new g(t.payment_source),this.subscription=new b(t.subscription),this.subscriptionItem=new b(t.subscription_item),this.chargeType=t.charge_type,this.status=t.status),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"amount",void 0),(0,r._)(this,"failedAt",void 0),(0,r._)(this,"paidAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"paymentSource",void 0),(0,r._)(this,"subscription",void 0),(0,r._)(this,"subscriptionItem",void 0),(0,r._)(this,"chargeType",void 0),(0,r._)(this,"status",void 0),this.fromJSON(t)}}i(50725);class g extends n.i{fromJSON(t){var e;return t&&(this.id=t.id,this.last4=t.last4,this.paymentMethod=t.payment_method,this.cardType=t.card_type,this.isDefault=t.is_default,this.isRemovable=t.is_removable,this.status=t.status,this.walletType=null!==(e=t.wallet_type)&&void 0!==e?e:void 0),this}async remove(t){var e;let{orgId:i}=null!=t?t:{};return new S(null===(e=await n.i._fetch({path:i?"/organizations/".concat(i,"/commerce/payment_sources/").concat(this.id):"/me/commerce/payment_sources/".concat(this.id),method:"DELETE"}))||void 0===e?void 0:e.response)}async makeDefault(t){let{orgId:e}=null!=t?t:{};return await n.i._fetch({path:e?"/organizations/".concat(e,"/commerce/payers/default_payment_source"):"/me/commerce/payers/default_payment_source",method:"PUT",body:{payment_source_id:this.id}}),null}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"last4",void 0),(0,r._)(this,"paymentMethod",void 0),(0,r._)(this,"cardType",void 0),(0,r._)(this,"isDefault",void 0),(0,r._)(this,"isRemovable",void 0),(0,r._)(this,"status",void 0),(0,r._)(this,"walletType",void 0),this.fromJSON(t)}}class y extends n.i{fromJSON(t){var e;return t&&(this.externalClientSecret=t.external_client_secret,this.externalGatewayId=t.external_gateway_id,this.paymentMethodOrder=null!==(e=t.payment_method_order)&&void 0!==e?e:["card"]),this}constructor(t){super(),(0,r._)(this,"externalClientSecret",void 0),(0,r._)(this,"externalGatewayId",void 0),(0,r._)(this,"paymentMethodOrder",void 0),this.fromJSON(t)}}class w extends n.i{fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.amount=t.amount,this.amountFormatted=t.amount_formatted,this.annualAmount=t.annual_amount,this.annualAmountFormatted=t.annual_amount_formatted,this.annualMonthlyAmount=t.annual_monthly_amount,this.annualMonthlyAmountFormatted=t.annual_monthly_amount_formatted,this.currencySymbol=t.currency_symbol,this.currency=t.currency,this.description=t.description,this.isDefault=t.is_default,this.isRecurring=t.is_recurring,this.hasBaseFee=t.has_base_fee,this.payerType=t.payer_type,this.publiclyVisible=t.publicly_visible,this.slug=t.slug,this.avatarUrl=t.avatar_url,this.features=(t.features||[]).map(t=>new p(t))),this}__internal_toSnapshot(){return{object:"commerce_plan",id:this.id,name:this.name,amount:this.amount,amount_formatted:this.amountFormatted,annual_amount:this.annualAmount,annual_amount_formatted:this.annualAmountFormatted,annual_monthly_amount:this.annualMonthlyAmount,annual_monthly_amount_formatted:this.annualMonthlyAmountFormatted,currency:this.currency,currency_symbol:this.currencySymbol,description:this.description,is_default:this.isDefault,is_recurring:this.isRecurring,has_base_fee:this.hasBaseFee,payer_type:this.payerType,publicly_visible:this.publiclyVisible,slug:this.slug,avatar_url:this.avatarUrl,features:this.features.map(t=>t.__internal_toSnapshot())}}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"amount",void 0),(0,r._)(this,"amountFormatted",void 0),(0,r._)(this,"annualAmount",void 0),(0,r._)(this,"annualAmountFormatted",void 0),(0,r._)(this,"annualMonthlyAmount",void 0),(0,r._)(this,"annualMonthlyAmountFormatted",void 0),(0,r._)(this,"currencySymbol",void 0),(0,r._)(this,"currency",void 0),(0,r._)(this,"description",void 0),(0,r._)(this,"isDefault",void 0),(0,r._)(this,"isRecurring",void 0),(0,r._)(this,"hasBaseFee",void 0),(0,r._)(this,"payerType",void 0),(0,r._)(this,"publiclyVisible",void 0),(0,r._)(this,"slug",void 0),(0,r._)(this,"avatarUrl",void 0),(0,r._)(this,"features",void 0),this.fromJSON(t)}}n.i;class b extends n.i{fromJSON(t){return t&&(this.id=t.id,this.paymentSourceId=t.payment_source_id,this.plan=new w(t.plan),this.planPeriod=t.plan_period,this.status=t.status,this.periodStart=t.period_start,this.periodEnd=t.period_end,this.canceledAt=t.canceled_at,this.amount=t.amount?(0,h.bj)(t.amount):void 0,this.credit=t.credit&&t.credit.amount?{amount:(0,h.bj)(t.credit.amount)}:void 0),this}async cancel(t){var e;let{orgId:i}=t;return new S(null===(e=await n.i._fetch({path:i?"/organizations/".concat(i,"/commerce/subscriptions/").concat(this.id):"/me/commerce/subscriptions/".concat(this.id),method:"DELETE"}))||void 0===e?void 0:e.response)}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"paymentSourceId",void 0),(0,r._)(this,"plan",void 0),(0,r._)(this,"planPeriod",void 0),(0,r._)(this,"status",void 0),(0,r._)(this,"periodStart",void 0),(0,r._)(this,"periodEnd",void 0),(0,r._)(this,"canceledAt",void 0),(0,r._)(this,"amount",void 0),(0,r._)(this,"credit",void 0),this.fromJSON(t)}}class S{fromJSON(t){return t&&(this.object=t.object,this.id=t.id,this.slug=t.slug,this.deleted=t.deleted),this}constructor(t){(0,r._)(this,"object",""),(0,r._)(this,"id",void 0),(0,r._)(this,"slug",void 0),(0,r._)(this,"deleted",!1),this.fromJSON(t)}}var k=i(74498),A=i(82986),x=i(39357),O=i(83767),U=i(56516),E=i(69596);class P extends n.i{fromJSON(t){return t&&(this.id=t.id,this.provider=t.provider,this.protocol=t.protocol,this.providerUserId=t.provider_user_id,this.active=t.active,this.emailAddress=t.email_address,this.firstName=t.first_name,this.lastName=t.last_name,this.publicMetadata=t.public_metadata,t.verification&&(this.verification=new E.GX(t.verification)),t.enterprise_connection&&(this.enterpriseConnection=new C(t.enterprise_connection))),this}__internal_toSnapshot(){var t,e;return{object:"enterprise_account",id:this.id,provider:this.provider,protocol:this.protocol,provider_user_id:this.providerUserId,active:this.active,email_address:this.emailAddress,first_name:this.firstName,last_name:this.lastName,public_metadata:this.publicMetadata,verification:(null===(t=this.verification)||void 0===t?void 0:t.__internal_toSnapshot())||null,enterprise_connection:(null===(e=this.enterpriseConnection)||void 0===e?void 0:e.__internal_toSnapshot())||null}}constructor(t,e){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"protocol",void 0),(0,r._)(this,"provider",void 0),(0,r._)(this,"providerUserId",null),(0,r._)(this,"active",void 0),(0,r._)(this,"emailAddress",""),(0,r._)(this,"firstName",""),(0,r._)(this,"lastName",""),(0,r._)(this,"publicMetadata",{}),(0,r._)(this,"verification",null),(0,r._)(this,"enterpriseConnection",null),this.pathRoot=e,this.fromJSON(t)}}class C extends n.i{fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.domain=t.domain,this.active=t.active,this.provider=t.provider,this.logoPublicUrl=t.logo_public_url,this.syncUserAttributes=t.sync_user_attributes,this.allowSubdomains=t.allow_subdomains,this.allowIdpInitiated=t.allow_idp_initiated,this.disableAdditionalIdentifications=t.disable_additional_identifications,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}__internal_toSnapshot(){return{object:"enterprise_account_connection",id:this.id,name:this.name,domain:this.domain,active:this.active,protocol:this.protocol,provider:this.provider,logo_public_url:this.logoPublicUrl,sync_user_attributes:this.syncUserAttributes,allow_subdomains:this.allowSubdomains,allow_idp_initiated:this.allowIdpInitiated,disable_additional_identifications:this.disableAdditionalIdentifications,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"active",void 0),(0,r._)(this,"allowIdpInitiated",void 0),(0,r._)(this,"allowSubdomains",void 0),(0,r._)(this,"disableAdditionalIdentifications",void 0),(0,r._)(this,"domain",void 0),(0,r._)(this,"logoPublicUrl",""),(0,r._)(this,"name",void 0),(0,r._)(this,"protocol",void 0),(0,r._)(this,"provider",void 0),(0,r._)(this,"syncUserAttributes",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),this.fromJSON(t)}}var I=i(71956),T=i(36890),R=i(19210),M=i(90750),N=i(4843);class z extends n.i{static async create(t,e){var i;let{name:r}=e;return new z(null===(i=await n.i._fetch({path:"/organizations/".concat(t,"/domains"),method:"POST",body:{name:r}}))||void 0===i?void 0:i.response)}fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.organizationId=t.organization_id,this.enrollmentMode=t.enrollment_mode,this.affiliationEmailAddress=t.affiliation_email_address,this.totalPendingSuggestions=t.total_pending_suggestions,this.totalPendingInvitations=t.total_pending_invitations,t.verification?this.verification={status:t.verification.status,strategy:t.verification.strategy,attempts:t.verification.attempts,expiresAt:(0,f.V)(t.verification.expires_at)}:this.verification=null),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"organizationId",void 0),(0,r._)(this,"enrollmentMode",void 0),(0,r._)(this,"verification",void 0),(0,r._)(this,"affiliationEmailAddress",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"totalPendingInvitations",void 0),(0,r._)(this,"totalPendingSuggestions",void 0),(0,r._)(this,"prepareAffiliationVerification",async t=>this._basePost({path:"/organizations/".concat(this.organizationId,"/domains/").concat(this.id,"/prepare_affiliation_verification"),method:"POST",body:t})),(0,r._)(this,"attemptAffiliationVerification",async t=>this._basePost({path:"/organizations/".concat(this.organizationId,"/domains/").concat(this.id,"/attempt_affiliation_verification"),method:"POST",body:t})),(0,r._)(this,"updateEnrollmentMode",t=>this._basePost({path:"/organizations/".concat(this.organizationId,"/domains/").concat(this.id,"/update_enrollment_mode"),body:t})),(0,r._)(this,"delete",()=>this._baseDelete({path:"/organizations/".concat(this.organizationId,"/domains/").concat(this.id)})),this.fromJSON(t)}}class D extends n.i{fromJSON(t){return t&&(this.id=t.id,this.organizationId=t.organization_id,this.status=t.status,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at),t.public_user_data&&(this.publicUserData=new Y(t.public_user_data))),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"organizationId",void 0),(0,r._)(this,"status",void 0),(0,r._)(this,"publicUserData",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"accept",async()=>await this._basePost({path:"/organizations/".concat(this.organizationId,"/membership_requests/").concat(this.id,"/accept")})),(0,r._)(this,"reject",async()=>await this._basePost({path:"/organizations/".concat(this.organizationId,"/membership_requests/").concat(this.id,"/reject")})),this.fromJSON(t)}}class L extends n.i{fromJSON(t){return t&&(this.id=t.id,this.key=t.key,this.name=t.name,this.description=t.description,this.type=t.type,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"key",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"description",void 0),(0,r._)(this,"type",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),this.fromJSON(t)}}class W extends n.i{fromJSON(t){return t&&(this.id=t.id,this.key=t.key,this.name=t.name,this.description=t.description,this.permissions=t.permissions.map(t=>new L(t)),this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"key",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"description",void 0),(0,r._)(this,"permissions",[]),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),this.fromJSON(t)}}class F extends n.i{static async create(t){var e;return new F(null===(e=await n.i._fetch({path:"/organizations",method:"POST",body:t}))||void 0===e?void 0:e.response)}static async get(t){var e;return new F(null===(e=await n.i._fetch({path:"/organizations/".concat(t),method:"GET"}))||void 0===e?void 0:e.response)}fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.slug=t.slug,this.imageUrl=t.image_url||"",this.hasImage=t.has_image||!1,this.publicMetadata=t.public_metadata||{},this.membersCount=t.members_count||0,this.pendingInvitationsCount=t.pending_invitations_count||0,this.maxAllowedMemberships=t.max_allowed_memberships||0,this.adminDeleteEnabled=t.admin_delete_enabled||!1,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}__internal_toSnapshot(){return{object:"organization",id:this.id,name:this.name,slug:this.slug,image_url:this.imageUrl,has_image:this.hasImage,public_metadata:this.publicMetadata,members_count:this.membersCount,pending_invitations_count:this.pendingInvitationsCount,max_allowed_memberships:this.maxAllowedMemberships,admin_delete_enabled:this.adminDeleteEnabled,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}async reload(t){var e;let{rotatingTokenNonce:i}=t||{},r=null===(e=await n.i._fetch({path:"/organizations/".concat(this.id),method:"GET",rotatingTokenNonce:i},{forceUpdateClient:!0}))||void 0===e?void 0:e.response;return this.fromJSON(r)}constructor(t){super(),(0,r._)(this,"pathRoot","/organizations"),(0,r._)(this,"id",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"slug",void 0),(0,r._)(this,"imageUrl",void 0),(0,r._)(this,"hasImage",void 0),(0,r._)(this,"publicMetadata",{}),(0,r._)(this,"adminDeleteEnabled",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"membersCount",0),(0,r._)(this,"pendingInvitationsCount",0),(0,r._)(this,"maxAllowedMemberships",void 0),(0,r._)(this,"update",async t=>this._basePatch({body:t})),(0,r._)(this,"getRoles",async t=>await n.i._fetch({path:"/organizations/".concat(this.id,"/roles"),method:"GET",search:(0,M.C)(t)},{forceUpdateClient:!0}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new W(t))}})),(0,r._)(this,"getDomains",async t=>await n.i._fetch({path:"/organizations/".concat(this.id,"/domains"),method:"GET",search:(0,M.C)(t)},{forceUpdateClient:!0}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new z(t))}})),(0,r._)(this,"getDomain",async t=>{var e;let{domainId:i}=t;return new z(null===(e=await n.i._fetch({path:"/organizations/".concat(this.id,"/domains/").concat(i),method:"GET"}))||void 0===e?void 0:e.response)}),(0,r._)(this,"getMembershipRequests",async t=>await n.i._fetch({path:"/organizations/".concat(this.id,"/membership_requests"),method:"GET",search:(0,M.C)(t)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new D(t))}})),(0,r._)(this,"createDomain",async t=>z.create(this.id,{name:t})),(0,r._)(this,"getMemberships",async t=>await n.i._fetch({path:"/organizations/".concat(this.id,"/memberships"),method:"GET",search:(0,M.C)({...t,paginated:!0})}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new K(t))}})),(0,r._)(this,"getInvitations",async t=>await n.i._fetch({path:"/organizations/".concat(this.id,"/invitations"),method:"GET",search:(0,M.C)(t)},{forceUpdateClient:!0}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new j(t))}})),(0,r._)(this,"addMember",async t=>{let{userId:e,role:i}=t;return await n.i._fetch({method:"POST",path:"/organizations/".concat(this.id,"/memberships"),body:{userId:e,role:i}}).then(t=>new K(null==t?void 0:t.response))}),(0,r._)(this,"inviteMember",async t=>j.create(this.id,t)),(0,r._)(this,"inviteMembers",async t=>j.createBulk(this.id,t)),(0,r._)(this,"updateMember",async t=>{let{userId:e,role:i}=t;return await n.i._fetch({method:"PATCH",path:"/organizations/".concat(this.id,"/memberships/").concat(e),body:{role:i}}).then(t=>new K(null==t?void 0:t.response))}),(0,r._)(this,"removeMember",async t=>await n.i._fetch({method:"DELETE",path:"/organizations/".concat(this.id,"/memberships/").concat(t)}).then(t=>new K(null==t?void 0:t.response))),(0,r._)(this,"getSubscriptions",async t=>await n.i._fetch({path:"/organizations/".concat(this.id,"/commerce/subscriptions"),method:"GET",search:(0,M.C)(t)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new b(t))}})),(0,r._)(this,"destroy",async()=>this._baseDelete()),(0,r._)(this,"setLogo",async t=>{let e,i,{file:r}=t;return null===r?await n.i._fetch({path:"/organizations/".concat(this.id,"/logo"),method:"DELETE"}).then(t=>new F(null==t?void 0:t.response)):("string"==typeof r?(e=r,i=new Headers({"Content-Type":"application/octet-stream"})):(e=new FormData).append("file",r),await n.i._fetch({path:"/organizations/".concat(this.id,"/logo"),method:"PUT",body:e,headers:i}).then(t=>new F(null==t?void 0:t.response)))}),(0,r._)(this,"initializePaymentSource",t=>(0,N._G)({...t,orgId:this.id})),(0,r._)(this,"addPaymentSource",t=>(0,N.C9)({...t,orgId:this.id})),(0,r._)(this,"getPaymentSources",t=>(0,N.QE)({...t,orgId:this.id})),this.fromJSON(t)}}class j extends n.i{static async create(t,e){var i;let{emailAddress:r,role:a}=e;return new j(null===(i=await n.i._fetch({path:"/organizations/".concat(t,"/invitations"),method:"POST",body:{email_address:r,role:a}}))||void 0===i?void 0:i.response)}static async createBulk(t,e){var i;let{emailAddresses:r,role:a}=e;return(null===(i=await n.i._fetch({path:"/organizations/".concat(t,"/invitations/bulk"),method:"POST",body:{email_address:r,role:a}}))||void 0===i?void 0:i.response).map(t=>new j(t))}fromJSON(t){return t&&(this.id=t.id,this.emailAddress=t.email_address,this.organizationId=t.organization_id,this.publicMetadata=t.public_metadata,this.role=t.role,this.roleName=t.role_name,this.status=t.status,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"emailAddress",void 0),(0,r._)(this,"organizationId",void 0),(0,r._)(this,"publicMetadata",{}),(0,r._)(this,"status",void 0),(0,r._)(this,"role",void 0),(0,r._)(this,"roleName",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"revoke",async()=>await this._basePost({path:"/organizations/".concat(this.organizationId,"/invitations/").concat(this.id,"/revoke")})),this.fromJSON(t)}}var V=i(24152);class K extends n.i{fromJSON(t){return t&&(this.id=t.id,this.organization=new F(t.organization),this.publicMetadata=t.public_metadata||{},t.public_user_data&&(this.publicUserData=new Y(t.public_user_data)),this.permissions=Array.isArray(t.permissions)?[...t.permissions]:[],this.role=t.role,this.roleName=t.role_name,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}__internal_toSnapshot(){var t;return{object:"organization_membership",id:this.id,organization:this.organization.__internal_toSnapshot(),public_metadata:this.publicMetadata,public_user_data:null===(t=this.publicUserData)||void 0===t?void 0:t.__internal_toSnapshot(),permissions:this.permissions,role:this.role,role_name:this.roleName,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}reload(t){(0,V.RE)("OrganizationMembership")}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"publicMetadata",{}),(0,r._)(this,"publicUserData",void 0),(0,r._)(this,"organization",void 0),(0,r._)(this,"permissions",[]),(0,r._)(this,"role",void 0),(0,r._)(this,"roleName",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"destroy",async()=>{var t;return await this._baseDelete({path:"/organizations/".concat(this.organization.id,"/memberships/").concat(null===(t=this.publicUserData)||void 0===t?void 0:t.userId)})}),(0,r._)(this,"update",async t=>{var e;let{role:i}=t;return await this._basePatch({path:"/organizations/".concat(this.organization.id,"/memberships/").concat(null===(e=this.publicUserData)||void 0===e?void 0:e.userId),body:{role:i}})}),this.fromJSON(t)}}(0,r._)(K,"retrieve",async t=>await n.i._fetch({path:"/me/organization_memberships",method:"GET",search:(0,M.C)({...t,paginated:!0})}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new K(t))}}));class B extends n.i{static async retrieve(t){return await n.i._fetch({path:"/me/organization_suggestions",method:"GET",search:(0,M.C)(t)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new B(t))}})}fromJSON(t){return t&&(this.id=t.id,this.status=t.status,this.publicOrganizationData={hasImage:t.public_organization_data.has_image,imageUrl:t.public_organization_data.image_url,name:t.public_organization_data.name,id:t.public_organization_data.id,slug:t.public_organization_data.slug},this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"publicOrganizationData",void 0),(0,r._)(this,"status",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"accept",async()=>await this._basePost({path:"/me/organization_suggestions/".concat(this.id,"/accept")})),this.fromJSON(t)}}var $=i(52724),J=i(67045),q=i(73531),G=i(32208),H=i(93367);class Z extends n.i{static async create(){return n.i._fetch({path:"/me/passkeys",method:"POST"}).then(t=>new Z(null==t?void 0:t.response))}static async attemptVerification(t,e){let i=(0,H.ku)(e);return n.i._fetch({path:"/me/passkeys/".concat(t,"/attempt_verification"),method:"POST",body:{strategy:"passkey",publicKeyCredential:JSON.stringify(i)}}).then(t=>new Z(null==t?void 0:t.response))}static async registerPasskey(){var t;let e=Z.clerk.__internal_isWebAuthnSupported||G.iW,i=Z.clerk.__internal_createPublicCredentials||H.pr,n=Z.clerk.__internal_isWebAuthnPlatformAuthenticatorSupported||G.C6;if(!e())throw new q.RK("Passkeys are not supported on this device.",{code:"passkey_not_supported"});let r=await this.create(),{verification:a}=r,s=null==a?void 0:a.publicKey;if(s||(0,V.$C)("create"),(null===(t=s.authenticatorSelection)||void 0===t?void 0:t.authenticatorAttachment)==="platform"&&!await n())throw new q.RK("Registration requires a platform authenticator but the device does not support it.",{code:"passkey_pa_not_supported"});let{publicKeyCredential:o,error:l}=await i(s);if(!o)throw l;return this.attemptVerification(r.id,o)}fromJSON(t){return t&&(this.id=t.id,this.name=t.name,this.lastUsedAt=t.last_used_at?(0,f.V)(t.last_used_at):null,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at),t.verification&&(this.verification=new E.P$(t.verification))),this}__internal_toSnapshot(){var t,e;return{object:"passkey",id:this.id,name:this.name,verification:(null===(t=this.verification)||void 0===t?void 0:t.__internal_toSnapshot())||null,last_used_at:(null===(e=this.lastUsedAt)||void 0===e?void 0:e.getTime())||null,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"pathRoot","/me/passkeys"),(0,r._)(this,"verification",null),(0,r._)(this,"name",null),(0,r._)(this,"lastUsedAt",null),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"update",t=>this._basePatch({body:t})),(0,r._)(this,"delete",async()=>{var t;return new S(null===(t=await n.i._fetch({path:this.path(),method:"DELETE"}))||void 0===t?void 0:t.response)}),this.fromJSON(t)}}class Y{fromJSON(t){return t&&(this.firstName=t.first_name||null,this.lastName=t.last_name||null,this.imageUrl=t.image_url||"",this.hasImage=t.has_image||!1,this.identifier=t.identifier||"",this.userId=t.user_id),this}__internal_toSnapshot(){return{first_name:this.firstName,last_name:this.lastName,image_url:this.imageUrl,has_image:this.hasImage,identifier:this.identifier,user_id:this.userId}}constructor(t){(0,r._)(this,"firstName",void 0),(0,r._)(this,"lastName",void 0),(0,r._)(this,"imageUrl",void 0),(0,r._)(this,"hasImage",void 0),(0,r._)(this,"identifier",void 0),(0,r._)(this,"userId",void 0),this.fromJSON(t)}}var X=i(28076),Q=i(53898);class tt{fromJSON(t){if(t){var e,i;this.firstName=t.first_name,this.lastName=t.last_name,this.imageUrl=null!==(e=t.image_url)&&void 0!==e?e:void 0,this.hasImage=null!==(i=t.has_image)&&void 0!==i?i:void 0}return this}__internal_toSnapshot(){return{first_name:this.firstName,last_name:this.lastName,image_url:this.imageUrl||null,has_image:this.hasImage||null}}constructor(t){(0,r._)(this,"firstName",void 0),(0,r._)(this,"lastName",void 0),(0,r._)(this,"imageUrl",void 0),(0,r._)(this,"hasImage",void 0),this.fromJSON(t)}}var te=i(90240),ti=i(20523);class tn extends n.i{fromJSON(t){return t&&(this.id=t.id,this.secret=t.secret,this.uri=t.uri,this.verified=t.verified,this.backupCodes=t.backup_codes,this.updatedAt=(0,f.V)(t.updated_at),this.createdAt=(0,f.V)(t.created_at)),this}constructor(t){super(),(0,r._)(this,"pathRoot","/me"),(0,r._)(this,"id",""),(0,r._)(this,"secret",void 0),(0,r._)(this,"uri",void 0),(0,r._)(this,"verified",!1),(0,r._)(this,"backupCodes",void 0),(0,r._)(this,"updatedAt",null),(0,r._)(this,"createdAt",null),this.fromJSON(t)}}var tr=i(49613);class ta extends n.i{static async retrieve(t){return await n.i._fetch({path:"/me/organization_invitations",method:"GET",search:(0,M.C)(t)}).then(t=>{let{data:e,total_count:i}=null==t?void 0:t.response;return{total_count:i,data:e.map(t=>new ta(t))}})}fromJSON(t){return t&&(this.id=t.id,this.emailAddress=t.email_address,this.publicOrganizationData={hasImage:t.public_organization_data.has_image,imageUrl:t.public_organization_data.image_url,name:t.public_organization_data.name,id:t.public_organization_data.id,slug:t.public_organization_data.slug},this.publicMetadata=t.public_metadata,this.role=t.role,this.status=t.status,this.createdAt=(0,f.V)(t.created_at),this.updatedAt=(0,f.V)(t.updated_at)),this}constructor(t){super(),(0,r._)(this,"id",void 0),(0,r._)(this,"emailAddress",void 0),(0,r._)(this,"publicOrganizationData",void 0),(0,r._)(this,"publicMetadata",{}),(0,r._)(this,"status",void 0),(0,r._)(this,"role",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),(0,r._)(this,"accept",async()=>await this._basePost({path:"/me/organization_invitations/".concat(this.id,"/accept")})),this.fromJSON(t)}}var ts=i(34737),to=i(32687);class tl extends n.i{fromJSON(t){return t&&(this.id=t.id,this.type=t.type,this.name=t.name,this.subject=t.subject,this.scopes=t.scopes,this.claims=t.claims,this.revoked=t.revoked,this.revocationReason=t.revocation_reason,this.expired=t.expired,this.expiration=t.expiration?(0,f.V)(t.expiration):null,this.createdBy=t.created_by,this.description=t.description,this.lastUsedAt=t.last_used_at?(0,f.V)(t.last_used_at):null,this.updatedAt=(0,f.V)(t.updated_at),this.createdAt=(0,f.V)(t.created_at)),this}__internal_toSnapshot(){return{object:"api_key",id:this.id,type:this.type,name:this.name,subject:this.subject,scopes:this.scopes,claims:this.claims,revoked:this.revoked,revocation_reason:this.revocationReason,expired:this.expired,expiration:this.expiration?this.expiration.getTime():null,created_by:this.createdBy,description:this.description,last_used_at:this.lastUsedAt?this.lastUsedAt.getTime():null,created_at:this.createdAt.getTime(),updated_at:this.updatedAt.getTime()}}constructor(t){super(),(0,r._)(this,"pathRoot","/api_keys"),(0,r._)(this,"id",void 0),(0,r._)(this,"type",void 0),(0,r._)(this,"name",void 0),(0,r._)(this,"subject",void 0),(0,r._)(this,"scopes",void 0),(0,r._)(this,"claims",void 0),(0,r._)(this,"revoked",void 0),(0,r._)(this,"revocationReason",void 0),(0,r._)(this,"expired",void 0),(0,r._)(this,"expiration",void 0),(0,r._)(this,"createdBy",void 0),(0,r._)(this,"description",void 0),(0,r._)(this,"lastUsedAt",void 0),(0,r._)(this,"createdAt",void 0),(0,r._)(this,"updatedAt",void 0),this.fromJSON(t)}}},76663:function(t,e,i){"use strict";i.d(e,{C:()=>a,m:()=>r});var n=i(26917);let r={org:"add-organization"};function a(t,e){let{componentNavigationContext:i,globalNavigate:a,options:s,environment:o}=e,l="/tasks/".concat(r[t]);if(i)return i.navigate(i.indexPath+l);let c=s.signInUrl||o.displayConfig.signInUrl,u=s.signUpUrl||o.displayConfig.signUpUrl,h=window.location.href.startsWith(u);return a((0,n.KV)({base:h?u:c,hashPath:l},{stringify:!0}))}},7292:function(t,e,i){"use strict";i.d(e,{O:()=>a});var n=i(17431);i(65223),i(56113);class r{static fromKey(t){let[e,i,n=""]=t.split("::");return new r(e,{audience:n,tokenId:i})}toKey(){let{tokenId:t,audience:e}=this.data;return[this.prefix,t,e||""].join("::")}constructor(t,e){(0,n._)(this,"prefix",void 0),(0,n._)(this,"data",void 0),this.prefix=t,this.data=e,this.prefix=t,this.data=e}}let a=function(){let t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"clerk",i=new Map;return{get:function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=new r(e,t),s=i.get(a.toKey());if(!s)return;let o=Math.floor(Date.now()/1e3)-s.createdAt;if(s.expiresIn-o<(n||1)+5){i.delete(a.toKey());return}return s.entry},set:n=>{let a=new r(e,{audience:n.audience,tokenId:n.tokenId}).toKey(),s={entry:n,createdAt:Math.floor(Date.now()/1e3)},o=()=>{i.get(a)===s&&i.delete(a)};n.tokenResolver.then(e=>{if(!e.jwt)return o();let i=e.jwt.claims.exp-e.jwt.claims.iat;s.expiresIn=i,"function"==typeof(t=setTimeout(o,1e3*i)).unref&&t.unref()}).catch(()=>{o()}),i.set(a,s)},clear:()=>{clearTimeout(t),i.clear()},size:()=>i.size}}()},95274:function(t,e,i){"use strict";i.d(e,{q:()=>r}),i(65223);let n=t=>"\uD83D\uDD12 Clerk:\n".concat(t.trim(),"\n(This notice only appears in development)"),r={cannotRenderComponentWhenSessionExists:"The <SignUp/> and <SignIn/> components cannot render when a user is already signed in, unless the application allows multiple sessions. Since a user is signed in and this application only allows a single session, Clerk is redirecting to the Home URL instead.",cannotRenderSignUpComponentWhenSessionExists:"The <SignUp/> component cannot render when a user is already signed in, unless the application allows multiple sessions. Since a user is signed in and this application only allows a single session, Clerk is redirecting to the value set in `afterSignUp` URL instead.",cannotRenderSignUpComponentWhenTaskExists:"The <SignUp/> component cannot render when a user has a pending task, unless the application allows multiple sessions. Since a user is signed in and this application only allows a single session, Clerk is redirecting to the task instead.",cannotRenderSignInComponentWhenSessionExists:"The <SignIn/> component cannot render when a user is already signed in, unless the application allows multiple sessions. Since a user is signed in and this application only allows a single session, Clerk is redirecting to the `afterSignIn` URL instead.",cannotRenderSignInComponentWhenTaskExists:"The <SignIn/> component cannot render when a user has a pending task, unless the application allows multiple sessions. Since a user is signed in and this application only allows a single session, Clerk is redirecting to the task instead.",cannotRenderComponentWhenUserDoesNotExist:"<UserProfile/> cannot render unless a user is signed in. Since no user is signed in, this is no-op.",cannotRenderComponentWhenOrgDoesNotExist:"<OrganizationProfile/> cannot render unless an organization is active. Since no organization is currently active, this is no-op.",cannotRenderAnyOrganizationComponent:t=>n("The <".concat(t,"/> cannot be rendered when the feature is turned off. Visit 'dashboard.clerk.com' to enable the feature. Since the feature is turned off, this is no-op.")),cannotRenderAnyCommerceComponent:t=>n("The <".concat(t,"/> component cannot be rendered when billing is disabled. Visit 'https://dashboard.clerk.com/last-active?path=billing/settings' to follow the necessary steps to enable commerce. Since commerce is disabled, this is no-op.")),cannotOpenUserProfile:"The UserProfile modal cannot render unless a user is signed in. Since no user is signed in, this is no-op.",cannotOpenCheckout:"The Checkout drawer cannot render unless a user is signed in. Since no user is signed in, this is no-op.",cannotOpenSignInOrSignUp:"The SignIn or SignUp modals do not render when a user is already signed in, unless the application allows multiple sessions. Since a user is signed in and this application only allows a single session, this is no-op.",cannotRenderAPIKeysComponent:"The <APIKeys/> component cannot be rendered when API keys is disabled. Since API keys is disabled, this is no-op."};for(let t of Object.keys(r)){let e=r[t];"function"!=typeof e&&(r[t]=n(e))}},87355:function(t,e,i){"use strict";let n;i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223),i(56113),i(87945);{let t=/(^.*\/@clerk\/clerk-js@)(.+?)(\/dist.*)/;(()=>{try{let e=new URL(document.currentScript.src),n=new URL(e.href.split("/").slice(0,-1).join("/")).href;n+=n.endsWith("/")?"":"/",i.p=n.replace(t,"$1".concat("5.69.0","$3"))}catch{}})()}i(34096);var r,a,s,o=i(34056),l=i(12499),c=i(60543),u=i(33101);function h(t,e,i){var n=(0,u._)(t,e,"set");return(0,c._)(t,n,i),i}var d=i(17970),p=i(91412),f=i(17431);i(92037),i(50725),i(79876),i(28419),i(38062);var _=i(72708),v=i(86225);i(64310);var m={Status:"status"},g=()=>(0,v.n)(),y=i(81188),w=i(73531),b=i(41402),S=class{prefixEventName(t){return this.channelKey+t}constructor(t){this.eventTarget=window,this.postMessage=t=>{if("undefined"!=typeof window)try{window.localStorage.setItem(this.channelKey,JSON.stringify(t)),window.localStorage.removeItem(this.channelKey)}catch{}},this.addEventListener=(t,e)=>{this.eventTarget.addEventListener(this.prefixEventName(t),t=>{e(t)})},this.setupLocalStorageListener=()=>{window.addEventListener("storage",t=>{if(t.key===this.channelKey&&t.newValue)try{let e=JSON.parse(t.newValue||""),i=new MessageEvent(this.prefixEventName("message"),{data:e});this.eventTarget.dispatchEvent(i)}catch{}})},this.channelKey="__lsbc__"+t,this.setupLocalStorageListener()}},k=i(82358),A=i(67509);function x(t){return/^http(s)?:\/\//.test(t||"")}function O(t){return t.startsWith("/")}var U=i(72810),E=i(94944),P=i(45100),C=i(26917),I=i(89576);let T="environment",R=JSON.stringify,M=JSON.parse;class N{static _key(t){return"".concat("__clerk_").concat(t)}static isExpired(t){return!!t.exp&&Date.now()>t.exp}static setItem(t,e,i){try{let n={value:e,...i&&{exp:Date.now()+i}};window.localStorage.setItem(this._key(t),R(n))}catch{}}static getItem(t,e){try{var i;let n=window.localStorage.getItem(this._key(t));if(!n)return e;let r=M(n);if(!r)return e;if(this.isExpired(r))return this.removeItem(t),e;return null!==(i=null==r?void 0:r.value)&&void 0!==i?i:e}catch{return e}}static removeItem(t){try{window.localStorage.removeItem(this._key(t))}catch{}}}var z=i(73139);function D(t,e){var i,n,r,a;return t.id!==e.id||t.updatedAt.getTime()<e.updatedAt.getTime()||(i=e,n=t,i.organizationMemberships.length!==n.organizationMemberships.length||(null===(r=i.organizationMemberships[0])||void 0===r?void 0:r.updatedAt)!==(null===(a=n.organizationMemberships[0])||void 0===a?void 0:a.updatedAt))}function L(t,e){return!function(t,e){if(!t&&e||t&&!e)return!0;if(!t&&t===e)return!1;if(!t||!e)return!0;try{var i,n,r,a,s,o,l,c;if(z.KU.isClientResource(t))return t.id!==e.id||t.updatedAt.getTime()<e.updatedAt.getTime()||t.sessions.length!==e.sessions.length;if(z.z_.isSessionResource(t)){return t.id!==e.id||t.updatedAt.getTime()<e.updatedAt.getTime()||(null===(r=t.lastActiveToken)||void 0===r?void 0:null===(n=r.jwt)||void 0===n?void 0:null===(i=n.claims)||void 0===i?void 0:i.__raw)!==(null===(o=e.lastActiveToken)||void 0===o?void 0:null===(s=o.jwt)||void 0===s?void 0:null===(a=s.claims)||void 0===a?void 0:a.__raw)||function(t,e){var i,n,r,a,s,o;if(t.lastActiveOrganizationId!==e.lastActiveOrganizationId)return!0;let l=null===(n=t.user)||void 0===n?void 0:null===(i=n.organizationMemberships)||void 0===i?void 0:i.find(e=>e.organization.id===t.lastActiveOrganizationId),c=null===(a=e.user)||void 0===a?void 0:null===(r=a.organizationMemberships)||void 0===r?void 0:r.find(e=>e.organization.id===t.lastActiveOrganizationId);return(null==l?void 0:null===(s=l.permissions)||void 0===s?void 0:s.length)!==(null==c?void 0:null===(o=c.permissions)||void 0===o?void 0:o.length)}(t,e)||(l=t,c=e,!!l.user!=!!c.user||!!l.user&&!!c.user&&D(l.user,c.user))}if(z.n5.isUserResource(t))return D(t,e)}catch{}return!0}(t,e)?t:e}var W=i(51607);function F(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)t[n]=i[n]}return t}var j=function t(e,i){function n(t,n,r){if("undefined"!=typeof document){"number"==typeof(r=F({},i,r)).expires&&(r.expires=new Date(Date.now()+864e5*r.expires)),r.expires&&(r.expires=r.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var s in r){if(r[s])a+="; "+s,!0!==r[s]&&(a+="="+r[s].split(";")[0])}return document.cookie=t+"="+e.write(n,t)+a}}return Object.create({set:n,get:function(t){if("undefined"!=typeof document&&(!arguments.length||t)){for(var i=document.cookie?document.cookie.split("; "):[],n={},r=0;r<i.length;r++){var a=i[r].split("="),s=a.slice(1).join("=");try{var o=decodeURIComponent(a[0]);if(n[o]=e.read(s,o),t===o)break}catch(t){}}return t?n[t]:n}},remove:function(t,e){n(t,"",F({},e,{expires:-1}))},withAttributes:function(e){return t(this.converter,F({},this.attributes,e))},withConverter:function(e){return t(F({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(i)},converter:{value:Object.freeze(e)}})}({read:function(t){return'"'===t[0]&&(t=t.slice(1,-1)),t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(t){return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});function V(t){return{get:()=>j.get(t),set(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};j.set(t,e,i)},remove(e){j.remove(t,e)}}}var K="__clerk_db_jwt";function B(t,e){let i=new URL(t),n=i.searchParams.get(K);i.searchParams.delete(K);let r=n||e;return r&&i.searchParams.set(K,r),i}var $=t=>t.searchParams.get(K)||"",J=t=>q(G(t)),q=t=>{let e=new URL(t);return e.searchParams.delete(K),e},G=t=>{let e=new URL(t);return e.searchParams.delete("__dev_session"),e.hash=decodeURI(e.hash).replace(/__clerk_db_jwt\[(.*)\]/,""),e.href.endsWith("#")&&(e.hash=""),e},H=i(24152),Z=i(50005),Y=i(98387);let X=V("__clerk_test_etld"),Q=t=>"https:"===window.location.protocol||"None"===t&&void 0===window.safari&&(void 0!==window.isSecureContext?window.isSecureContext:"localhost"===window.location.hostname),tt="__client_uat",te=t=>{let e=V(tt),i=V((0,b._d)(tt,t));return{set:t=>{let r=(0,Y.Bc)(Date.now(),1),a=(0,C.TI)()?"None":"Strict",s=Q(a),o=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.hostname,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:X;if(n)return n;if(["localhost","127.0.0.1","0.0.0.0"].includes(t))return t;let i=t.split(".");if(1===i.length)return t;for(let t=i.length-2;t>=0;t--){let r=i.slice(t).join(".");if(e.set("1",{domain:r}),"1"===e.get())return e.remove({domain:r}),n=r,r;e.remove({domain:r})}}(),l="0";t&&t.updatedAt&&t.signedInSessions.length>0&&(l=Math.floor(t.updatedAt.getTime()/1e3).toString()),i.remove(),e.remove(),i.set(l,{domain:o,expires:r,partitioned:!1,sameSite:a,secure:s}),e.set(l,{domain:o,expires:r,partitioned:!1,sameSite:a,secure:s})},get:()=>parseInt(i.get()||e.get()||"0",10)}},ti="__session",tn=t=>{let e=V(ti),i=V((0,b._d)(ti,t));return{set:t=>{let n=(0,Y.Bc)(Date.now(),1),r=(0,C.TI)()?"None":"Lax",a=Q(r);e.set(t,{expires:n,sameSite:r,secure:a,partitioned:!1}),i.set(t,{expires:n,sameSite:r,secure:a,partitioned:!1})},remove:()=>{e.remove(),i.remove()},get:()=>i.get()||e.get()}};async function tr(t){let e;try{e=await (0,b.P)(t)}catch(s){k.k.logOnce("Suffixed cookie failed due to ".concat(s.message," (secure-context: ").concat(window.isSecureContext,", url: ").concat(window.location.href,")"));let{default:n}=await Promise.all([i.e("200"),i.e("199")]).then(i.t.bind(i,70394,23)),{default:r}=await Promise.all([i.e("200"),i.e("199")]).then(i.t.bind(i,87202,23)),a=n(t);e=r.stringify(a).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}return e}let ta=t=>{let e=V(K),i=V((0,b._d)(K,t));return{get:()=>i.get()||e.get(),set:t=>{let n=(0,Y.Bc)(Date.now(),1),r=(0,C.TI)()?"None":"Lax",a=Q(r);i.set(t,{expires:n,sameSite:r,secure:a}),e.set(t,{expires:n,sameSite:r,secure:a})},remove:()=>{i.remove(),e.remove()}}};var ts=i(79695),to=i(82788),tl=i.n(to);class tc{startPollingForSessionToken(t){if(this.timerId||this.initiated)return;let e=async()=>{this.initiated=!0,await this.lock.acquireLockAndRun(t),this.timerId=this.workerTimers.setTimeout(e,5e3)};e()}stopPollingForSessionToken(){null!=this.timerId&&(this.workerTimers.clearTimeout(this.timerId),this.timerId=null),this.initiated=!1}constructor(){(0,f._)(this,"lock",function(t){let e=new(tl());return window.addEventListener("beforeunload",async()=>{await e.releaseLock(t)}),{acquireLockAndRun:async i=>{if("locks"in navigator&&isSecureContext){let e=new AbortController,n=setTimeout(()=>e.abort(),4999);return await navigator.locks.request(t,{signal:e.signal},async()=>(clearTimeout(n),await i())).catch(()=>!1)}if(await e.acquireLock(t,5e3))try{return await i()}finally{await e.releaseLock(t)}}}}("clerk.lock.refreshSessionToken")),(0,f._)(this,"workerTimers",(0,ts.Q)()),(0,f._)(this,"timerId",null),(0,f._)(this,"initiated",!1)}}class tu{static async create(t,e,i,n){let r=await tr(t.publishableKey),a=new tu(t,e,r,i,n);return await a.setup(),a}async setup(){return(V("clerk_active_org").remove(),"production"===this.instanceType)?this.setupProduction():this.setupDevelopment()}isSignedOut(){return this.clerk.loaded?!!this.clerk.user:0>=this.clientUat.get()}async handleUnauthenticatedDevBrowser(){this.devBrowser.clear(),await this.devBrowser.setup()}decorateUrlWithDevBrowserToken(t){let e=this.devBrowser.getDevBrowserJWT();return e?B(t,e):(0,H.qO)()}async setupDevelopment(){await this.devBrowser.setup()}setupProduction(){this.devBrowser.clear()}startPollingForToken(){this.poller||(this.poller=new tc,this.poller.startPollingForSessionToken(()=>this.refreshSessionToken()))}stopPollingForToken(){this.poller&&(this.poller.stopPollingForSessionToken(),this.poller=null)}refreshTokenOnFocus(){window.addEventListener("focus",()=>{"visible"===document.visibilityState&&this.refreshSessionToken({updateCookieImmediately:!0})})}async refreshSessionToken(){let{updateCookieImmediately:t=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.clerk.session)try{let e=await this.clerk.session.getToken();t&&this.updateSessionCookie(e)}catch(t){return this.handleGetTokenError(t)}}updateSessionCookie(t){if(document.hasFocus()||this.isCurrentContextActive())return this.setActiveContextInStorage(),t?this.sessionCookie.set(t):this.sessionCookie.remove()}setClientUatCookieForDevelopmentInstances(){"production"!==this.instanceType&&this.inCustomDevelopmentDomain()&&this.clientUat.set(this.clerk.client)}inCustomDevelopmentDomain(){let t=this.clerk.frontendApi.replace("clerk.","");return!window.location.host.endsWith(t)}handleGetTokenError(t){if((0,w.kD)(t)||(0,w.uX)(t)||(0,w.eE)(t)){if((0,w.ix)(t)){this.clerk.handleUnauthenticated().catch(P.ZT);return}this.clerkEventBus.emit(m.Status,"degraded")}}setActiveContextInStorage(){var t,e;let i=(null===(t=this.clerk.session)||void 0===t?void 0:t.id)||"",n=(null===(e=this.clerk.organization)||void 0===e?void 0:e.id)||"",r="".concat(i,":").concat(n);":"!==r?this.activeCookie.set(r):this.activeCookie.remove()}isCurrentContextActive(){var t,e;let i=this.activeCookie.get();if(!i)return!0;let[n,r]=i.split(":"),a=(null===(t=this.clerk.session)||void 0===t?void 0:t.id)||"",s=(null===(e=this.clerk.organization)||void 0===e?void 0:e.id)||"";return n===a&&r===s}getSessionCookie(){return this.sessionCookie.get()}constructor(t,e,i,n,r){(0,f._)(this,"clerk",void 0),(0,f._)(this,"instanceType",void 0),(0,f._)(this,"clerkEventBus",void 0),(0,f._)(this,"poller",void 0),(0,f._)(this,"clientUat",void 0),(0,f._)(this,"sessionCookie",void 0),(0,f._)(this,"activeCookie",void 0),(0,f._)(this,"devBrowser",void 0),this.clerk=t,this.instanceType=n,this.clerkEventBus=r,this.poller=null,Z.Y.on(Z.U.TokenUpdate,t=>{let{token:e}=t;this.updateSessionCookie(e&&e.getRawString()),this.setClientUatCookieForDevelopmentInstances()}),this.refreshTokenOnFocus(),this.startPollingForToken(),this.clientUat=te(i),this.sessionCookie=tn(i),this.activeCookie=V("clerk_active_context"),this.devBrowser=function(t){let{cookieSuffix:e,frontendApi:i,fapiClient:n}=t,r=ta(e);function a(){return r.get()}function s(t){r.set(t)}function o(){r.remove()}return{clear:function(){o()},setup:async function(){if(!(0,C.un)(i))return;n.onBeforeRequest(t=>{let e=a();e&&(null==t?void 0:t.url)&&(t.url=B(t.url,e))}),n.onAfterResponse((t,e)=>{var i;let n=null==e?void 0:null===(i=e.headers)||void 0===i?void 0:i.get("Clerk-Db-Jwt");n&&s(n)});let t=function(t){let e=$(t);return J(t).href!==t.href&&void 0!==globalThis.history&&globalThis.history.replaceState(null,"",J(t)),e}(new URL(window.location.href));if(t){s(t);return}if(r.get())return;let e=n.buildUrl({path:"/dev_browser"}),o=await fetch(e.toString(),{method:"POST"});if(!o.ok){let t=await o.json(),e=(0,w.iR)(t.errors);e[0]?(0,H.Dg)(e[0].longMessage):(0,H.Dg)()}let l=await o.json();s(null==l?void 0:l.id)},getDevBrowserJWT:a,setDevBrowserJWT:s,removeDevBrowserJWT:o}}({frontendApi:t.frontendApi,fapiClient:e,cookieSuffix:i})}}var th=i(97976);class td{async start(){this.isEnabled()&&(await this.challengeAndSend(),this.timers.setInterval(()=>{this.challengeAndSend()},this.intervalInMs()))}async challengeAndSend(){if(!(!this.clerk.client||this.clientBypass()))try{let t=await this.captchaChallenge.invisible({action:"heartbeat"});await this.clerk.client.__internal_sendCaptchaToken(t)}catch{}}isEnabled(){var t,e;return!!(null===(e=this.clerk.__unstable__environment)||void 0===e?void 0:null===(t=e.displayConfig)||void 0===t?void 0:t.captchaHeartbeat)}clientBypass(){var t;return null===(t=this.clerk.client)||void 0===t?void 0:t.captchaBypass}intervalInMs(){var t,e,i;return null!==(i=null===(e=this.clerk.__unstable__environment)||void 0===e?void 0:null===(t=e.displayConfig)||void 0===t?void 0:t.captchaHeartbeatIntervalMs)&&void 0!==i?i:6e5}constructor(t,e=new th.E(t),i=(0,ts.Q)()){(0,f._)(this,"clerk",void 0),(0,f._)(this,"captchaChallenge",void 0),(0,f._)(this,"timers",void 0),this.clerk=t,this.captchaChallenge=e,this.timers=i}}var tp=i(80753);i(93008);var tf=i(60153),t_=i(65027);let tv=["/client","/waitlist"];var tm=i(71353),tg=t=>{let{per:e,fpm:i}=t;if(!e||!i)return{permissions:[],featurePermissionMap:[]};let n=e.split(",").map(t=>t.trim()),r=i.split(",").map(t=>Number.parseInt(t.trim(),10)).map(t=>t.toString(2).padStart(n.length,"0").split("").map(t=>Number.parseInt(t,10)).reverse()).filter(Boolean);return{permissions:n,featurePermissionMap:r}},ty=t=>{var e,i,n,r,a,s,o,l;let c,u,h,d;let p=null!==(e=t.fva)&&void 0!==e?e:null,f=null!==(i=t.sts)&&void 0!==i?i:null;if(2===t.v){if(t.o){c=null===(n=t.o)||void 0===n?void 0:n.id,h=null===(r=t.o)||void 0===r?void 0:r.slg,(null===(a=t.o)||void 0===a?void 0:a.rol)&&(u="org:".concat(null===(l=t.o)||void 0===l?void 0:l.rol));let{org:e}=(0,tm.Ly)(t.fea),{permissions:i,featurePermissionMap:p}=tg({per:null===(s=t.o)||void 0===s?void 0:s.per,fpm:null===(o=t.o)||void 0===o?void 0:o.fpm});d=function(t){let{features:e,permissions:i,featurePermissionMap:n}=t;if(!e||!i||!n)return[];let r=[];for(let t=0;t<e.length;t++){let a=e[t];if(t>=n.length)continue;let s=n[t];if(s)for(let t=0;t<s.length;t++)1===s[t]&&r.push("org:".concat(a,":").concat(i[t]))}return r}({features:e,featurePermissionMap:p,permissions:i})}}else c=t.org_id,u=t.org_role,h=t.org_slug,d=t.org_permissions;return{sessionClaims:t,sessionId:t.sid,sessionStatus:f,actor:t.act,userId:t.sub,orgId:c,orgRole:u,orgSlug:h,orgPermissions:d,factorVerificationAge:p}};i(60104);var tw=i(29401);i(74498),i(82986),i(39357),i(83767),i(56516),i(71956),i(36890),i(19210),i(52724),i(67045),i(28076),i(53898),i(90240);var tb=i(20523);i(49613),i(69596),i(34737),i(32687);class tS{async getBaseFapiProxyOptions(){var t;let e=await (null===(t=z.i.clerk.session)||void 0===t?void 0:t.getToken());if(!e)throw new z.w$("No valid session token available",{code:"no_session_token"});return{pathPrefix:"",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},credentials:"same-origin"}}async getAll(t){var e,i,n,r,a;return z.i.clerk.getFapiClient().request({...await this.getBaseFapiProxyOptions(),method:"GET",path:"/api_keys",search:{subject:null!==(a=null!==(r=null!==(n=null==t?void 0:t.subject)&&void 0!==n?n:null===(e=z.i.clerk.organization)||void 0===e?void 0:e.id)&&void 0!==r?r:null===(i=z.i.clerk.user)||void 0===i?void 0:i.id)&&void 0!==a?a:""}}).then(t=>t.payload.api_keys.map(t=>new z.qN(t)))}async getSecret(t){return z.i.clerk.getFapiClient().request({...await this.getBaseFapiProxyOptions(),method:"GET",path:"/api_keys/".concat(t,"/secret")}).then(t=>{let{secret:e}=t.payload;return e})}async create(t){var e,i,n,r,a,s,o;let l=null===(e=await z.i._fetch({...await this.getBaseFapiProxyOptions(),path:"/api_keys",method:"POST",body:JSON.stringify({type:null!==(r=t.type)&&void 0!==r?r:"api_key",name:t.name,subject:null!==(o=null!==(s=null!==(a=t.subject)&&void 0!==a?a:null===(i=z.i.clerk.organization)||void 0===i?void 0:i.id)&&void 0!==s?s:null===(n=z.i.clerk.user)||void 0===n?void 0:n.id)&&void 0!==o?o:"",description:t.description,seconds_until_expiration:t.secondsUntilExpiration})}))||void 0===e?void 0:e.response;return new z.qN(l)}async revoke(t){var e;let i=null===(e=await z.i._fetch({...await this.getBaseFapiProxyOptions(),method:"POST",path:"/api_keys/".concat(t.apiKeyID,"/revoke"),body:JSON.stringify({revocation_reason:t.revocationReason})}))||void 0===e?void 0:e.response;return new z.qN(i)}}var tk=i(4843),tA=i(76663),tx=i(95274);let tO="cannot_render_billing_disabled",tU="cannot_render_user_missing",tE="cannot_render_organizations_disabled",tP="cannot_render_organization_missing",tC="cannot_render_single_session_enabled",tI={polling:!0,standardBrowser:!0,touchSession:!0,isSatellite:!1,signInUrl:void 0,signUpUrl:void 0,afterSignOutUrl:void 0,signInFallbackRedirectUrl:void 0,signUpFallbackRedirectUrl:void 0,signInForceRedirectUrl:void 0,signUpForceRedirectUrl:void 0,treatPendingAsSignedOut:!0,newSubscriptionRedirectUrl:void 0};var tT=new WeakMap,tR=new WeakMap,tM=new WeakMap,tN=new WeakMap,tz=new WeakMap,tD=new WeakMap,tL=new WeakMap,tW=new WeakMap,tF=new WeakMap,tj=new WeakMap,tV=new WeakMap,tK=new WeakMap,tB=new WeakMap,t$=new WeakMap,tJ=new WeakMap,tq=new WeakMap,tG=new WeakMap,tH=new WeakSet,tZ=new WeakMap,tY=new WeakMap,tX=new WeakMap,tQ=new WeakMap,t0=new WeakMap,t1=new WeakMap,t2=new WeakMap,t5=new WeakMap,t3=new WeakMap,t7=new WeakMap,t6=new WeakMap,t9=new WeakMap,t4=new WeakMap,t8=new WeakMap,et=new WeakMap,ee=new WeakMap,ei=new WeakMap,en=new WeakMap,er=new WeakMap,ea=new WeakMap,es=new WeakMap,eo=new WeakMap,el=new WeakMap,ec=new WeakMap,eu=new WeakMap,eh=new WeakMap,ed=new WeakMap;class ep{get publishableKey(){return(0,o._)(this,tT)}get version(){return ep.version}set sdkMetadata(t){ep.sdkMetadata=t}get sdkMetadata(){return ep.sdkMetadata}get loaded(){return"degraded"===this.status||"ready"===this.status}get status(){return(0,o._)(this,tj)}get isSatellite(){return!!(0,C._f)()&&(0,P.YZ)((0,o._)(this,tB).isSatellite,new URL(window.location.href),!1)}get domain(){if((0,C._f)()){let t=(0,E.d5)((0,P.YZ)((0,o._)(this,tR),new URL(window.location.href)));return"production"===(0,o._)(this,tF)?(0,E.xy)(t):t}return""}get proxyUrl(){if((0,C._f)()){let t=(0,P.YZ)((0,o._)(this,tM),new URL(window.location.href));return!(!t||x(t)||O(t))&&C.RM.throwInvalidProxyUrl({url:t}),t?O(t)?new URL(t,window.location.origin).toString():t:""}return""}get frontendApi(){let t=(0,b.nQ)(this.publishableKey);return t?t.frontendApi:C.RM.throwInvalidPublishableKeyError({key:this.publishableKey})}get instanceType(){return(0,o._)(this,tF)}get isStandardBrowser(){return(0,o._)(this,tB).standardBrowser||!1}get billing(){return ep._billing||(ep._billing=new tk.iL),ep._billing}get apiKeys(){return ep._apiKeys||(ep._apiKeys=new tS),ep._apiKeys}__internal_getOption(t){return(0,o._)(this,tB)[t]}get isSignedIn(){var t;let{treatPendingAsSignedOut:e}=(0,o._)(this,tB),i=(null===this||void 0===this?void 0:null===(t=this.session)||void 0===t?void 0:t.status)==="pending";return(!e||!i)&&!!this.session}buildUrlWithAuth(t){if("production"===(0,o._)(this,tF))return t;let e=new URL(t,window.location.origin);return e.origin!==window.location.origin&&(0,o._)(this,tN)?(0,o._)(this,tN).decorateUrlWithDevBrowserToken(e).href:e.href}buildSignInUrl(t){return(0,o._)(this,el).call(this,"signInUrl",{...t,redirectUrl:(null==t?void 0:t.redirectUrl)||window.location.href},null==t?void 0:t.initialValues)}buildSignUpUrl(t){return(0,o._)(this,el).call(this,"signUpUrl",{...t,redirectUrl:(null==t?void 0:t.redirectUrl)||window.location.href},null==t?void 0:t.initialValues)}buildUserProfileUrl(){return this.environment&&this.environment.displayConfig?this.buildUrlWithAuth(this.environment.displayConfig.userProfileUrl):""}buildHomeUrl(){return this.environment&&this.environment.displayConfig?this.buildUrlWithAuth(this.environment.displayConfig.homeUrl):""}buildAfterSignInUrl(){let{params:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.buildUrlWithAuth(new W.O((0,o._)(this,tB),{},t).getAfterSignInUrl())}buildAfterSignUpUrl(){let{params:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.buildUrlWithAuth(new W.O((0,o._)(this,tB),{},t).getAfterSignUpUrl())}buildAfterSignOutUrl(){return(0,o._)(this,tB).afterSignOutUrl?this.buildUrlWithAuth((0,o._)(this,tB).afterSignOutUrl):"/"}buildNewSubscriptionRedirectUrl(){return(0,o._)(this,tB).newSubscriptionRedirectUrl?(0,o._)(this,tB).newSubscriptionRedirectUrl:this.buildAfterSignInUrl()}buildWaitlistUrl(t){if(!this.environment||!this.environment.displayConfig)return"";let e=(0,o._)(this,tB).waitlistUrl||this.environment.displayConfig.waitlistUrl,i=new URLSearchParams((null==t?void 0:t.initialValues)||{});return(0,C.KV)({base:e,hashSearchParams:[i]},{stringify:!0})}buildAfterMultiSessionSingleSignOutUrl(){if(!(0,o._)(this,tB).afterMultiSessionSingleSignOutUrl){var t;return this.buildUrlWithAuth((0,C.KV)({base:(0,o._)(this,tB).signInUrl?"".concat((0,o._)(this,tB).signInUrl,"/choose"):null===(t=this.environment)||void 0===t?void 0:t.displayConfig.afterSignOutOneUrl},{stringify:!0}))}return this.buildUrlWithAuth((0,o._)(this,tB).afterMultiSessionSingleSignOutUrl)}buildCreateOrganizationUrl(){return this.environment&&this.environment.displayConfig?this.buildUrlWithAuth(this.environment.displayConfig.createOrganizationUrl):""}buildOrganizationProfileUrl(){return this.environment&&this.environment.displayConfig?this.buildUrlWithAuth(this.environment.displayConfig.organizationProfileUrl):""}updateEnvironment(t){this.environment=t}get __internal_last_error(){let t=this.internal_last_error;return this.internal_last_error=null,t}set __internal_last_error(t){this.internal_last_error=t}get __unstable__environment(){return this.environment}__internal_navigateWithError(t,e){return this.__internal_last_error=e,this.navigate(t)}assertComponentsReady(t){if(!ep.mountComponentRenderer)throw Error("ClerkJS was loaded without UI components.");if(!t)throw Error("ClerkJS components are not ready yet.")}constructor(t,e){var i=this;if((0,p._)(this,tH),(0,l._)(this,ed,{get:e_,set:void 0}),(0,f._)(this,"client",void 0),(0,f._)(this,"session",void 0),(0,f._)(this,"organization",void 0),(0,f._)(this,"user",void 0),(0,f._)(this,"__internal_country",void 0),(0,f._)(this,"telemetry",void 0),(0,f._)(this,"internal_last_error",null),(0,f._)(this,"environment",void 0),(0,l._)(this,tT,{writable:!0,value:""}),(0,l._)(this,tR,{writable:!0,value:void 0}),(0,l._)(this,tM,{writable:!0,value:void 0}),(0,l._)(this,tN,{writable:!0,value:void 0}),(0,l._)(this,tz,{writable:!0,value:void 0}),(0,l._)(this,tD,{writable:!0,value:null}),(0,l._)(this,tL,{writable:!0,value:void 0}),(0,l._)(this,tW,{writable:!0,value:void 0}),(0,l._)(this,tF,{writable:!0,value:void 0}),(0,l._)(this,tj,{writable:!0,value:"loading"}),(0,l._)(this,tV,{writable:!0,value:[]}),(0,l._)(this,tK,{writable:!0,value:[]}),(0,l._)(this,tB,{writable:!0,value:{}}),(0,l._)(this,t$,{writable:!0,value:null}),(0,l._)(this,tJ,{writable:!0,value:0}),(0,l._)(this,tq,{writable:!0,value:null}),(0,l._)(this,tG,{writable:!0,value:g()}),(0,f._)(this,"__internal_getCachedResources",void 0),(0,f._)(this,"__internal_createPublicCredentials",void 0),(0,f._)(this,"__internal_getPublicCredentials",void 0),(0,f._)(this,"__internal_isWebAuthnSupported",void 0),(0,f._)(this,"__internal_isWebAuthnAutofillSupported",void 0),(0,f._)(this,"__internal_isWebAuthnPlatformAuthenticatorSupported",void 0),(0,f._)(this,"__internal_setActiveInProgress",!1),(0,f._)(this,"getFapiClient",()=>(0,o._)(this,tW)),(0,f._)(this,"load",async t=>{if(!this.loaded){"development"===(0,o._)(this,tF)&&k.k.warnOnce("Clerk: Clerk has been loaded with development keys. Development instances have strict usage limits and should not be used when deploying your application to production. Learn more: https://clerk.com/docs/deployments/overview"),h(this,tB,(0,o._)(this,eu).call(this,t)),Z.Y.on(Z.U.SessionTokenResolved,()=>{(0,o._)(this,er).call(this,this.session),(0,o._)(this,et).call(this)}),(0,I.v)((0,o._)(this,tB)),(0,o._)(this,tB).sdkMetadata&&(ep.sdkMetadata=(0,o._)(this,tB).sdkMetadata),!1!==(0,o._)(this,tB).telemetry&&(this.telemetry=new U.NS({clerkVersion:ep.version,samplingRate:1,publishableKey:this.publishableKey,...(0,o._)(this,tB).telemetry}));try{(0,o._)(this,tB).standardBrowser?await (0,o._)(this,t7).call(this):await (0,o._)(this,t6).call(this)}catch(t){throw(0,o._)(this,tG).emit(m.Status,"error"),t}}}),(0,f._)(this,"signOut",async(t,e)=>{var i,n,r;if(!this.client||0===this.client.sessions.length)return;let a="undefined"!=typeof window&&"function"==typeof window.__unstable__onBeforeSetActive?window.__unstable__onBeforeSetActive:P.ZT,s="undefined"!=typeof window&&"function"==typeof window.__unstable__onAfterSetActive?window.__unstable__onAfterSetActive:P.ZT,l=t&&"object"==typeof t?t:e||{},c=(null==l?void 0:l.redirectUrl)||this.buildAfterSignOutUrl(),u="function"==typeof t?t:void 0,h=async()=>{let t=(0,C.HV)((0,o._)(this,tB).standardBrowser);Z.Y.emit(Z.U.UserSignOut,null),Z.Y.emit(Z.U.TokenUpdate,{token:null}),(0,o._)(this,ei).call(this),await t.track(async()=>{u?await u():await this.navigate(c)}),!t.isUnloading()&&((0,o._)(this,er).call(this),(0,o._)(this,et).call(this),await s())};if(await a(),!l.sessionId||1===this.client.signedInSessions.length){null===(r=null===(n=(0,o._)(this,tB).experimental)||void 0===n?void 0:n.persistClient)||void 0===r||r?await this.client.removeSessions():await this.client.destroy(),await h();return}let d=this.client.signedInSessions.find(t=>t.id===l.sessionId),p=(null==d?void 0:d.id)&&(null===(i=this.session)||void 0===i?void 0:i.id)===d.id;await (null==d?void 0:d.remove()),p&&await h()}),(0,f._)(this,"openGoogleOneTap",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"GoogleOneTap"}).then(e=>e.openModal("googleOneTap",t||{})),null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("GoogleOneTap",t))}),(0,f._)(this,"closeGoogleOneTap",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("googleOneTap"))}),(0,f._)(this,"openSignIn",t=>{var e,i;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.So)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotOpenSignInOrSignUp,{code:tC});return}(0,o._)(this,tL).ensureMounted({preloadHint:"SignIn"}).then(e=>e.openModal("signIn",t||{}));let n={withSignUp:null!==(i=null==t?void 0:t.withSignUp)&&void 0!==i?i:(0,d._)(this,tH,ef).call(this)};null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("SignIn",t,n))}),(0,f._)(this,"closeSignIn",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("signIn"))}),(0,f._)(this,"__internal_openCheckout",t=>{if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.KR)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyCommerceComponent("Checkout"),{code:tO});return}if((0,C.f)(this)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotOpenCheckout,{code:tU});return}(0,o._)(this,tL).ensureMounted({preloadHint:"Checkout"}).then(e=>e.openDrawer("checkout",t||{}))}),(0,f._)(this,"__internal_closeCheckout",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeDrawer("checkout"))}),(0,f._)(this,"__internal_openPlanDetails",t=>{if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.KR)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyCommerceComponent("PlanDetails"),{code:tO});return}(0,o._)(this,tL).ensureMounted({preloadHint:"PlanDetails"}).then(e=>e.openDrawer("planDetails",t||{}))}),(0,f._)(this,"__internal_closePlanDetails",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeDrawer("planDetails"))}),(0,f._)(this,"__internal_openReverification",t=>{var e;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.f)(this)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotOpenUserProfile,{code:tU});return}(0,o._)(this,tL).ensureMounted({preloadHint:"UserVerification"}).then(e=>e.openModal("userVerification",t||{})),null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("UserVerification",t))}),(0,f._)(this,"__internal_closeReverification",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("userVerification"))}),(0,f._)(this,"__internal_openBlankCaptchaModal",()=>(this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"BlankCaptchaModal"}).then(t=>t.openModal("blankCaptcha",{})))),(0,f._)(this,"__internal_closeBlankCaptchaModal",()=>(this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"BlankCaptchaModal"}).then(t=>t.closeModal("blankCaptcha")))),(0,f._)(this,"openSignUp",t=>{var e;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.So)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotOpenSignInOrSignUp,{code:tC});return}(0,o._)(this,tL).ensureMounted({preloadHint:"SignUp"}).then(e=>e.openModal("signUp",t||{})),null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("SignUp",t))}),(0,f._)(this,"closeSignUp",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("signUp"))}),(0,f._)(this,"openUserProfile",t=>{var e,i;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.f)(this)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotOpenUserProfile,{code:tU});return}(0,o._)(this,tL).ensureMounted({preloadHint:"UserProfile"}).then(e=>e.openModal("userProfile",t||{}));let n=(null==t?void 0:null===(e=t.customPages)||void 0===e?void 0:e.length)?{customPages:!0}:void 0;null===(i=this.telemetry)||void 0===i||i.record((0,U.uT)("UserProfile",t,n))}),(0,f._)(this,"closeUserProfile",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("userProfile"))}),(0,f._)(this,"openOrganizationProfile",t=>{var e;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.sW)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyOrganizationComponent("OrganizationProfile"),{code:tE});return}if((0,C.n$)(this)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderComponentWhenOrgDoesNotExist,{code:tP});return}(0,o._)(this,tL).ensureMounted({preloadHint:"OrganizationProfile"}).then(e=>e.openModal("organizationProfile",t||{})),null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("OrganizationProfile",t))}),(0,f._)(this,"closeOrganizationProfile",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("organizationProfile"))}),(0,f._)(this,"openCreateOrganization",t=>{var e;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.sW)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyOrganizationComponent("CreateOrganization"),{code:tE});return}(0,o._)(this,tL).ensureMounted({preloadHint:"CreateOrganization"}).then(e=>e.openModal("createOrganization",t||{})),null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("CreateOrganization",t))}),(0,f._)(this,"closeCreateOrganization",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("createOrganization"))}),(0,f._)(this,"openWaitlist",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"Waitlist"}).then(e=>e.openModal("waitlist",t||{})),null===(e=this.telemetry)||void 0===e||e.record((0,U.uT)("Waitlist",t))}),(0,f._)(this,"closeWaitlist",()=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(t=>t.closeModal("waitlist"))}),(0,f._)(this,"mountSignIn",(t,e)=>{var i,n;this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"SignIn"}).then(i=>i.mountComponent({name:"SignIn",appearanceKey:"signIn",node:t,props:e}));let r={withSignUp:null!==(n=null==e?void 0:e.withSignUp)&&void 0!==n?n:(0,d._)(this,tH,ef).call(this)};null===(i=this.telemetry)||void 0===i||i.record((0,U.nx)("SignIn",e,r))}),(0,f._)(this,"unmountSignIn",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountSignUp",(t,e)=>{var i;this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"SignUp"}).then(i=>i.mountComponent({name:"SignUp",appearanceKey:"signUp",node:t,props:e})),null===(i=this.telemetry)||void 0===i||i.record((0,U.nx)("SignUp",e))}),(0,f._)(this,"unmountSignUp",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountUserProfile",(t,e)=>{var i,n;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.f)(this)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderComponentWhenUserDoesNotExist,{code:tU});return}(0,o._)(this,tL).ensureMounted({preloadHint:"UserProfile"}).then(i=>i.mountComponent({name:"UserProfile",appearanceKey:"userProfile",node:t,props:e}));let r=(null==e?void 0:null===(i=e.customPages)||void 0===i?void 0:i.length)?{customPages:!0}:void 0;null===(n=this.telemetry)||void 0===n||n.record((0,U.nx)("UserProfile",e,r))}),(0,f._)(this,"unmountUserProfile",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountOrganizationProfile",(t,e)=>{var i;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.sW)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyOrganizationComponent("OrganizationProfile"),{code:tE});return}let n=!(0,C.f)(this);if((0,C.n$)(this)&&n){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderComponentWhenOrgDoesNotExist,{code:tP});return}(0,o._)(this,tL).ensureMounted({preloadHint:"OrganizationProfile"}).then(i=>i.mountComponent({name:"OrganizationProfile",appearanceKey:"userProfile",node:t,props:e})),null===(i=this.telemetry)||void 0===i||i.record((0,U.nx)("OrganizationProfile",e))}),(0,f._)(this,"unmountOrganizationProfile",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountCreateOrganization",(t,e)=>{var i,n;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.sW)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyOrganizationComponent("CreateOrganization"),{code:tE});return}null===(i=(0,o._)(this,tL))||void 0===i||i.ensureMounted({preloadHint:"CreateOrganization"}).then(i=>i.mountComponent({name:"CreateOrganization",appearanceKey:"createOrganization",node:t,props:e})),null===(n=this.telemetry)||void 0===n||n.record((0,U.nx)("CreateOrganization",e))}),(0,f._)(this,"unmountCreateOrganization",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),null===(e=(0,o._)(this,tL))||void 0===e||e.ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountOrganizationSwitcher",(t,e)=>{var i,n,r;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.sW)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyOrganizationComponent("OrganizationSwitcher"),{code:tE});return}null===(i=(0,o._)(this,tL))||void 0===i||i.ensureMounted({preloadHint:"OrganizationSwitcher"}).then(i=>i.mountComponent({name:"OrganizationSwitcher",appearanceKey:"organizationSwitcher",node:t,props:e})),null===(r=this.telemetry)||void 0===r||r.record((0,U.nx)("OrganizationSwitcher",{...e,forceOrganizationSelection:null===(n=this.environment)||void 0===n?void 0:n.organizationSettings.forceOrganizationSelection}))}),(0,f._)(this,"unmountOrganizationSwitcher",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),null===(e=(0,o._)(this,tL))||void 0===e||e.ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"__experimental_prefetchOrganizationSwitcher",()=>{var t;this.assertComponentsReady((0,o._)(this,tL)),null===(t=(0,o._)(this,tL))||void 0===t||t.ensureMounted({preloadHint:"OrganizationSwitcher"}).then(t=>t.prefetch("organizationSwitcher"))}),(0,f._)(this,"mountOrganizationList",(t,e)=>{var i,n,r;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.sW)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyOrganizationComponent("OrganizationList"),{code:tE});return}null===(i=(0,o._)(this,tL))||void 0===i||i.ensureMounted({preloadHint:"OrganizationList"}).then(i=>i.mountComponent({name:"OrganizationList",appearanceKey:"organizationList",node:t,props:e})),null===(r=this.telemetry)||void 0===r||r.record((0,U.nx)("OrganizationList",{...e,forceOrganizationSelection:null===(n=this.environment)||void 0===n?void 0:n.organizationSettings.forceOrganizationSelection}))}),(0,f._)(this,"unmountOrganizationList",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),null===(e=(0,o._)(this,tL))||void 0===e||e.ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountUserButton",(t,e)=>{var i,n,r;this.assertComponentsReady((0,o._)(this,tL)),null===(i=(0,o._)(this,tL))||void 0===i||i.ensureMounted({preloadHint:"UserButton"}).then(i=>i.mountComponent({name:"UserButton",appearanceKey:"userButton",node:t,props:e}));let a={...(null==e?void 0:null===(n=e.customMenuItems)||void 0===n?void 0:n.length)?{customItems:!0}:void 0,...(null==e?void 0:e.__experimental_asStandalone)?{standalone:!0}:void 0};null===(r=this.telemetry)||void 0===r||r.record((0,U.nx)("UserButton",e,a))}),(0,f._)(this,"unmountUserButton",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),null===(e=(0,o._)(this,tL))||void 0===e||e.ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountWaitlist",(t,e)=>{var i,n;this.assertComponentsReady((0,o._)(this,tL)),null===(i=(0,o._)(this,tL))||void 0===i||i.ensureMounted({preloadHint:"Waitlist"}).then(i=>i.mountComponent({name:"Waitlist",appearanceKey:"waitlist",node:t,props:e})),null===(n=this.telemetry)||void 0===n||n.record((0,U.nx)("Waitlist",e))}),(0,f._)(this,"unmountWaitlist",t=>{var e;this.assertComponentsReady((0,o._)(this,tL)),null===(e=(0,o._)(this,tL))||void 0===e||e.ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountPricingTable",(t,e)=>{var i;if(this.assertComponentsReady((0,o._)(this,tL)),(0,C.KR)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAnyCommerceComponent("PricingTable"),{code:tO});return}(0,o._)(this,tL).ensureMounted({preloadHint:"PricingTable"}).then(i=>i.mountComponent({name:"PricingTable",appearanceKey:"pricingTable",node:t,props:e})),null===(i=this.telemetry)||void 0===i||i.record((0,U.nx)("PricingTable",e))}),(0,f._)(this,"unmountPricingTable",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"__internal_mountOAuthConsent",(t,e)=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted({preloadHint:"OAuthConsent"}).then(i=>i.mountComponent({name:"OAuthConsent",appearanceKey:"__internal_oauthConsent",node:t,props:e}))}),(0,f._)(this,"__internal_unmountOAuthConsent",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"mountApiKeys",(t,e)=>{var i;if(this.assertComponentsReady((0,o._)(this,tL)),k.k.warnOnce("Clerk: <APIKeys /> component is in early access and not yet recommended for production use."),(0,C.e)(this,this.environment)){if("development"===(0,o._)(this,tF))throw new w.w$(tx.q.cannotRenderAPIKeysComponent,{code:"cannot_render_api_keys_disabled"});return}(0,o._)(this,tL).ensureMounted({preloadHint:"APIKeys"}).then(i=>i.mountComponent({name:"APIKeys",appearanceKey:"apiKeys",node:t,props:e})),null===(i=this.telemetry)||void 0===i||i.record((0,U.nx)("APIKeys",e))}),(0,f._)(this,"unmountApiKeys",t=>{this.assertComponentsReady((0,o._)(this,tL)),(0,o._)(this,tL).ensureMounted().then(e=>e.unmountComponent({node:t}))}),(0,f._)(this,"setActive",async t=>{let{session:e,organization:i,beforeEmit:n,redirectUrl:r}=t;this.__internal_setActiveInProgress=!0;try{if(!this.client)throw Error("setActive is being called before the client is loaded. Wait for init.");if(void 0===e&&!this.session)throw Error("setActive should either be called with a session param or there should be already an active session.");let t="undefined"!=typeof window&&"function"==typeof window.__unstable__onBeforeSetActive?window.__unstable__onBeforeSetActive:P.ZT,l="undefined"!=typeof window&&"function"==typeof window.__unstable__onAfterSetActive?window.__unstable__onAfterSetActive:P.ZT;"string"==typeof e&&(e=this.client.sessions.find(t=>t.id===e)||null);let c=void 0===e?this.session:e,u=void 0!==i;if(c&&u){let t="string"==typeof i?i:null==i?void 0:i.id;if((0,C.ir)(t))c.lastActiveOrganizationId=t||null;else{var a,s;let e=c.user.organizationMemberships.find(e=>e.organization.slug===t),i=(null==e?void 0:e.organization.id)||null,n=null===i;if((null===(s=this.environment)||void 0===s?void 0:null===(a=s.organizationSettings)||void 0===a?void 0:a.forceOrganizationSelection)&&n)return;c.lastActiveOrganizationId=i}}if((null==c?void 0:c.status)==="pending"){await (0,o._)(this,tZ).call(this,c);return}await t(null===c?"sign-out":void 0),((0,C.oH)()||!(0,o._)(this,tB).standardBrowser)&&(await (0,o._)(this,t8).call(this,c),c=(0,o._)(this,ea).call(this,null==c?void 0:c.id)),await (null==c?void 0:c.getToken())||Z.Y.emit(Z.U.TokenUpdate,{token:null});let h=(0,C.HV)((0,o._)(this,tB).standardBrowser);if(n&&((0,y.x9)("Clerk.setActive({beforeEmit})",'Use the `redirectUrl` property instead. Example `Clerk.setActive({redirectUrl:"/"})`'),await h.track(async()=>{(0,o._)(this,ei).call(this),await n(c)})),r&&!n&&await h.track(async()=>{if(this.client){if((0,o._)(this,ei).call(this),this.client.isEligibleForTouch()){let t=new URL(r,window.location.href);await this.navigate(this.buildUrlWithAuth(this.client.buildTouchUrl({redirectUrl:t})))}else await this.navigate(r)}}),h.isUnloading())return;(0,o._)(this,er).call(this,c),(0,o._)(this,et).call(this),await l()}finally{this.__internal_setActiveInProgress=!1}}),(0,l._)(this,tZ,{writable:!0,value:async t=>{if(!this.environment)return;let e=t;if((0,C.oH)()||!(0,o._)(this,tB).standardBrowser){var i;await (0,o._)(this,t8).call(this,t),e=null!==(i=(0,o._)(this,ea).call(this,t.id))&&void 0!==i?i:t}await t.getToken()||Z.Y.emit(Z.U.TokenUpdate,{token:null}),(null==e?void 0:e.currentTask)&&(0,o._)(this,tq)&&await (0,tA.C)(t.currentTask.key,{options:(0,o._)(this,tB),environment:this.environment,globalNavigate:this.navigate,componentNavigationContext:(0,o._)(this,tq)}),(0,o._)(this,er).call(this,t),(0,o._)(this,et).call(this)}}),(0,f._)(this,"__experimental_navigateToTask",async function(){var t,e;let{redirectUrlComplete:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=await (null===(t=i.session)||void 0===t?void 0:t.reload());if(!r||!i.environment)return;if("pending"===r.status){await (0,tA.C)(r.currentTask.key,{options:(0,o._)(i,tB),environment:i.environment,globalNavigate:i.navigate,componentNavigationContext:(0,o._)(i,tq)});return}let a=(0,C.HV)((0,o._)(i,tB).standardBrowser),s=(null===(e=i.client)||void 0===e?void 0:e.signUp)?i.buildAfterSignUpUrl():i.buildAfterSignInUrl();(0,o._)(i,ei).call(i),await a.track(async()=>{await i.navigate(null!=n?n:s)}),!a.isUnloading()&&((0,o._)(i,er).call(i,r),(0,o._)(i,et).call(i))}),(0,f._)(this,"addListener",t=>{var e;let i;return e=t,t=t=>{var n;i||(i={...t}),e(i={client:L((n=i).client,t.client),session:L(n.session,t.session),user:L(n.user,t.user),organization:L(n.organization,t.organization)})},(0,o._)(this,tV).push(t),this.client&&t({client:this.client,session:this.session,user:this.user,organization:this.organization}),()=>{h(this,tV,(0,o._)(this,tV).filter(e=>e!==t))}}),(0,f._)(this,"on",function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];(0,o._)(i,tG).on(...e)}),(0,f._)(this,"off",function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];(0,o._)(i,tG).off(...e)}),(0,f._)(this,"__internal_addNavigationListener",t=>((0,o._)(this,tK).push(t),()=>{h(this,tK,(0,o._)(this,tK).filter(e=>e!==t))})),(0,f._)(this,"__internal_setComponentNavigationContext",t=>(h(this,tq,t),()=>h(this,tq,null))),(0,f._)(this,"navigate",async(t,e)=>{if(!t||!(0,C._f)())return;setTimeout(()=>{(0,o._)(this,ee).call(this)},0);let i=new URL(t,window.location.href);(0,o._)(this,ed).includes(i.protocol)||(console.warn('Clerk: "'.concat(i.protocol,'" is not a valid protocol. Redirecting to "/" instead. If you think this is a mistake, please open an issue.')),i=new URL("/",window.location.href));let n=(null==e?void 0:e.replace)&&(0,o._)(this,tB).routerReplace?(0,o._)(this,tB).routerReplace:(0,o._)(this,tB).routerPush;if((0,o._)(this,tB).routerDebug&&console.log("Clerk is navigating to: ".concat(i)),"null"!==i.origin&&i.origin!==window.location.origin||!n){(0,C.T7)(i);return}let r={...(null==e?void 0:e.metadata)?{__internal_metadata:null==e?void 0:e.metadata}:{},windowNavigate:C.T7};return await n((0,C.M)(i),r)}),(0,l._)(this,tY,{writable:!0,value:async()=>{if(!(0,C._f)())return;let t=new URLSearchParams({[tp.cD]:"true"}),e=(0,C.XV)(tp.w9);e&&x(e)||(0,H.sY)();let i=(0,C.KV)({base:(0,C.XV)(tp.w9),searchParams:t},{stringify:!0});return this.navigate(this.buildUrlWithAuth(i))}}),(0,f._)(this,"redirectWithAuth",async t=>{if((0,C._f)())return this.navigate(this.buildUrlWithAuth(t))}),(0,f._)(this,"redirectToSignIn",async t=>{if((0,C._f)())return this.navigate(this.buildSignInUrl(t))}),(0,f._)(this,"redirectToSignUp",async t=>{if((0,C._f)())return this.navigate(this.buildSignUpUrl(t))}),(0,f._)(this,"redirectToUserProfile",async()=>{if((0,C._f)())return this.navigate(this.buildUserProfileUrl())}),(0,f._)(this,"redirectToCreateOrganization",async()=>{if((0,C._f)())return this.navigate(this.buildCreateOrganizationUrl())}),(0,f._)(this,"redirectToOrganizationProfile",async()=>{if((0,C._f)())return this.navigate(this.buildOrganizationProfileUrl())}),(0,f._)(this,"redirectToAfterSignIn",async()=>{if((0,C._f)())return this.navigate(this.buildAfterSignInUrl())}),(0,f._)(this,"redirectToAfterSignUp",async()=>{if((0,C._f)())return this.navigate(this.buildAfterSignUpUrl())}),(0,f._)(this,"redirectToAfterSignOut",async()=>{if((0,C._f)())return this.navigate(this.buildAfterSignOutUrl())}),(0,f._)(this,"redirectToWaitlist",async()=>{if((0,C._f)())return this.navigate(this.buildWaitlistUrl())}),(0,f._)(this,"handleEmailLinkVerification",async(t,e)=>{if(!this.client)return;let i=(0,C.XV)("__clerk_status");if("expired"===i)throw new z.N(w.u$.Expired);if("client_mismatch"===i)throw new z.N(w.u$.ClientMismatch);if("verified"!==i)throw new z.N(w.u$.Failed);let n=(0,C.XV)("__clerk_created_session"),{signIn:r,signUp:a,sessions:s}=this.client,o=s.some(t=>t.id===n),l="needs_second_factor"===r.status||"missing_requirements"===a.status,c=t=>e&&"function"==typeof e?e(t):this.navigate(t),u=t.redirectUrl?()=>c(t.redirectUrl):P.ZT;return o?this.setActive({session:n,redirectUrl:t.redirectUrlComplete}):l?u():("function"==typeof t.onVerifiedOnOtherDevice&&t.onVerifiedOnOtherDevice(),null)}),(0,f._)(this,"handleGoogleOneTapCallback",async(t,e,i)=>{if(!this.loaded||!this.environment||!this.client)return;let{signIn:n,signUp:r}=this.client,a="identifier"in(t||{})?t:n,s="missingFields"in(t||{})?t:r;return this._handleRedirectCallback(e,{signUp:s,signIn:a,navigate:t=>i&&"function"==typeof i?i(this.buildUrlWithAuth(t)):this.navigate(this.buildUrlWithAuth(t))})}),(0,f._)(this,"_handleRedirectCallback",async(t,e)=>{var i,n,r,a,s,l,c;let{signIn:u,signUp:h,navigate:d}=e;if(!this.loaded||!this.environment||!this.client)return;if(!window.opener&&t.reloadResource)try{"signIn"===t.reloadResource?await u.reload():"signUp"===t.reloadResource&&await h.reload()}catch{}let{displayConfig:p}=this.environment,{firstFactorVerification:f}=u,{externalAccount:_}=h.verifications,v={status:h.status,missingFields:h.missingFields,externalAccountStatus:_.status,externalAccountErrorCode:null===(i=_.error)||void 0===i?void 0:i.code,externalAccountSessionId:null===(r=_.error)||void 0===r?void 0:null===(n=r.meta)||void 0===n?void 0:n.sessionId,sessionId:h.createdSessionId},m={status:u.status,firstFactorVerificationStatus:f.status,firstFactorVerificationErrorCode:null===(a=f.error)||void 0===a?void 0:a.code,firstFactorVerificationSessionId:null===(l=f.error)||void 0===l?void 0:null===(s=l.meta)||void 0===s?void 0:s.sessionId,sessionId:u.createdSessionId},g=t=>()=>d(t),y=g(t.signInUrl||p.signInUrl),w=g(t.signUpUrl||p.signUpUrl),b=g(t.firstFactorUrl||(0,C.KV)({base:p.signInUrl,hashPath:"/factor-one"},{stringify:!0})),S=g(t.secondFactorUrl||(0,C.KV)({base:p.signInUrl,hashPath:"/factor-two"},{stringify:!0})),k=g(t.resetPasswordUrl||(0,C.KV)({base:p.signInUrl,hashPath:"/reset-password"},{stringify:!0})),A=new W.O((0,o._)(this,tB),t),x=g(t.continueSignUpUrl||(0,C.KV)({base:p.signUpUrl,hashPath:"/continue"},{stringify:!0})),O=e=>{let{missingFields:i}=e;return i.length?x():(0,C.vx)({signUp:h,verifyEmailPath:t.verifyEmailAddressUrl||(0,C.KV)({base:p.signUpUrl,hashPath:"/verify-email-address"},{stringify:!0}),verifyPhonePath:t.verifyPhoneNumberUrl||(0,C.KV)({base:p.signUpUrl,hashPath:"/verify-phone-number"},{stringify:!0}),navigate:d})};if("complete"===m.status)return this.setActive({session:m.sessionId,redirectUrl:A.getAfterSignInUrl()});if("transferable"===v.externalAccountStatus&&"external_account_exists"===v.externalAccountErrorCode){let t=await u.create({transfer:!0});switch(t.status){case"complete":return this.setActive({session:t.createdSessionId,redirectUrl:A.getAfterSignInUrl()});case"needs_first_factor":return b();case"needs_second_factor":return S();case"needs_new_password":return k();default:(0,H.O7)("sign in")}}let U="user_locked"===v.externalAccountErrorCode,E="user_locked"===m.firstFactorVerificationErrorCode;if(U)return w();if(E)return y();if("needs_first_factor"===m.status&&!(null===(c=u.supportedFirstFactors)||void 0===c?void 0:c.every(t=>"enterprise_sso"===t.strategy)))return b();if("needs_new_password"===m.status)return k();if("transferable"===m.firstFactorVerificationStatus){if(!1===t.transferable)return y();let e=await h.create({transfer:!0});switch(e.status){case"complete":return this.setActive({session:e.createdSessionId,redirectUrl:A.getAfterSignUpUrl()});case"missing_requirements":return O({missingFields:e.missingFields});default:(0,H.O7)("sign in")}}if("complete"===v.status)return this.setActive({session:v.sessionId,redirectUrl:A.getAfterSignUpUrl()});if("needs_second_factor"===m.status)return S();let P=("failed"===v.externalAccountStatus||"unverified"===v.externalAccountStatus)&&"identifier_already_signed_in"===v.externalAccountErrorCode&&v.externalAccountSessionId,I="failed"===m.firstFactorVerificationStatus&&"identifier_already_signed_in"===m.firstFactorVerificationErrorCode&&m.firstFactorVerificationSessionId;if(P||I){let t=m.firstFactorVerificationSessionId||v.externalAccountSessionId;if(t)return this.setActive({session:t,redirectUrl:A.getAfterSignInUrl()})}return(0,C.l1)(h)?w():"verified"===v.externalAccountStatus&&"missing_requirements"===v.status?O({missingFields:h.missingFields}):y()}),(0,f._)(this,"handleRedirectCallback",async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(!i.loaded||!i.environment||!i.client)return;let{signIn:n,signUp:r}=i.client;return i._handleRedirectCallback(t,{signUp:r,signIn:n,navigate:t=>e&&"function"==typeof e?e(t):i.navigate(t)})}),(0,f._)(this,"handleUnauthenticated",async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{broadcast:!0};if(i.client&&i.session)try{let e=await z.KU.getOrCreateInstance().fetch();if(i.updateClient(e),i.session)return;return t.broadcast&&Z.Y.emit(Z.U.UserSignOut,null),i.setActive({session:null})}catch(t){if((0,w.kD)(t)&&[403,500].includes(t.status))return i.setActive({session:null});throw t}}),(0,f._)(this,"authenticateWithGoogleOneTap",async t=>{var e;return null===(e=this.client)||void 0===e?void 0:e.signIn.create({strategy:"google_one_tap",token:t.token}).catch(e=>{if((0,w.kD)(e)&&"external_account_not_found"===e.errors[0].code){var i;return null===(i=this.client)||void 0===i?void 0:i.signUp.create({strategy:"google_one_tap",token:t.token,legalAccepted:t.legalAccepted})}throw e})}),(0,f._)(this,"authenticateWithMetamask",async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await i.authenticateWithWeb3({...t,strategy:"web3_metamask_signature"})}),(0,f._)(this,"authenticateWithCoinbaseWallet",async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await i.authenticateWithWeb3({...t,strategy:"web3_coinbase_wallet_signature"})}),(0,f._)(this,"authenticateWithOKXWallet",async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await i.authenticateWithWeb3({...t,strategy:"web3_okx_wallet_signature"})}),(0,f._)(this,"authenticateWithWeb3",async t=>{let e,{redirectUrl:i,signUpContinueUrl:n,customNavigate:r,unsafeMetadata:a,strategy:s,legalAccepted:o,secondFactorUrl:l}=t;if(!this.client||!this.environment)return;let{displayConfig:c}=this.environment,u=s.replace("web3_","").replace("_signature",""),h=await (0,C.Ly)({provider:u}),d="metamask"===u?C.wO:"coinbase_wallet"===u?C.dR:C.C3,p=t=>()=>r&&"function"==typeof r?r(t):this.navigate(t),f=p(l||(0,C.KV)({base:c.signInUrl,hashPath:"/factor-two"},{stringify:!0})),_=p(n||(0,C.KV)({base:c.signUpUrl,hashPath:"/continue"},{stringify:!0}));try{e=await this.client.signIn.authenticateWithWeb3({identifier:h,generateSignature:d,strategy:s})}catch(t){if((0,C.VZ)(t,tp.O1.FORM_IDENTIFIER_NOT_FOUND))e=await this.client.signUp.authenticateWithWeb3({identifier:h,generateSignature:d,unsafeMetadata:a,strategy:s,legalAccepted:o}),n&&"missing_requirements"===e.status&&"verified"===e.verifications.web3Wallet.status&&await _();else throw t}switch(e.status){case"needs_second_factor":await f();break;case"complete":e.createdSessionId&&await this.setActive({session:e.createdSessionId,redirectUrl:i});break;default:return}}),(0,f._)(this,"createOrganization",async t=>{let{name:e,slug:i}=t;return z.cp.create({name:e,slug:i})}),(0,f._)(this,"getOrganization",async t=>z.cp.get(t)),(0,f._)(this,"joinWaitlist",async t=>{let{emailAddress:e}=t;return z.yk.join({emailAddress:e})}),(0,f._)(this,"__internal_setCountry",t=>{this.__internal_country||(this.__internal_country=t)}),(0,f._)(this,"updateClient",t=>{if(!this.client){let e=(0,o._)(this,tB).selectInitialSession?(0,o._)(this,tB).selectInitialSession(t):(0,o._)(this,t9).call(this,t);(0,o._)(this,er).call(this,e)}if(this.client=t,this.session){var e;let t=(0,o._)(this,ea).call(this,this.session.id);(0,o._)(this,er).call(this,t),Z.Y.emit(Z.U.TokenUpdate,{token:null===(e=this.session)||void 0===e?void 0:e.lastActiveToken})}(0,o._)(this,et).call(this)}),(0,f._)(this,"__unstable__setEnvironment",async t=>{this.environment=new z.qA(t),ep.mountComponentRenderer&&h(this,tL,ep.mountComponentRenderer(this,this.environment,(0,o._)(this,tB)))}),(0,f._)(this,"__unstable__onBeforeRequest",t=>{(0,o._)(this,tW).onBeforeRequest(t)}),(0,f._)(this,"__unstable__onAfterResponse",t=>{(0,o._)(this,tW).onAfterResponse(t)}),(0,f._)(this,"__unstable__updateProps",t=>{var e;let i={...t,options:(0,o._)(this,eu).call(this,{...(0,o._)(this,tB),...t.options})};return null===(e=(0,o._)(this,tL))||void 0===e?void 0:e.ensureMounted().then(t=>t.updateProps(i))}),(0,l._)(this,tX,{writable:!0,value:()=>{let t=new URLSearchParams({[tp.w9]:window.location.href});return(0,C.KV)({base:(0,o._)(this,tB).signInUrl,searchParams:t},{stringify:!0})}}),(0,l._)(this,tQ,{writable:!0,value:()=>{let t;if(this.proxyUrl){let e=new URL(this.proxyUrl);t=new URL("".concat(e.pathname,"/v1/client/sync"),e.origin)}else this.domain&&(t=new URL("/v1/client/sync","https://".concat(this.domain)));return null==t||t.searchParams.append("redirect_url",window.location.href),(null==t?void 0:t.toString())||""}}),(0,l._)(this,t0,{writable:!0,value:()=>{var t;return"true"!==(0,C.XV)(tp.cD)&&!!this.isSatellite&&!!(null===(t=(0,o._)(this,tN))||void 0===t?void 0:t.isSignedOut())}}),(0,l._)(this,t1,{writable:!0,value:()=>"production"!==(0,o._)(this,tF)&&!this.isSatellite&&!!(0,C.XV)(tp.w9)}),(0,l._)(this,t2,{writable:!0,value:async()=>{"development"===this.instanceType?await this.navigate((0,o._)(this,tX).call(this)):"production"===this.instanceType&&await this.navigate((0,o._)(this,tQ).call(this))}}),(0,l._)(this,t5,{writable:!0,value:(t,e)=>{let i;try{i=new URL(t)}catch{(0,H._5)()}i.origin===e&&(0,H.cT)()}}),(0,l._)(this,t3,{writable:!0,value:()=>{this.isSatellite&&("development"!==(0,o._)(this,tF)||(0,o._)(this,tB).signInUrl||(0,H.qW)(),this.proxyUrl||this.domain||(0,H.xZ)(),(0,o._)(this,tB).signInUrl&&(0,o._)(this,t5).call(this,(0,o._)(this,tB).signInUrl,window.location.origin))}}),(0,l._)(this,t7,{writable:!0,value:async()=>{var t,e;if(h(this,tN,await tu.create(this,(0,o._)(this,tW),(0,o._)(this,tF),(0,o._)(this,tG))),(0,o._)(this,t3).call(this),(0,o._)(this,t0).call(this)){await (0,o._)(this,t2).call(this);return}if((0,o._)(this,t1).call(this)){await (0,o._)(this,tY).call(this);return}h(this,t$,(0,C.LM)()),h(this,tD,new S("clerk")),(0,o._)(this,t4).call(this);let i=(0,C.u9)(null===(t=window)||void 0===t?void 0:t.location.hostname),n="development"===(0,o._)(this,tF)&&!i,r=0,a=0;for(;a<2;){a++;try{let t=z.qA.getInstance().fetch({touch:n}).then(t=>this.updateEnvironment(t)).catch(()=>{++r;let t=N.getItem(T,null);t&&this.updateEnvironment(new z.qA(t))}),i=async()=>z.KU.getOrCreateInstance().fetch().then(t=>this.updateClient(t)).catch(async t=>{var e,i,n;if((0,w.ix)(t))throw t;++r;let a=null===(e=(0,o._)(this,tN))||void 0===e?void 0:e.getSessionCookie(),s=function(t){let e;try{e=new tb.W({jwt:t||"",object:"token",id:void 0})}catch{e=null}if(tw.K.clearInstance(),!(null==e?void 0:e.jwt))return tw.K.getOrCreateInstance({object:"client",last_active_session_id:null,id:"client_init",sessions:[]});let{sessionId:i,userId:n,orgId:r,orgRole:a,orgPermissions:s,orgSlug:o,factorVerificationAge:l}=ty(e.jwt.claims),c={object:"client",last_active_session_id:i,id:"client_init",sessions:[{object:"session",id:i,status:"active",last_active_organization_id:r||null,last_active_token:{id:void 0,object:"token",jwt:t},factor_verification_age:l||null,public_user_data:{user_id:n},user:{object:"user",id:n,organization_memberships:r&&o&&a?[{object:"organization_membership",id:r,role:a,permissions:s||[],organization:{object:"organization",id:r,name:o,slug:o,members_count:1,max_allowed_memberships:1}}]:[]}}]};return tw.K.getOrCreateInstance(c)}(a);return this.updateClient(s),null===(i=(0,o._)(this,tN))||void 0===i||i.stopPollingForToken(),await (null===(n=this.session)||void 0===n?void 0:n.getToken({skipCache:!0}).catch(()=>null).finally(()=>{var t;null===(t=(0,o._)(this,tN))||void 0===t||t.startPollingForToken()})),null}),a=()=>{ep.mountComponentRenderer&&!(0,o._)(this,tL)&&h(this,tL,ep.mountComponentRenderer(this,this.environment,(0,o._)(this,tB)))},[,s]=await (0,P.Lu)([t,i()]);if("rejected"===s.status){let t=s.reason;if((0,C.VZ)(t,"requires_captcha"))a(),await i();else throw t}if(null===(e=(0,o._)(this,tN))||void 0===e||e.setClientUatCookieForDevelopmentInstances(),await (0,o._)(this,ec).call(this))return;a();break}catch(t){if((0,C.VZ)(t,"dev_browser_unauthenticated"))await (0,o._)(this,tN).handleUnauthenticatedDevBrowser();else if((0,_.af)())throw t;else{console.warn(t);return}}a>=2&&(0,H.Xp)()}h(this,tz,new td(this)),(0,o._)(this,tz).start(),(0,o._)(this,eh).call(this),(0,o._)(this,es).call(this),(0,o._)(this,eo).call(this),(0,o._)(this,tG).emit(m.Status,r>0?"degraded":"ready")}}),(0,f._)(this,"shouldFallbackToCachedResources",()=>!!this.__internal_getCachedResources),(0,l._)(this,t6,{writable:!0,value:async()=>{let t,e;let i=this.shouldFallbackToCachedResources()?1:void 0,n=0;try{[t,e]=await Promise.all([z.qA.getInstance().fetch({touch:!1,fetchMaxTries:i}),z.KU.getOrCreateInstance().fetch({fetchMaxTries:i})])}catch(i){if((0,z.uX)(i)&&"network_error"===i.code&&this.shouldFallbackToCachedResources()){var r;let i=await (null===(r=this.__internal_getCachedResources)||void 0===r?void 0:r.call(this));t=new z.qA(null==i?void 0:i.environment),z.KU.clearInstance(),e=z.KU.getOrCreateInstance(null==i?void 0:i.client),++n}else throw i}this.updateClient(e),this.updateEnvironment(t),ep.mountComponentRenderer&&h(this,tL,ep.mountComponentRenderer(this,this.environment,(0,o._)(this,tB))),(0,o._)(this,tG).emit(m.Status,n>0?"degraded":"ready")}}),(0,f._)(this,"__internal_reloadInitialResources",async()=>{let[t,e]=await Promise.all([z.qA.getInstance().fetch({touch:!1,fetchMaxTries:1}),z.KU.getOrCreateInstance().fetch({fetchMaxTries:1})]);this.updateClient(e),this.updateEnvironment(t),(0,o._)(this,et).call(this)}),(0,l._)(this,t9,{writable:!0,value:t=>{if(t.lastActiveSessionId){let e=t.signedInSessions.find(e=>e.id===t.lastActiveSessionId);if(e)return e}return t.signedInSessions[0]||null}}),(0,l._)(this,t4,{writable:!0,value:()=>{var t,e;(0,_._f)()&&(null===(t=(0,o._)(this,t$))||void 0===t||t.onPageFocus(()=>{!(!this.session||!(this.environment&&!this.environment.authConfig.singleSessionMode)&&(0,o._)(this,tJ)>Date.now())&&(h(this,tJ,Date.now()+5e3),(0,o._)(this,t8).call(this,this.session))}),null===(e=(0,o._)(this,tD))||void 0===e||e.addEventListener("message",t=>{let{data:e}=t;"signout"===e.type&&this.handleUnauthenticated({broadcast:!1})}),Z.Y.on(Z.U.UserSignOut,()=>{var t;null===(t=(0,o._)(this,tD))||void 0===t||t.postMessage({type:"signout"})}),Z.Y.on(Z.U.EnvironmentUpdate,()=>{var t;N.setItem(T,null===(t=this.environment)||void 0===t?void 0:t.__internal_toSnapshot(),864e5)}))}}),(0,l._)(this,t8,{writable:!0,value:async t=>{if(!t||!(0,o._)(this,tB).touchSession)return Promise.resolve();await t.touch().catch(t=>{(0,w.ix)(t)&&this.handleUnauthenticated()})}}),(0,l._)(this,et,{writable:!0,value:()=>{if(this.client)for(let t of(0,o._)(this,tV))t({client:this.client,session:this.session,user:this.user,organization:this.organization})}}),(0,l._)(this,ee,{writable:!0,value:()=>{for(let t of(0,o._)(this,tK))t()}}),(0,l._)(this,ei,{writable:!0,value:()=>{this.session=void 0,this.organization=void 0,this.user=void 0,(0,o._)(this,et).call(this)}}),(0,l._)(this,en,{writable:!0,value:()=>{var t;return((null===(t=this.session)||void 0===t?void 0:t.user.organizationMemberships)||[]).map(t=>t.organization).find(t=>{var e;return t.id===(null===(e=this.session)||void 0===e?void 0:e.lastActiveOrganizationId)})||null}}),(0,l._)(this,er,{writable:!0,value:t=>{this.session=t||null,this.organization=(0,o._)(this,en).call(this),this.user=this.session?this.session.user:null}}),(0,l._)(this,ea,{writable:!0,value:t=>{var e;return(null===(e=this.client)||void 0===e?void 0:e.signedInSessions.find(e=>e.id===t))||null}}),(0,l._)(this,es,{writable:!0,value:()=>{this.addListener(t=>{let{session:e}=t;if(null==e?void 0:e.actor){var i;null===(i=(0,o._)(this,tL))||void 0===i||i.ensureMounted().then(t=>t.mountImpersonationFab())}})}}),(0,l._)(this,eo,{writable:!0,value:()=>{if((0,o._)(this,tB).__internal_keyless_claimKeylessApplicationUrl){var t;null===(t=(0,o._)(this,tL))||void 0===t||t.ensureMounted().then(t=>{t.updateProps({options:{__internal_keyless_claimKeylessApplicationUrl:(0,o._)(this,tB).__internal_keyless_claimKeylessApplicationUrl,__internal_keyless_copyInstanceKeysUrl:(0,o._)(this,tB).__internal_keyless_copyInstanceKeysUrl,__internal_keyless_dismissPrompt:(0,o._)(this,tB).__internal_keyless_dismissPrompt}})})}}}),(0,l._)(this,el,{writable:!0,value:(t,e,i)=>{if(!t||!this.loaded||!this.environment||!this.environment.displayConfig)return"";let n=(0,o._)(this,tB)[t]||this.environment.displayConfig[t];(0,d._)(this,tH,ef).call(this)&&(n=(0,o._)(this,tB).signInUrl);let r=new W.O((0,o._)(this,tB),e).toSearchParams(),a=new URLSearchParams(i||{}),s=(0,C.KV)({base:n,hashPath:(0,d._)(this,tH,ef).call(this)&&"signUpUrl"===t?"/create":"",hashSearchParams:[a,r]},{stringify:!0});return this.buildUrlWithAuth(s)}}),(0,l._)(this,ec,{writable:!0,value:async()=>{var t,e;let i=new URLSearchParams(window.location.search).get("redirect_url"),n="production"===this.instanceType,r=null!==i&&(0,C.To)(this.frontendApi,i);if(n||!r)return!1;let a=this.session,s=(0,o._)(this,tB).signInUrl||(null===(t=this.environment)||void 0===t?void 0:t.displayConfig.signInUrl),l=s&&window.location.href.startsWith(s),c=(0,o._)(this,tB).signUpUrl||(null===(e=this.environment)||void 0===e?void 0:e.displayConfig.signUpUrl),u=c&&window.location.href.startsWith(c);return(!(0,C.WW)(i)||!!a||!l&&!u)&&(await this.navigate(this.buildUrlWithAuth(i)),!0)}}),(0,l._)(this,eu,{writable:!0,value:t=>({...tI,...t,allowedRedirectOrigins:(0,C.$1)(null==t?void 0:t.allowedRedirectOrigins,this.frontendApi,this.instanceType)})}),(0,l._)(this,eh,{writable:!0,value:()=>{try{(0,C.xy)(tp.cD),(0,C.xy)(A.A),(0,C.xy)(tp.cc),(0,C.xy)("__clerk_handshake"),(0,C.xy)("__clerk_handshake_nonce"),(0,C.xy)("__clerk_help")}catch{}}}),!(t=(t||"").trim()))return C.RM.throwMissingPublishableKeyError();let n=(0,b.nQ)(t);if(!n)return C.RM.throwInvalidPublishableKeyError({key:t});h(this,tR,null==e?void 0:e.domain),h(this,tM,null==e?void 0:e.proxyUrl),this.environment=z.qA.getInstance(),h(this,tF,n.instanceType),h(this,tT,t),h(this,tW,function(t){let e=[],i=[];async function n(t){for await(let i of["undefined"!=typeof window&&window.__unstable__onBeforeRequest,...e].filter(t=>t))if(await i(t)===!1)return!1;return!0}async function r(t,e){for await(let n of["undefined"!=typeof window&&window.__unstable__onAfterResponse,...i].filter(t=>t))if(await n(t,e)===!1)return!1;return!0}function a(e){let{method:i,path:n,sessionId:r,search:a,rotatingTokenNonce:s}=e,o=new URLSearchParams(a);o.append("__clerk_api_version",tp.gR),o.append("_clerk_js_version","5.69.0"),s&&o.append("rotating_token_nonce",s),t.domain&&"development"===t.instanceType&&t.isSatellite&&o.append("__domain",t.domain),i&&"GET"!==i&&"POST"!==i&&o.append("_method",i),n&&!tv.some(t=>n.startsWith(t))&&r&&o.append("_clerk_session_id",r);let l=[...o.entries()].reduce((t,e)=>{let[i,n]=e;return t[i]=n.includes(",")?n.split(","):n,t},{});return(0,C.f0)(l)}function s(e){let{path:i,pathPrefix:n="v1"}=e;if(t.proxyUrl){let r=new URL(t.proxyUrl),s=r.pathname.slice(1,r.pathname.length);return(0,C.KV)({base:r.origin,pathname:"".concat(s,"/").concat(n).concat(i),search:a(e)},{stringify:!1})}let r="production"===t.instanceType?t.domain:"",s="https://".concat(r||t.frontendApi);return(0,C.KV)({base:s,pathname:"".concat(n).concat(i),search:a(e)},{stringify:!1})}async function o(e,i){let a;let o={...e},{method:l="GET",body:c}=o;o.url=s({...o,sessionId:t.getSessionId()}),o.headers=new Headers(o.headers),"GET"===l||c instanceof FormData||o.headers.has("content-type")||o.headers.set("content-type","application/x-www-form-urlencoded"),"application/x-www-form-urlencoded"===o.headers.get("content-type")&&(o.body=c?(0,C.f0)(c,{keyEncoder:t_.a1}):c);let u=await n(o),h="GET"===l?"GET":"POST",d=o.url.toString(),p={...o,method:h,credentials:o.credentials||"include"};try{if(u){var f;let t=null!==(f=null==i?void 0:i.fetchMaxTries)&&void 0!==f?f:(0,_.TN)()?4:11;a=await (0,tf.X)(()=>fetch(d,p),{retryImmediately:!0,initialDelay:700,maxDelayBetweenRetries:5e3,shouldRetry:(e,i)=>"GET"===h&&i<t})}else a=new Response("{}",o)}catch(t){(0,H.an)(d,t)}let v=204!==a.status?await a.json():null,m=Object.assign(a,{payload:v});return await r(o,m),m}return{buildEmailAddress:function(e){return(0,C.aR)({localPart:e,frontendApi:t.frontendApi})},buildUrl:s,onAfterResponse:function(t){i.push(t)},onBeforeRequest:function(t){e.push(t)},request:o}}({domain:this.domain,frontendApi:this.frontendApi,instanceType:this.instanceType,isSatellite:this.isSatellite,getSessionId:()=>{var t;return null===(t=this.session)||void 0===t?void 0:t.id},proxyUrl:this.proxyUrl})),(0,o._)(this,tG).emit(m.Status,"loading"),(0,o._)(this,tG).prioritizedOn(m.Status,t=>h(this,tj,t)),z.i.clerk=this}}function ef(){return!!(!(0,o._)(this,tB).signUpUrl&&(0,o._)(this,tB).signInUrl&&!(0,E.sD)((0,o._)(this,tB).signInUrl))}function e_(){let t=C.Sk;return(0,o._)(this,tB).allowedRedirectProtocols&&(t=t.concat((0,o._)(this,tB).allowedRedirectProtocols)),t}(0,f._)(ep,"mountComponentRenderer",void 0),(0,f._)(ep,"version","5.69.0"),(0,f._)(ep,"sdkMetadata",{name:"@clerk/clerk-js",version:"5.69.0",environment:"production"}),(0,f._)(ep,"_billing",void 0),(0,f._)(ep,"_apiKeys",void 0);var ev=i(79109),em=i(69144);function eg(t,e){if(!t)return{};let i="signin"===e?"signIn":"signUp",n={["".concat(i,"ForceRedirectUrl")]:t.forceRedirectUrl,["".concat(i,"FallbackRedirectUrl")]:t.fallbackRedirectUrl},r={...t,...n};return delete r.forceRedirectUrl,delete r.fallbackRedirectUrl,r}var ey=i(91139),ew=i(7508);let eb=(0,em.lazy)(()=>Promise.all([i.e("200"),i.e("573")]).then(i.bind(i,88742)).then(t=>({default:t.MountedCheckoutDrawer}))),eS=(0,em.lazy)(()=>Promise.all([i.e("200"),i.e("573")]).then(i.bind(i,78902)).then(t=>({default:t.MountedPlanDetailDrawer})));var ek=i(56556);let eA="undefined"!=typeof window?em.useLayoutEffect:em.useEffect,ex=0,eO={},eU=Object.freeze({SignUp:"signUpModal",SignIn:"signInModal",UserProfile:"userProfileModal",OrganizationProfile:"organizationProfileModal",CreateOrganization:"createOrganizationModal",Waitlist:"waitlistModal"}),eE=t=>{var e,i;let[n,r]=em.useState({appearance:t.options.appearance,options:t.options,googleOneTapModal:null,signInModal:null,signUpModal:null,userProfileModal:null,userVerificationModal:null,organizationProfileModal:null,createOrganizationModal:null,organizationSwitcherPrefetch:!1,waitlistModal:null,blankCaptchaModal:null,checkoutDrawer:{open:!1,props:null},planDetailsDrawer:{open:!1,props:null},nodes:new Map,impersonationFab:!1}),{googleOneTapModal:a,signInModal:s,signUpModal:o,userProfileModal:l,userVerificationModal:c,organizationProfileModal:u,createOrganizationModal:h,waitlistModal:d,blankCaptchaModal:p,checkoutDrawer:f,planDetailsDrawer:_,nodes:v}=n,{urlStateParam:m,clearUrlStateParam:g,decodedRedirectParams:y}=(0,ey._)();eA(()=>{y&&r(t=>({...t,[eU[y.componentName]]:!0})),eO.mountComponent=t=>{let{node:e,name:i,props:n,appearanceKey:a}=t;e||(0,H.HE)(),r(t=>(t.nodes.set(e,{key:"p".concat(++ex),name:i,props:n,appearanceKey:a}),{...t,nodes:v}))},eO.unmountComponent=t=>{let{node:e}=t;r(t=>(t.nodes.delete(e),{...t,nodes:v}))},eO.updateProps=t=>{let{node:e,props:i,...a}=t;if(e&&i&&"object"==typeof i){let t=n.nodes.get(e);if(t){t.props={...i},r(t=>({...t}));return}}r(t=>({...t,...a,options:{...t.options,...a.options}}))},eO.closeModal=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{notify:i=!0}=e;g(),r(e=>((function(){let n=e["".concat(t,"Modal")]||{};if("afterVerificationCancelled"in n&&i){var r;null===(r=n.afterVerificationCancelled)||void 0===r||r.call(n)}})(),{...e,["".concat(t,"Modal")]:null}))},eO.openModal=(t,e)=>{"afterVerificationCancelled"in e?"afterVerificationCancelled"in e&&r(i=>({...i,["".concat(t,"Modal")]:{...e,afterVerification(){var i;null===(i=e.afterVerification)||void 0===i||i.call(e),eO.closeModal(t,{notify:!1})}}})):r(i=>({...i,["".concat(t,"Modal")]:e}))},eO.mountImpersonationFab=()=>{r(t=>({...t,impersonationFab:!0}))},eO.openDrawer=(t,e)=>{r(i=>({...i,["".concat(t,"Drawer")]:{open:!0,props:e}}))},eO.closeDrawer=t=>{r(e=>{var i,n;let r=e["".concat(t,"Drawer")];return null==r||null===(n=r.props)||void 0===n||null===(i=n.onClose)||void 0===i||i.call(n),{...e,["".concat(t,"Drawer")]:{...e["".concat(t,"Drawer")],open:!1}}})},eO.prefetch=t=>{r(e=>({...e,["".concat(t,"Prefetch")]:!0}))},t.onComponentsMounted()},[]);let w=(0,ev.tZ)(ek._,{componentProps:a,globalAppearance:n.appearance,componentAppearance:null==a?void 0:a.appearance,startPath:(0,C.e1)({base:"/one-tap",path:""})}),b=(0,ev.BX)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"signIn",componentAppearance:null==s?void 0:s.appearance,flowName:"signIn",onClose:()=>eO.closeModal("signIn"),onExternalNavigate:()=>eO.closeModal("signIn"),startPath:(0,C.e1)({base:"/sign-in",path:null==m?void 0:m.path}),componentName:"SignInModal",children:[(0,ev.tZ)(ew.fA,{...s}),(0,ev.tZ)(ew.XL,{...eg(s,"signin")}),(0,ev.tZ)(ew.ql,{...d})]}),S=(0,ev.BX)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"signUp",componentAppearance:null==o?void 0:o.appearance,flowName:"signUp",onClose:()=>eO.closeModal("signUp"),onExternalNavigate:()=>eO.closeModal("signUp"),startPath:(0,C.e1)({base:"/sign-up",path:null==m?void 0:m.path}),componentName:"SignUpModal",children:[(0,ev.tZ)(ew.fA,{...eg(o,"signup")}),(0,ev.tZ)(ew.XL,{...o}),(0,ev.tZ)(ew.ql,{...d})]}),k=(0,ev.tZ)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"userProfile",componentAppearance:null==l?void 0:l.appearance,flowName:"userProfile",onClose:()=>eO.closeModal("userProfile"),onExternalNavigate:()=>eO.closeModal("userProfile"),startPath:(0,C.e1)({base:"/user",path:(null==l?void 0:l.__experimental_startPath)||(null==m?void 0:m.path)}),componentName:"UserProfileModal",modalContainerSx:{alignItems:"center"},modalContentSx:t=>({height:"min(".concat(t.sizes.$176,", calc(100% - ").concat(t.sizes.$12,"))"),margin:0}),children:(0,ev.tZ)(ew.FI,{...l})}),A=(0,ev.tZ)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"userVerification",componentAppearance:null==c?void 0:c.appearance,flowName:"userVerification",onClose:()=>eO.closeModal("userVerification"),onExternalNavigate:()=>eO.closeModal("userVerification"),startPath:(0,C.e1)({base:"/user-verification",path:null==m?void 0:m.path}),componentName:"UserVerificationModal",modalContainerSx:{alignItems:"center"},children:(0,ev.tZ)(ew.$D,{...c})}),x=(0,ev.tZ)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"organizationProfile",componentAppearance:null==u?void 0:u.appearance,flowName:"organizationProfile",onClose:()=>eO.closeModal("organizationProfile"),onExternalNavigate:()=>eO.closeModal("organizationProfile"),startPath:(0,C.e1)({base:"/organizationProfile",path:(null==u?void 0:u.__experimental_startPath)||(null==m?void 0:m.path)}),componentName:"OrganizationProfileModal",modalContainerSx:{alignItems:"center"},modalContentSx:t=>({height:"min(".concat(t.sizes.$176,", calc(100% - ").concat(t.sizes.$12,"))"),margin:0}),children:(0,ev.tZ)(ew.J3,{...u})}),O=(0,ev.tZ)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"createOrganization",componentAppearance:null==h?void 0:h.appearance,flowName:"createOrganization",onClose:()=>eO.closeModal("createOrganization"),onExternalNavigate:()=>eO.closeModal("createOrganization"),startPath:(0,C.e1)({base:"/createOrganization",path:null==m?void 0:m.path}),componentName:"CreateOrganizationModal",modalContainerSx:{alignItems:"center"},modalContentSx:t=>({height:"min(".concat(t.sizes.$120,", calc(100% - ").concat(t.sizes.$12,"))"),margin:0}),children:(0,ev.tZ)(ew.yh,{...h})}),U=(0,ev.BX)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"waitlist",componentAppearance:null==d?void 0:d.appearance,flowName:"waitlist",onClose:()=>eO.closeModal("waitlist"),onExternalNavigate:()=>eO.closeModal("waitlist"),startPath:(0,C.e1)({base:"/waitlist",path:null==m?void 0:m.path}),componentName:"WaitlistModal",children:[(0,ev.tZ)(ew.ql,{...d}),(0,ev.tZ)(ew.fA,{...d})]}),E=(0,ev.tZ)(ek.mA,{globalAppearance:n.appearance,appearanceKey:"blankCaptcha",componentAppearance:{},flowName:"blankCaptcha",onClose:()=>eO.closeModal("blankCaptcha"),startPath:(0,C.e1)({base:"/blank-captcha",path:null==m?void 0:m.path}),componentName:"BlankCaptchaModal",canCloseModal:!1,modalId:"cl-modal-captcha-wrapper",modalStyle:{visibility:"hidden",pointerEvents:"none"},children:(0,ev.tZ)(ew.U3,{})});return(0,ev.tZ)(em.Suspense,{fallback:"",children:(0,ev.BX)(ek.Jk,{clerk:t.clerk,environment:t.environment,options:n.options,children:[[...v].map(t=>{var e;let[i,r]=t;return(0,ev.tZ)(ek.P3,{node:i,globalAppearance:n.appearance,appearanceKey:r.appearanceKey,componentAppearance:null===(e=r.props)||void 0===e?void 0:e.appearance,componentName:r.name,componentProps:r.props},r.key)}),a&&w,s&&b,o&&S,l&&k,c&&A,u&&x,h&&O,d&&U,p&&E,(0,ev.tZ)(eb,{appearance:n.appearance,checkoutDrawer:f,onOpenChange:()=>eO.closeDrawer("checkout")}),(0,ev.tZ)(eS,{appearance:n.appearance,planDetailsDrawer:_,onOpenChange:()=>eO.closeDrawer("planDetails")}),n.impersonationFab&&(0,ev.tZ)(ek.$9,{globalAppearance:n.appearance,children:(0,ev.tZ)(ew.Qm,{})}),(null===(e=n.options)||void 0===e?void 0:e.__internal_keyless_claimKeylessApplicationUrl)&&(null===(i=n.options)||void 0===i?void 0:i.__internal_keyless_copyInstanceKeysUrl)&&(0,ev.tZ)(ek.$9,{globalAppearance:n.appearance,children:(0,ev.tZ)(ew.B1,{claimUrl:n.options.__internal_keyless_claimKeylessApplicationUrl,copyKeysUrl:n.options.__internal_keyless_copyInstanceKeysUrl,onDismiss:n.options.__internal_keyless_dismissPrompt})}),(0,ev.tZ)(em.Suspense,{children:n.organizationSwitcherPrefetch&&(0,ev.tZ)(ek.oL,{})})]})})};t=i.hmd(t),ep.mountComponentRenderer=(t,e,n)=>{let r,a=document.getElementById("clerk-components");return a||((a=document.createElement("div")).setAttribute("id","clerk-components"),document.body.appendChild(a)),{ensureMounted:async s=>{let{preloadHint:o}=s||{};if(!r){let s=(0,P.WK)();o&&(0,ew.Yt)(o),r=Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,5681)).then(i=>{let{createRoot:r}=i;return r(a).render((0,ev.tZ)(eE,{clerk:t,environment:e,options:n,onComponentsMounted:s.resolve})),s.promise.then(()=>eO)})}return r.then(t=>t)}}};let eP=(null===(r=document.querySelector("script[data-clerk-publishable-key]"))||void 0===r?void 0:r.getAttribute("data-clerk-publishable-key"))||window.__clerk_publishable_key||"",eC=(null===(a=document.querySelector("script[data-clerk-proxy-url]"))||void 0===a?void 0:a.getAttribute("data-clerk-proxy-url"))||window.__clerk_proxy_url||"",eI=(null===(s=document.querySelector("script[data-clerk-domain]"))||void 0===s?void 0:s.getAttribute("data-clerk-domain"))||window.__clerk_domain||"";window.Clerk||(window.Clerk=new ep(eP,{proxyUrl:eC,domain:eI})),t.hot&&t.hot.accept()},91139:function(t,e,i){"use strict";i.d(e,{_:()=>s}),i(65223);var n=i(69144),r=i(80753),a=i(26917);let s=()=>{let[t,e]=n.useState({startPath:"",path:"",componentName:"",socialProvider:""}),i=(0,a.w$)();n.useLayoutEffect(()=>{i&&e(i)},[]);let s=()=>{e({startPath:"",path:"",componentName:"",socialProvider:""})};return{urlStateParam:{...t,clearUrlStateParam:s},decodedRedirectParams:i,clearUrlStateParam:s,removeQueryParam:()=>(0,a.xy)(r.v_)}}},7508:function(t,e,i){"use strict";i.d(e,{$D:()=>c,B1:()=>x,FI:()=>f,J3:()=>g,KO:()=>P,NQ:()=>R,Qm:()=>A,U3:()=>k,XL:()=>h,Yt:()=>T,fA:()=>s,hZ:()=>E,ql:()=>S,x7:()=>I,yh:()=>v}),i(50725);var n=i(69144);let r={SignIn:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("722")]).then(i.bind(i,6154)),SignUp:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("710")]).then(i.bind(i,85049)),UserButton:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("270")]).then(i.bind(i,37322)),UserProfile:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("66"),i.e("646"),i.e("470")]).then(i.bind(i,36871)),CreateOrganization:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("192"),i.e("96")]).then(i.bind(i,21441)),OrganizationProfile:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("192"),i.e("66"),i.e("646"),i.e("554")]).then(i.bind(i,79743)),OrganizationSwitcher:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("780")]).then(i.bind(i,96061)),OrganizationList:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("192"),i.e("158")]).then(i.bind(i,30683)),ImpersonationFab:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("378")]).then(i.bind(i,75692)),GoogleOneTap:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("920")]).then(i.bind(i,41491)),BlankCaptchaModal:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("875")]).then(i.bind(i,59900)),UserVerification:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("662")]).then(i.bind(i,61419)),Waitlist:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("861")]).then(i.bind(i,46522)),KeylessPrompt:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("307")]).then(i.bind(i,61976)),PricingTable:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("66"),i.e("219")]).then(i.bind(i,29828)),Checkout:()=>Promise.all([i.e("344"),i.e("507"),i.e("200"),i.e("573"),i.e("237")]).then(i.bind(i,10670)),SessionTasks:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("192"),i.e("158"),i.e("877")]).then(i.bind(i,51618)),PlanDetails:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("33")]).then(i.bind(i,51991)),APIKeys:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("616")]).then(i.bind(i,52010)),OAuthConsent:()=>Promise.all([i.e("344"),i.e("200"),i.e("573"),i.e("776")]).then(i.bind(i,40429))},a=(0,n.lazy)(()=>r.SignIn().then(t=>({default:t.SignIn}))),s=(0,n.lazy)(()=>r.SignIn().then(t=>({default:t.SignInModal}))),o=(0,n.lazy)(()=>r.GoogleOneTap().then(t=>({default:t.OneTap}))),l=(0,n.lazy)(()=>r.UserVerification().then(t=>({default:t.UserVerification}))),c=(0,n.lazy)(()=>r.UserVerification().then(t=>({default:t.UserVerificationModal}))),u=(0,n.lazy)(()=>r.SignUp().then(t=>({default:t.SignUp}))),h=(0,n.lazy)(()=>r.SignUp().then(t=>({default:t.SignUpModal}))),d=(0,n.lazy)(()=>r.UserButton().then(t=>({default:t.UserButton}))),p=(0,n.lazy)(()=>r.UserProfile().then(t=>({default:t.UserProfile}))),f=(0,n.lazy)(()=>r.UserProfile().then(t=>({default:t.UserProfileModal}))),_=(0,n.lazy)(()=>r.CreateOrganization().then(t=>({default:t.CreateOrganization}))),v=(0,n.lazy)(()=>r.CreateOrganization().then(t=>({default:t.CreateOrganizationModal}))),m=(0,n.lazy)(()=>r.OrganizationProfile().then(t=>({default:t.OrganizationProfile}))),g=(0,n.lazy)(()=>r.OrganizationProfile().then(t=>({default:t.OrganizationProfileModal}))),y=(0,n.lazy)(()=>r.OrganizationSwitcher().then(t=>({default:t.OrganizationSwitcher}))),w=(0,n.lazy)(()=>r.OrganizationList().then(t=>({default:t.OrganizationList}))),b=(0,n.lazy)(()=>r.Waitlist().then(t=>({default:t.Waitlist}))),S=(0,n.lazy)(()=>r.Waitlist().then(t=>({default:t.WaitlistModal}))),k=(0,n.lazy)(()=>r.BlankCaptchaModal().then(t=>({default:t.BlankCaptchaModal}))),A=(0,n.lazy)(()=>r.ImpersonationFab().then(t=>({default:t.ImpersonationFab}))),x=(0,n.lazy)(()=>r.KeylessPrompt().then(t=>({default:t.KeylessPrompt}))),O=(0,n.lazy)(()=>r.PricingTable().then(t=>({default:t.PricingTable}))),U=(0,n.lazy)(()=>r.APIKeys().then(t=>({default:t.APIKeys}))),E=(0,n.lazy)(()=>r.Checkout().then(t=>({default:t.Checkout}))),P=(0,n.lazy)(()=>r.PlanDetails().then(t=>({default:t.PlanDetails}))),C=(0,n.lazy)(()=>r.OAuthConsent().then(t=>({default:t.OAuthConsent}))),I=(0,n.lazy)(()=>r.SessionTasks().then(t=>({default:t.SessionTask}))),T=async t=>{var e;return null===(e=r[t])||void 0===e?void 0:e.call(r)},R={SignIn:a,SignUp:u,UserButton:d,UserProfile:p,UserVerification:l,OrganizationSwitcher:y,OrganizationList:w,OrganizationProfile:m,CreateOrganization:_,SignInModal:s,SignUpModal:h,UserProfileModal:f,OrganizationProfileModal:g,CreateOrganizationModal:v,UserVerificationModal:c,GoogleOneTap:o,Waitlist:b,WaitlistModal:S,BlankCaptchaModal:k,PricingTable:O,Checkout:E,PlanDetails:P,APIKeys:U,OAuthConsent:C}},56556:function(t,e,i){"use strict";i.d(e,{$9:()=>x,Jk:()=>b,Ni:()=>A,P3:()=>S,_:()=>O,mA:()=>k,oL:()=>w}),i(50725);var n=i(79109),r=i(81188),a=i(69144),s=i(7508);let o=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,11576)).then(t=>({default:t.CoreClerkContextWrapper}))),l=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,11576)).then(t=>({default:t.EnvironmentProvider}))),c=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,11576)).then(t=>({default:t.OptionsProvider}))),u=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,39541)).then(t=>({default:t.AppearanceProvider}))),h=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,24676)).then(t=>({default:t.VirtualRouter}))),d=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,81201)).then(t=>({default:t.InternalThemeProvider}))),p=(0,a.lazy)(()=>i.e("573").then(i.bind(i,26560)).then(t=>({default:t.StyleCacheProvider}))),f=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,64750)).then(t=>({default:t.Portal}))),_=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,64750)).then(t=>({default:t.VirtualBodyRootPortal}))),v=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,2672)).then(t=>({default:t.FlowMetadataProvider}))),m=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,56459)).then(t=>({default:t.Modal}))),g=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,81102)).then(t=>({default:t.Drawer.Root}))),y=(0,a.lazy)(()=>Promise.all([i.e("344"),i.e("200"),i.e("573")]).then(i.bind(i,81102)).then(t=>({default:t.Drawer.Overlay}))),w=(0,a.lazy)(()=>Promise.all([i.e("200"),i.e("573"),i.e("211")]).then(i.bind(i,66703)).then(t=>({default:t.OrganizationSwitcherPrefetch}))),b=t=>{var e;return(0,n.tZ)(p,{nonce:t.options.nonce,cssLayerName:null===(e=t.options.appearance)||void 0===e?void 0:e.cssLayerName,children:(0,n.tZ)(o,{clerk:t.clerk,children:(0,n.tZ)(l,{value:t.environment,children:(0,n.tZ)(c,{value:t.options,children:t.children})})})})},S=t=>{var e;return(null==t?void 0:null===(e=t.componentProps)||void 0===e?void 0:e.routing)==="virtual"&&(0,r.x9)('routing="virtual"','Use routing="hash" instead.'),(0,n.tZ)(u,{globalAppearance:t.globalAppearance,appearanceKey:t.appearanceKey,appearance:t.componentAppearance,children:(0,n.tZ)(f,{node:t.node,component:s.NQ[t.componentName],props:t.componentProps,componentName:t.componentName})})},k=t=>(0,n.tZ)(a.Suspense,{fallback:"",children:(0,n.tZ)(u,{globalAppearance:t.globalAppearance,appearanceKey:t.appearanceKey,appearance:t.componentAppearance,children:(0,n.tZ)(v,{flow:t.flowName||"",children:(0,n.tZ)(d,{children:(0,n.tZ)(m,{id:t.modalId,style:t.modalStyle,handleClose:t.onClose,containerSx:t.modalContainerSx,contentSx:t.modalContentSx,canCloseModal:t.canCloseModal,children:t.startPath?(0,n.tZ)(a.Suspense,{children:(0,n.tZ)(h,{startPath:t.startPath,onExternalNavigate:t.onExternalNavigate,children:t.children})}):t.children})})})})}),A=t=>(0,n.tZ)(a.Suspense,{fallback:"",children:(0,n.tZ)(h,{startPath:"",children:(0,n.tZ)(u,{globalAppearance:t.globalAppearance,appearanceKey:t.appearanceKey,appearance:t.componentAppearance,children:(0,n.tZ)(v,{flow:t.flowName||"",children:(0,n.tZ)(d,{children:(0,n.BX)(g,{open:t.open,onOpenChange:t.onOpenChange,strategy:t.portalId||t.portalRoot?"absolute":"fixed",portalProps:{id:t.portalId?t.portalId:void 0,root:t.portalRoot?t.portalRoot:void 0},children:[(0,n.tZ)(y,{}),t.children]})})})})})}),x=t=>(0,n.tZ)(a.Suspense,{children:(0,n.tZ)(h,{startPath:"",children:(0,n.tZ)(u,{globalAppearance:t.globalAppearance,appearanceKey:"impersonationFab",children:t.children})})}),O=t=>(0,n.tZ)(u,{globalAppearance:t.globalAppearance,appearanceKey:"oneTap",appearance:t.componentAppearance,children:(0,n.tZ)(_,{startPath:t.startPath,component:s.NQ.GoogleOneTap,props:t.componentProps,componentName:"GoogleOneTap"})})},89576:function(t,e,i){"use strict";i.d(e,{L:()=>a,v:()=>r}),i(28419);var n=i(82358);function r(t){let e=["redirectUrl","afterSignInUrl","afterSignUpUrl","after_sign_in_url","after_sign_up_url"],i=Object.keys(t).find(t=>e.includes(t));i&&t[i]&&n.k.warnOnce('Clerk: The prop "'.concat(i,'" is deprecated and should be replaced with the new "fallbackRedirectUrl" or "forceRedirectUrl" props instead. Learn more: https://clerk.com/docs/guides/custom-redirects#redirect-url-props'))}function a(t,e,i,r){e&&r&&n.k.warnOnce('Clerk: The "'.concat(t,'" prop ("').concat(e,'") has priority over the legacy "').concat(i,'" (or "redirectUrl") ("').concat(r,'"), which will be completely ignored in this case. "').concat(i,'" (or "redirectUrl" prop) should be replaced with the new "fallbackRedirectUrl" or "forceRedirectUrl" props instead. Learn more: https://clerk.com/docs/guides/custom-redirects#redirect-url-props'))}},73837:function(t,e,i){"use strict";async function n(t,e,i,n,r){if(!t.client||!n.popup)return;let a=function(t){if(!t)return"";let e=t.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return"https://".concat(e)}(t.frontendApi),{redirectUrl:s}=n,o=new URL(s);o.searchParams.set("sign_in_force_redirect_url",n.redirectUrlComplete),o.searchParams.set("sign_up_force_redirect_url",n.redirectUrlComplete),o.searchParams.set("intent",e);let l=t.buildUrlWithAuth(o.toString()),c=t.buildUrlWithAuth("".concat(a,"/popup-callback")),u=t.buildUrlWithAuth("".concat(a,"/popup-callback?return_url=").concat(encodeURIComponent(l))),h=async e=>{if(e.origin!==a)return;let i=!1;if(e.data.session){var r,s;if(!(null===(r=t.client)||void 0===r?void 0:r.sessions.find(t=>t.id===e.data.session)))try{await (null===(s=t.client)||void 0===s?void 0:s.reload())}catch(t){console.error(t)}await t.setActive({session:e.data.session,redirectUrl:n.redirectUrlComplete}),i=!0}else e.data.return_url&&(t.navigate(e.data.return_url),i=!0);i&&window.removeEventListener("message",h)};window.addEventListener("message",h),await i({...n,redirectUrlComplete:c,redirectUrl:u},r)}i.d(e,{G:()=>n}),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223),i(50725),i(87945),i(56113),i(64310)},97976:function(t,e,i){"use strict";i.d(e,{E:()=>p});var n=i(17431);i(50725),i(92037),i(79876),i(65223),i(64310);var r=i(40748),a=i(83935);let s=t=>!!["crashed","undefined_error","102","103","104","106","110600","300","600"].find(e=>t.startsWith(e));async function o(){return window.turnstile||await l().catch(()=>{throw{captchaError:"captcha_script_failed_to_load"}}),window.turnstile}async function l(){try{return await (0,r.v)("https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit",{defer:!0})}catch(t){throw console.warn("Clerk: Failed to load the CAPTCHA script from Cloudflare. If you see a CSP error in your browser, please add the necessary CSP rules to your app. Visit https://clerk.com/docs/security/clerk-csp for more information."),t}}function c(t){try{let e=t.getAttribute("data-cl-theme")||void 0,i=t.getAttribute("data-cl-language")||void 0,n=t.getAttribute("data-cl-size")||void 0;return{theme:e,language:i,size:n}}catch{return{theme:void 0,language:void 0,size:void 0}}}let u=async t=>{let e,i,n,r;let{siteKey:l,widgetType:u,invisibleSiteKey:h}=t,{modalContainerQuerySelector:d,modalWrapperQuerySelector:p,closeModal:f,openModal:_}=t,v=await o(),m=[],g="",y="",w=l,b=0,S=null,k="invisible";if(d&&p){S=u,r=d,k="modal";try{await (null==_?void 0:_())}catch{throw{captchaError:"modal_component_not_ready"}}let t=await new Promise(t=>{if(document.querySelector(d))return t(document.querySelector(d));let e=new MutationObserver(()=>{document.querySelector(d)&&(e.disconnect(),t(document.querySelector(d)))});e.observe(document.body,{childList:!0,subtree:!0})});if(t){let{theme:r,language:a,size:s}=c(t);e=r,n=a,i=s}}if(!r&&"smart"===u){let t=document.getElementById(a.M);if(t){k="smart",S="smart",r="#".concat(a.M),t.style.maxHeight="0";let{theme:s,language:o,size:l}=c(t);e=s,n=o,i=l}else console.error("Cannot initialize Smart CAPTCHA widget because the `clerk-captcha` DOM element was not found; falling back to Invisible CAPTCHA widget. If you are using a custom flow, visit https://clerk.com/docs/custom-flows/bot-sign-up-protection for instructions")}if(!r){k="invisible",w=h,S="invisible",r=".".concat(a.G);let t=document.createElement("div");t.classList.add(a.G),t.style.display="none",document.body.appendChild(t)}let A=async()=>new Promise((o,l)=>{try{let c=v.render(r,{sitekey:w,appearance:"interaction-only",theme:e||"auto",size:i||"normal",language:n||"auto",action:t.action,retry:"never","refresh-expired":"auto",callback:function(t){null==f||f(),o([t,c])},"before-interactive-callback":()=>{if(p){let t=document.querySelector(p);null==t||t.style.setProperty("visibility","visible"),null==t||t.style.setProperty("pointer-events","all")}else{let t=document.getElementById(a.M);t&&(t.style.maxHeight="unset",t.style.minHeight="compact"===i?"140px":"68px",t.style.marginBottom="1.5rem")}},"error-callback":function(t){if(m.push(t),b<2&&s(t.toString())){setTimeout(()=>{v.reset(c),b++},250);return}l([m.join(","),c])},"unsupported-callback":function(){return l(["This browser is not supported by the CAPTCHA.",c]),!0}})}catch(t){l([t,void 0])}});try{[g,y]=await A(),v.remove(y)}catch(i){let[t,e]=i;throw e&&v.remove(e),{captchaError:t}}finally{if("modal"===k&&(null==f||f()),"invisible"===k){let t=document.querySelector(".".concat(a.G));t&&document.body.removeChild(t)}if("smart"===k){let t=document.getElementById(a.M);t&&(t.style.maxHeight="0",t.style.minHeight="unset",t.style.marginBottom="unset")}}return{captchaToken:g,captchaWidgetType:S}},h=t=>u(t),d=t=>{let e=t.__unstable__environment,i=e?e.displayConfig.captchaProvider:"turnstile";return{captchaSiteKey:e?e.displayConfig.captchaPublicKey:null,captchaWidgetType:e?e.displayConfig.captchaWidgetType:null,captchaProvider:i,captchaPublicKeyInvisible:e?e.displayConfig.captchaPublicKeyInvisible:null,canUseCaptcha:e?e.userSettings.signUp.captcha_enabled&&t.isStandardBrowser:null}};class p{async invisible(t){let{captchaSiteKey:e,canUseCaptcha:i,captchaPublicKeyInvisible:n}=d(this.clerk);return i&&e&&n?{...await h({siteKey:n,invisibleSiteKey:n,widgetType:"invisible",captchaProvider:"turnstile",action:null==t?void 0:t.action}).catch(t=>t.captchaError?{captchaError:t.captchaError}:{captchaError:(null==t?void 0:t.message)||t||"unexpected_captcha_error"}),captchaAction:null==t?void 0:t.action}:{captchaError:"captcha_unavailable",captchaAction:null==t?void 0:t.action}}async managedOrInvisible(t){let{captchaSiteKey:e,canUseCaptcha:i,captchaWidgetType:n,captchaProvider:r,captchaPublicKeyInvisible:a}=d(this.clerk);if(i&&e&&a){let i=await h({siteKey:e,widgetType:n,invisibleSiteKey:a,captchaProvider:r,...t}).catch(e=>e.captchaError?{captchaError:e.captchaError}:(null==t?void 0:t.action)==="verify"?{captchaError:(null==e?void 0:e.message)||e||"unexpected_captcha_error"}:void 0);return(null==t?void 0:t.action)==="verify"?{...i,captchaAction:"verify"}:i}return(null==t?void 0:t.action)==="verify"?{captchaError:"captcha_unavailable",captchaAction:null==t?void 0:t.action}:{}}async managedInModal(t){return this.managedOrInvisible({modalWrapperQuerySelector:"#cl-modal-captcha-wrapper",modalContainerQuerySelector:"#cl-modal-captcha-container",openModal:()=>this.clerk.__internal_openBlankCaptchaModal(),closeModal:()=>this.clerk.__internal_closeBlankCaptchaModal(),action:null==t?void 0:t.action})}constructor(t){(0,n._)(this,"clerk",void 0),this.clerk=t}}},83935:function(t,e,i){"use strict";i.d(e,{G:()=>r,M:()=>n});let n="clerk-captcha",r="clerk-invisible-captcha"},65756:function(t,e,i){"use strict";i.d(e,{v:()=>r}),i(28419);var n=i(24029);let r=t=>{let{signUp:e,verifyEmailPath:i,verifyPhonePath:r,continuePath:a,navigate:s,handleComplete:o,redirectUrl:l="",redirectUrlComplete:c="",oidcPrompt:u}=t;if("complete"===e.status)return o&&o();if("missing_requirements"===e.status){var h,d;if(e.missingFields.some(t=>"saml"===t||"enterprise_sso"===t))return e.authenticateWithRedirect({strategy:"enterprise_sso",redirectUrl:l,redirectUrlComplete:c,continueSignUp:!0,oidcPrompt:u});let t=(0,n.z3)();if((null===(h=e.unverifiedFields)||void 0===h?void 0:h.includes("email_address"))&&i)return s(i,{searchParams:t});if((null===(d=e.unverifiedFields)||void 0===d?void 0:d.includes("phone_number"))&&r)return s(r,{searchParams:t});if(a)return s(a,{searchParams:t})}}},90750:function(t,e,i){"use strict";i.d(e,{C:()=>r}),i(45261),i(70957),i(24551),i(22349),i(65223);var n=i(65027);function r(t){let{pageSize:e,initialPage:i,...r}=t||{},a=null!=e?e:10;return new URLSearchParams({...(0,n.hF)(r),limit:a+"",offset:((null!=i?i:1)-1)*a+""})}},46630:function(t,e,i){"use strict";function n(t){let e=new Date(t||new Date);return e instanceof Date&&!isNaN(e.getTime())?e:new Date}i.d(e,{V:()=>n})},24029:function(t,e,i){"use strict";i.d(e,{XV:()=>s,xy:()=>o,z3:()=>l}),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223),i(56113);var n=i(67509),r=i(80753);let a=["__clerk_status","__clerk_created_session","__clerk_invitation_token","__clerk_ticket","__clerk_modal_state","__clerk_handshake","__clerk_handshake_nonce","__clerk_help",n.A,r.cD,r.w9,r.cc];function s(t){return new URL(window.location.href).searchParams.get(t)||null}function o(t){let e=new URL(window.location.href);e.searchParams.has(t)&&(e.searchParams.delete(t),window.history.replaceState(window.history.state,"",e))}function l(t){let e=new URLSearchParams(window.location.search),i=t||new URLSearchParams;for(let t of a){let n=e.get(t);n&&i.set(t,n)}return i}},40065:function(t,e,i){"use strict";function n(t){return t.split("").map(t=>t.charCodeAt(0).toString(16).padStart(2,"0")).join("")}i.d(e,{N:()=>n}),i(56113)},26917:function(t,e,i){"use strict";i.d(e,{wO:()=>j.wO,To:()=>O.To,Sk:()=>n.Sk,_f:()=>L,n$:()=>h,vl:()=>D.v,WW:()=>O.WW,$1:()=>O.$1,pU:()=>O.pU,z9:()=>O.z9,T7:()=>n.T7,XV:()=>k.XV,TI:()=>F,So:()=>c,OY:()=>m,QD:()=>O.QD,QO:()=>x,oH:()=>W,M:()=>O.M,R6:()=>o,bj:()=>s,sW:()=>d,ZE:()=>j.ZE,un:()=>O.un,hb:()=>O.hb,OR:()=>T,e1:()=>M,Os:()=>O.Os,u9:()=>O.u9,HV:()=>a,LM:()=>I,aR:()=>g,bX:()=>z,vx:()=>l.v,Jx:()=>U,KR:()=>p,$0:()=>j.$0,Ly:()=>j.Ly,l1:()=>O.l1,eT:()=>O.eT,f:()=>u,KV:()=>O.KV,Yh:()=>_,dR:()=>j.dR,jv:()=>O.jv,w$:()=>N,C3:()=>j.C3,M8:()=>j.M8,xy:()=>k.xy,e:()=>v,ir:()=>E,f0:()=>D.f,RM:()=>S,VZ:()=>b,eg:()=>y,le:()=>f});var n=i(55809);let r=()=>{let t=!1,e=()=>t=!0;return{startListening:()=>{window.addEventListener("beforeunload",e),window.addEventListener(n.IK,e)},stopListening:()=>{window.removeEventListener("beforeunload",e),window.removeEventListener(n.IK,e)},isUnloading:()=>t}},a=function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!t)return{track:async t=>{await t()},isUnloading:()=>!1};let e=r();return{track:async t=>{e.startListening(),await t(),e.stopListening()},isUnloading:e.isUnloading}},s=t=>({amount:t.amount,amountFormatted:t.amount_formatted,currency:t.currency,currencySymbol:t.currency_symbol}),o=t=>{let e={grandTotal:s(t.grand_total),subtotal:s(t.subtotal),taxTotal:s(t.tax_total)};return"total_due_now"in t&&(e.totalDueNow=s(t.total_due_now)),"credit"in t&&(e.credit=s(t.credit)),"past_due"in t&&(e.pastDue=s(t.past_due)),e};var l=i(65756);let c=(t,e)=>!!(t.session&&(null==e?void 0:e.authConfig.singleSessionMode)),u=t=>!t.user,h=t=>!t.organization,d=(t,e)=>!(null==e?void 0:e.organizationSettings.enabled),p=(t,e)=>!(null==e?void 0:e.commerceSettings.billing.enabled),f=(t,e)=>(null==e?void 0:e.commerceSettings.billing.hasPaidOrgPlans)||!1,_=(t,e)=>(null==e?void 0:e.commerceSettings.billing.hasPaidUserPlans)||!1,v=(t,e)=>{var i;return!(null==e?void 0:null===(i=e.apiKeysSettings)||void 0===i?void 0:i.enabled)};i(56113),i(87945);let m=t=>{let{regex:e}=t;return t=>{let{urlWithParam:i,entity:n}=t,r=e.exec(i);if(r){let t=r[1];if(t in n){let e=n[t];return i.replace(r[0],e)}}return i}};function g(t){let{localPart:e,frontendApi:i}=t,n=i?i.replace("clerk.",""):"clerk.com";return"".concat(e,"@").concat(n)}function y(t){return i.g.btoa(t)}function w(t){var e;return e=t.replace(/_/g,"/").replace(/-/g,"+"),decodeURIComponent(i.g.atob(e).split("").map(t=>"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)).join(""))}function b(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.errors&&!!t.errors.find(t=>t.code===e)}i(50886);let S=(0,i(73531).t5)({packageName:"@clerk/clerk-js"});var k=i(24029);i(40065),i(28419),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223);var A=i(10290);i(64310);let x=t=>{if((t||"").includes("gravatar")||(t||"").includes("avatar_placeholder"))return!0;try{let e=new URL(t).pathname.replace("/",""),i=(0,A.S)(e),n=JSON.parse(i);return"default"===n.type}catch{return!1}};var O=i(76971);function U(t){let e=(t||"").split("."),[i,n,r]=e;if(3!==e.length||!i||!n||!r)throw Error("JWT could not be decoded");let a=JSON.parse(w(n)),s={__raw:t};return Object.keys(a).forEach(t=>{s[t]=a[t]}),{encoded:{header:i,payload:n,signature:r},header:JSON.parse(w(i)),claims:s}}function E(t){return"string"==typeof t&&t.startsWith("org_")}i(92037),i(22073),i(79876);var P=i(72708),C=i(45100);let I=()=>{if(!(0,P._f)())return{onPageFocus:C.ZT};let t={focus:[]};return window.addEventListener("focus",()=>{"visible"===document.visibilityState&&t.focus.forEach(t=>t())}),{onPageFocus:e=>{t.focus.push(e)}}};function T(t){return"function"==typeof t?t():t}i(59853);var R=i(80753);let M=t=>{let{base:e,path:i}=t;return i?e+i:e},N=()=>{var t;let e=null!==(t=(0,k.XV)(R.v_))&&void 0!==t?t:"";return e?JSON.parse(atob(e)):null},z=t=>{let{url:e,startPath:i="/user",currentPath:n="",componentName:r,socialProvider:a=""}=t,s=y(JSON.stringify({path:n.replace(/CLERK-ROUTER\/VIRTUAL\/.*\//,"")||"",componentName:r,startPath:i,socialProvider:a})),o=new URL(e),l=o.searchParams;return l.set(R.v_,s),o.search=l.toString(),o.toString()};var D=i(17672);function L(){return void 0!==globalThis.document}function W(){return L()&&globalThis.document.hasFocus()}function F(){return L()&&window.self!==window.top&&!window.frameElement}i(64728);var j=i(35274)},22073:function(t,e,i){"use strict";i.d(e,{L:()=>r});var n=i(24152);let r=t=>{let{routing:e,path:i}=t;return i&&!e?{routing:"path",path:i}:"path"!==e&&i?(0,n.PQ)(e):{routing:e,path:i}}},93367:function(t,e,i){"use strict";i.d(e,{GH:()=>u,N7:()=>h,YI:()=>o,ku:()=>p,pr:()=>s,t1:()=>l,zQ:()=>f});var n=i(17431);i(50886),i(73588),i(75184),i(12100),i(29646),i(67581),i(63782),i(14311),i(72547),i(86936),i(20889),i(54357),i(86775),i(6763),i(65223),i(87945),i(56113),i(50725),i(92037);var r=i(73531);class a{static encode(t){return btoa(String.fromCharCode(...new Uint8Array(t))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}static decode(t){let e=atob(t.replace(/-/g,"+").replace(/_/g,"/")),i=e.length,n=new Uint8Array(i);for(let t=0;t<i;t++)n[t]=e.charCodeAt(t);return n.buffer}}async function s(t){try{let e=await navigator.credentials.create({publicKey:t});if(!e)return{error:new r.RK("Browser failed to create credential",{code:"passkey_registration_failed"}),publicKeyCredential:null};return{publicKeyCredential:e,error:null}}catch(t){var e;return{error:"InvalidStateError"===(e=t).name?new r.RK(e.message,{code:"passkey_already_exists"}):"NotAllowedError"===e.name?new r.RK(e.message,{code:"passkey_registration_cancelled"}):c(e),publicKeyCredential:null}}}let o=new class{__abort(){if(!this.controller)return;let t=Error();t.name="AbortError",this.controller.abort(t)}createAbortSignal(){this.__abort();let t=new AbortController;return this.controller=t,t.signal}abort(){this.__abort(),this.controller=void 0}constructor(){(0,n._)(this,"controller",void 0)}};async function l(t){let{publicKeyOptions:e,conditionalUI:i}=t;try{let t=await navigator.credentials.get({publicKey:e,mediation:i?"conditional":"optional",signal:o.createAbortSignal()});if(!t)return{error:new r.RK("Browser failed to get credential",{code:"passkey_retrieval_failed"}),publicKeyCredential:null};return{publicKeyCredential:t,error:null}}catch(t){var n;return{error:"NotAllowedError"===(n=t).name?new r.RK(n.message,{code:"passkey_retrieval_cancelled"}):c(n),publicKeyCredential:null}}}function c(t){return"AbortError"===t.name?new r.RK(t.message,{code:"passkey_operation_aborted"}):"SecurityError"===t.name?new r.RK(t.message,{code:"passkey_invalid_rpID_or_domain"}):t}function u(t){let e=v(t.user.id),i=v(t.challenge),n=(t.excludeCredentials||[]).map(t=>({...t,id:v(t.id)}));return{...t,excludeCredentials:n,challenge:i,user:{...t.user,id:e}}}function h(t){let e=v(t.challenge),i=(t.allowCredentials||[]).map(t=>({...t,id:v(t.id)}));return{...t,allowCredentials:i,challenge:e}}function d(t){return{type:t.type,id:t.id,rawId:_(t.rawId),authenticatorAttachment:t.authenticatorAttachment}}function p(t){let e=t.response;return{...d(t),response:{clientDataJSON:_(e.clientDataJSON),attestationObject:_(e.attestationObject),transports:e.getTransports()}}}function f(t){let e=t.response;return{...d(t),response:{clientDataJSON:_(e.clientDataJSON),authenticatorData:_(e.authenticatorData),signature:_(e.signature),userHandle:e.userHandle?_(e.userHandle):null}}}let _=a.encode.bind(a),v=a.decode.bind(a)},8755:function(t,e,i){"use strict";i.d(e,{W:()=>a}),i(87945),i(56113),i(80194),i(25800),i(65223);let n=t=>{let e;if(t.allowed_special_characters){let i=t.allowed_special_characters.replace("[","\\[");i=i.replace("]","\\]"),e=new RegExp("[".concat(i,"]"))}else e=/[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]/;return(t,i)=>{let{minLength:n,maxLength:r}=i;return{max_length:t.length<r,min_length:t.length>=n,require_numbers:/\d/.test(t),require_lowercase:/[a-z]/.test(t),require_uppercase:/[A-Z]/.test(t),require_special_char:e.test(t)}}},r=(t,e)=>{let{max_length:i,min_length:r,require_special_char:a,require_lowercase:s,require_numbers:o,require_uppercase:l}=e,c=n(e)(t,{maxLength:e.max_length,minLength:e.min_length}),u={max_length:i,min_length:r,require_special_char:a,require_lowercase:s,require_numbers:o,require_uppercase:l},h=new Map;for(let t in u)u[t]&&(c[t]||h.set(t,!0));return Object.freeze(Object.fromEntries(h))},a=t=>e=>r(e,t)},30529:function(t,e,i){"use strict";i.d(e,{z:()=>o});var n=i(45100);i(50725),i(65223);let r=()=>Promise.all([Promise.all([i.e("708"),i.e("200")]).then(i.bind(i,50384)),i.e("325").then(i.bind(i,47706))]).then(t=>{let[e,i]=t,{zxcvbnOptions:n,zxcvbn:r}=e,{dictionary:a,adjacencyGraphs:s}=i;return n.setOptions({dictionary:{...a},graphs:s}),r});var a=i(8755);let s=t=>{let{min_zxcvbn_strength:e,onResult:i}=t;return t=>n=>{let r=t(n);return(null==i||i(r),r.score>=e&&r.score<3)?{state:"pass",keys:["unstable__errors.zxcvbn.couldBeStronger"],result:r}:r.score>=e?{state:"excellent",result:r}:{state:"fail",keys:["unstable__errors.zxcvbn.notEnough",...r.feedback.suggestions.map(t=>"unstable__errors.zxcvbn.suggestions.".concat(t))],result:r}}},o=(t,e)=>{let{onValidation:i=n.ZT,onValidationComplexity:o=n.ZT}=e||{},{show_zxcvbn:l,validatePassword:c}=t,u=(0,a.W)(t),h=s(t),d={};return(t,e)=>{let{onValidation:n=i,onValidationComplexity:a=o}=e||{};if(!c)return;let s=u(t);a(0===Object.keys(s).length),d={...d,complexity:s},l&&r().then(e=>{let i=h(e)(t);n({...d={...d,strength:i},strength:i})}),(!d.complexity||0!==Object.keys(d.complexity).length||!l)&&n(d)}}},59853:function(t,e,i){"use strict";i.d(e,{R:()=>r}),i(80194),i(25800),i(56113),i(87945);let n=RegExp("/{1,}","g");function r(t,e){return[t,e].filter(t=>t).join("/").replace(n,"/")}},17672:function(t,e,i){"use strict";i.d(e,{f:()=>r,v:()=>n}),i(45261),i(70957),i(24551),i(22349),i(65223),i(79876);let n=t=>{let e={};return new URLSearchParams(t).forEach((t,i)=>{if(i in e){let n=e[i];Array.isArray(n)?n.push(t):e[i]=[n,t]}else e[i]=t}),e},r=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||!t||"object"!=typeof t)return"";let i=new URLSearchParams;return Object.keys(t).forEach(n=>{let r=e.keyEncoder?e.keyEncoder(n):n,a=t[n];if(Array.isArray(a))a.forEach(t=>void 0!==t&&i.append(r,t||""));else{if(void 0===a)return;"object"==typeof a&&null!==a?i.append(r,JSON.stringify(a)):i.append(r,String(null!=a?a:""))}}),i.toString()}},51607:function(t,e,i){"use strict";i.d(e,{O:()=>S});var n=i(34056),r=i(12499),a=i(17970),s=i(91412),o=i(17431);i(65223),i(45261),i(70957),i(24551),i(22349),i(28419),i(93008);var l=t=>Object.entries(t).reduce((t,e)=>{let[i,n]=e;return null!=n&&(t[i]=n),t},{}),c=(t,e)=>{let i={};for(let n in t)i[n]=e(t[n],n);return i},u=(t,e)=>{let i={};for(let n in t)t[n]&&e(t[n])&&(i[n]=t[n]);return i};i(64310);var h=i(65027),d=i(89576),p=i(76971),f=new WeakSet,_=new WeakSet,v=new WeakSet,m=new WeakSet,g=new WeakSet,y=new WeakSet,w=new WeakSet,b=new WeakMap;class S{getAfterSignInUrl(){return(0,a._)(this,m,O).call(this,"signIn")}getAfterSignUpUrl(){return(0,a._)(this,m,O).call(this,"signUp")}getPreservedSearchParams(){return(0,a._)(this,f,k).call(this,(0,a._)(this,_,A).call(this))}toSearchParams(){return(0,a._)(this,f,k).call(this,(0,a._)(this,v,x).call(this))}constructor(t,e={},i={},n){(0,s._)(this,f),(0,s._)(this,_),(0,s._)(this,v),(0,s._)(this,m),(0,s._)(this,g),(0,s._)(this,y),(0,s._)(this,w),(0,o._)(this,"options",void 0),(0,o._)(this,"fromOptions",void 0),(0,o._)(this,"fromProps",void 0),(0,o._)(this,"fromSearchParams",void 0),(0,o._)(this,"mode",void 0),(0,r._)(this,b,{writable:!0,value:t=>{var e;return u(t,(0,p.QD)(null===(e=this.options)||void 0===e?void 0:e.allowedRedirectOrigins,window.location.origin))}}),this.options=t,this.fromOptions=(0,a._)(this,g,U).call(this,t||{}),this.fromProps=(0,a._)(this,g,U).call(this,e||{}),this.fromSearchParams=(0,a._)(this,y,E).call(this,i||{}),this.mode=n}}function k(t){return new URLSearchParams(l(Object.fromEntries(Object.entries(t).map(t=>{let[e,i]=t;return[(0,h.a1)(e),i]}))))}function A(){return Object.fromEntries(Object.entries({...this.fromSearchParams}).filter(t=>{let[e]=t;return S.preserved.includes(e)}))}function x(){let t=this.fromSearchParams.signUpForceRedirectUrl||this.fromProps.signUpForceRedirectUrl||this.fromOptions.signUpForceRedirectUrl,e=this.fromSearchParams.signUpFallbackRedirectUrl||this.fromProps.signUpFallbackRedirectUrl||this.fromOptions.signUpFallbackRedirectUrl,i=this.fromSearchParams.signInForceRedirectUrl||this.fromProps.signInForceRedirectUrl||this.fromOptions.signInForceRedirectUrl,n=this.fromSearchParams.signInFallbackRedirectUrl||this.fromProps.signInFallbackRedirectUrl||this.fromOptions.signInFallbackRedirectUrl,r=this.fromSearchParams.afterSignInUrl||this.fromProps.afterSignInUrl||this.fromOptions.afterSignInUrl,a={signUpForceRedirectUrl:t,signUpFallbackRedirectUrl:e,signInFallbackRedirectUrl:n,signInForceRedirectUrl:i,afterSignInUrl:r,afterSignUpUrl:this.fromSearchParams.afterSignUpUrl||this.fromProps.afterSignUpUrl||this.fromOptions.afterSignUpUrl,redirectUrl:this.fromSearchParams.redirectUrl||this.fromProps.redirectUrl||this.fromOptions.redirectUrl};return t&&delete a.signUpFallbackRedirectUrl,i&&delete a.signInFallbackRedirectUrl,a}function O(t){let e,i;let n="".concat(t,"ForceRedirectUrl"),r="".concat(t,"FallbackRedirectUrl"),a="after".concat(t[0].toUpperCase()).concat(t.slice(1),"Url");(i=this.fromSearchParams[n]||this.fromProps[n]||this.fromOptions[n])&&(e=n),i||(i=this.fromSearchParams.redirectUrl),i&&(e="redirectUrl"),i||(i=this.fromSearchParams[r]||this.fromProps[r]||this.fromOptions[r]),i&&(e=r);let s=this.fromSearchParams[a]||this.fromProps[a]||this.fromProps.redirectUrl||this.fromOptions[a];return((0,d.L)(e,i,a,s),i||(i=s),i||"modal"!==this.mode)?i||"/":window.location.href}function U(t){(0,d.v)(t);let e={};return S.keys.forEach(i=>{e[i]=t[i]}),c((0,n._)(this,b).call(this,(0,a._)(this,w,P).call(this,u(e,Boolean))),t=>t.toString())}function E(t){(0,d.v)(t);let e={};return S.keys.forEach(i=>{t instanceof URLSearchParams?e[i]=t.get((0,h.a1)(i)):e[i]=t[(0,h.a1)(i)]}),c((0,n._)(this,b).call(this,(0,a._)(this,w,P).call(this,u(e,Boolean))),t=>t.toString())}function P(t){return c(t,t=>(0,p.R9)(t,window.location.origin))}(0,o._)(S,"keys",["signInForceRedirectUrl","signInFallbackRedirectUrl","signUpForceRedirectUrl","signUpFallbackRedirectUrl","afterSignInUrl","afterSignUpUrl","redirectUrl"]),(0,o._)(S,"preserved",["redirectUrl"])},26310:function(t,e,i){"use strict";function n(t){let{unsafeMetadata:e}={...t},i=e?"object"==typeof e?JSON.stringify(e):e:"";return{...t,...e?{unsafeMetadata:i}:{}}}i.d(e,{q:()=>n})},76971:function(t,e,i){"use strict";i.d(e,{R9:()=>S,u9:()=>_,WW:()=>I,eT:()=>x,l1:()=>w,$1:()=>R,pU:()=>A,z9:()=>O,KV:()=>m,jv:()=>b,QD:()=>T,M:()=>g,hb:()=>U,un:()=>p,To:()=>C,Os:()=>y}),i(65223),i(87945),i(56113),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(56711),i(28419),i(79876),i(92037),i(64310);var n=i(54763),r={toRegexp:t=>{try{return n(t)}catch(e){throw Error("Invalid pattern: ".concat(t,".\nConsult the documentation of glob-to-regexp here: https://www.npmjs.com/package/glob-to-regexp.\n").concat(e.message))}}},a=i(41402),s=i(82358),o=i(65027),l=i(94944),c=i(59853),u=i(17672);let h="http://clerk-dummy",d=["javascript:"],{isDevOrStagingUrl:p}=(0,a.MY)(),f=new Map;function _(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.hostname;if(!t)return!1;let e=f.get(t);return void 0===e&&(e=(0,l.A5)(t)||(0,l.LQ)(t),f.set(t,e)),e}function v(t){return t.replace("clerk.","")}function m(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{base:i,hashPath:n,hashSearch:r,searchParams:a,hashSearchParams:s,...l}=t,d="",p=new URL(i||"","undefined"!=typeof window&&window.location?window.location.href:"http://react-native-fake-base-url");if(a instanceof URLSearchParams&&a.forEach((t,e)=>{null!=t&&p.searchParams.set((0,o.a1)(e),t)}),Object.assign(p,l),n||r||s){let t=new URL(h+p.hash.substring(1));for(let[e,i]of(t.pathname=(0,c.R)(t.pathname,n||""),Object.entries((0,u.v)(r||""))))t.searchParams.append(e,i);if(s)for(let e of Array.isArray(s)?s:[s])(e instanceof URLSearchParams||"object"==typeof e)&&new URLSearchParams(e).forEach((e,i)=>{null!=e&&t.searchParams.set((0,o.a1)(i),e)});let e=t.href.replace(h,"");"/"!==e&&(p.hash=e)}let{stringify:f,skipOrigin:_}=e;return f?_?p.href.replace(p.origin,""):p.href:p}function g(t){return(t=new URL(t.toString(),window.location.origin)).href.replace(t.origin,"")}let y=t=>(t||"").replace(/\/+$/,""),w=t=>{let{externalAccount:e}=t.verifications;return!!e.error};function b(t){if(!t)return!1;try{return new URL(t),!0}catch{return!1}}function S(t,e){try{return new URL(t)}catch{return new URL(t,e)}}let k=[/\0/,/^\/\//,/[\x00-\x1F]/];function A(t){return!!b(t)&&"data:"===new URL(t).protocol}let x=t=>new URL(t,h).hash.startsWith("#/"),O=t=>{let e=new URL(t);if(!x(e))return e;let i=new URL(e.hash.replace("#/","/"),e.href),n=new URL([e.pathname,i.pathname].map(t=>t.split("/")).flat().filter(Boolean).join("/"),e.origin);return e.searchParams.forEach((t,e)=>{n.searchParams.set(e,t)}),i.searchParams.forEach((t,e)=>{n.searchParams.set(e,t)}),n},U=t=>t.replace(/CLERK-ROUTER\/(.*?)\//,""),E=["/oauth/authorize"],P=["/v1/verify","/v1/tickets/accept"];function C(t,e){let i=new URL(e,h),n=i.pathname,r=E.includes(n)||P.includes(n);return t===i.host&&r}function I(t){let e=new URL(t,h);return E.includes(e.pathname)}let T=(t,e)=>i=>{let n=i;if("string"==typeof n&&(n=S(n,e)),!t)return!0;let a=e===n.origin,o=!function(t){if(function(t){if(!b(t))return!1;let e=new URL(t).protocol;return d.some(t=>t===e)}(t))return!0;for(let e of k)if(e.test(t.pathname))return!0;return!1}(n)&&(a||t.map(t=>"string"==typeof t?r.toRegexp(y(t)):t).some(t=>t.test(y(n.origin))));return o||s.k.warnOnce("Clerk: Redirect URL ".concat(n," is not on one of the allowedRedirectOrigins, falling back to the default redirect URL.")),o};function R(t,e,i){if(Array.isArray(t)&&t.length)return t;let n=[];return"undefined"!=typeof window&&window.location&&n.push(window.location.origin),n.push("https://".concat(v(e))),n.push("https://*.".concat(v(e))),"development"===i&&n.push("https://".concat(e)),n}},95878:function(t,e,i){"use strict";i.d(e,{Pp:()=>n,Qm:()=>r,xC:()=>a});let n=t=>{let{firstName:e,lastName:i,name:n}=t;return n||[e,i].join(" ").trim()||""},r=t=>{let{firstName:e,lastName:i,name:n}=t;return[(e||"")[0],(i||"")[0]].join("").trim()||(n||"")[0]},a=t=>t.username?t.username:t.primaryEmailAddress?t.primaryEmailAddress.emailAddress:t.primaryPhoneNumber?t.primaryPhoneNumber.phoneNumber:t.primaryWeb3Wallet?t.primaryWeb3Wallet.web3Wallet:""},35274:function(t,e,i){"use strict";i.d(e,{wO:()=>k,dR:()=>A,bQ:()=>y,Ly:()=>g,C3:()=>x,M8:()=>w,$0:()=>b,ZE:()=>S}),i(50725);var n=i(40065),r=i(34056),a=i(12499),s=i(69661);function o(t,e){if(t!==e)throw TypeError("Private static access of wrong provenance")}function l(t,e){if(void 0===t)throw TypeError("attempted to "+e+" private static field before its declaration")}function c(t,e,i){return o(t,e),l(i,"get"),(0,s._)(t,i)}i(92037);var u=i(60543),h=i(17431);i(65223),i(79876);var d=new WeakMap,p=new WeakMap,f=new WeakMap;class _{static getInstance(){if(!c(_,_,v)){var t;t=new _,o(_,_),l(v,"set"),(0,u._)(_,v,t)}return c(_,_,v)}constructor(){if((0,a._)(this,d,{writable:!0,value:[]}),(0,a._)(this,p,{writable:!0,value:{metamask:"MetaMask",okx_wallet:"OKX Wallet"}}),(0,h._)(this,"get",t=>{var e;let i=null===(e=(0,r._)(this,d).find(e=>e.info.name===(0,r._)(this,p)[t]))||void 0===e?void 0:e.provider;return void 0!==i?i:window.ethereum}),(0,a._)(this,f,{writable:!0,value:t=>{!(0,r._)(this,d).some(e=>e.info.uuid===t.detail.info.uuid)&&(0,r._)(this,d).push(t.detail)}}),"undefined"==typeof window)return;window.addEventListener("eip6963:announceProvider",(0,r._)(this,f)),window.dispatchEvent(new Event("eip6963:requestProvider"))}}var v={writable:!0,value:null};let m=()=>_.getInstance();async function g(t){let{provider:e}=t,i=await O(e);if(!i)return"";let n=await i.request({method:"eth_requestAccounts"});return n&&n[0]||""}async function y(t){let{identifier:e,nonce:i,provider:r}=t,a=await O(r);return a?await a.request({method:"personal_sign",params:["0x".concat((0,n.N)(i)),e]}):""}async function w(){return await g({provider:"metamask"})}async function b(){return await g({provider:"coinbase_wallet"})}async function S(){return await g({provider:"okx_wallet"})}async function k(t){return await y({...t,provider:"metamask"})}async function A(t){return await y({...t,provider:"coinbase_wallet"})}async function x(t){return await y({...t,provider:"okx_wallet"})}async function O(t){return"coinbase_wallet"===t?(await Promise.all([i.e("956"),i.e("200")]).then(i.bind(i,77453)).then(t=>t.createCoinbaseWalletSDK))({preference:{options:"all"}}).getProvider():m().get(t)}},55809:function(t,e,i){"use strict";i.d(e,{IK:()=>n,Sk:()=>r,T7:()=>a}),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223);let n="clerk:beforeunload",r=["http:","https:","wails:","chrome-extension:"];function a(t){let e=new URL(t,window.location.href);window.dispatchEvent(new CustomEvent(n)),window.location.href=e.href}},11025:function(t,e,i){"use strict";i.d(e,{Z:()=>B}),i(65223),i(87945),i(56113),i(92037),i(79876);var n=function(){function t(t){var e=this;this._insertTag=function(t){var i;i=0===e.tags.length?e.insertionPoint?e.insertionPoint.nextSibling:e.prepend?e.container.firstChild:e.before:e.tags[e.tags.length-1].nextSibling,e.container.insertBefore(t,i),e.tags.push(t)},this.isSpeedy=void 0===t.speedy||t.speedy,this.tags=[],this.ctr=0,this.nonce=t.nonce,this.key=t.key,this.container=t.container,this.prepend=t.prepend,this.insertionPoint=t.insertionPoint,this.before=null}var e=t.prototype;return e.hydrate=function(t){t.forEach(this._insertTag)},e.insert=function(t){if(this.ctr%(this.isSpeedy?65e3:1)==0){var e;this._insertTag(((e=document.createElement("style")).setAttribute("data-emotion",this.key),void 0!==this.nonce&&e.setAttribute("nonce",this.nonce),e.appendChild(document.createTextNode("")),e.setAttribute("data-s",""),e))}var i=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(t){if(t.sheet)return t.sheet;for(var e=0;e<document.styleSheets.length;e++)if(document.styleSheets[e].ownerNode===t)return document.styleSheets[e]}(i);try{n.insertRule(t,n.cssRules.length)}catch(t){}}else i.appendChild(document.createTextNode(t));this.ctr++},e.flush=function(){this.tags.forEach(function(t){var e;return null==(e=t.parentNode)?void 0:e.removeChild(t)}),this.tags=[],this.ctr=0},t}(),r=Math.abs,a=String.fromCharCode,s=Object.assign;function o(t,e,i){return t.replace(e,i)}function l(t,e){return t.indexOf(e)}function c(t,e){return 0|t.charCodeAt(e)}function u(t,e,i){return t.slice(e,i)}function h(t){return t.length}function d(t,e){return e.push(t),t}var p=1,f=1,_=0,v=0,m=0,g="";function y(t,e,i,n,r,a,s){return{value:t,root:e,parent:i,type:n,props:r,children:a,line:p,column:f,length:s,return:""}}function w(t,e){return s(y("",null,null,"",null,null,0),t,{length:-t.length},e)}function b(){return m=v<_?c(g,v++):0,f++,10===m&&(f=1,p++),m}function S(){return c(g,v)}function k(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function A(t){return p=f=1,_=h(g=t),v=0,[]}function x(t){var e,i;return(e=v-1,i=function t(e){for(;b();)switch(m){case e:return v;case 34:case 39:34!==e&&39!==e&&t(m);break;case 40:41===e&&t(e);break;case 92:b()}return v}(91===t?t+2:40===t?t+1:t),u(g,e,i)).trim()}var O="-ms-",U="-moz-",E="-webkit-",P="comm",C="rule",I="decl",T="@keyframes";function R(t,e){for(var i="",n=t.length,r=0;r<n;r++)i+=e(t[r],r,t,e)||"";return i}function M(t,e,i,n){switch(t.type){case"@layer":if(t.children.length)break;case"@import":case I:return t.return=t.return||t.value;case P:return"";case T:return t.return=t.value+"{"+R(t.children,n)+"}";case C:t.value=t.props.join(",")}return h(i=R(t.children,n))?t.return=t.value+"{"+i+"}":""}function N(t,e,i,n,a,s,l,c,h,d,p){for(var f=a-1,_=0===a?s:[""],v=_.length,m=0,g=0,w=0;m<n;++m)for(var b=0,S=u(t,f+1,f=r(g=l[m])),k=t;b<v;++b)(k=(g>0?_[b]+" "+S:o(S,/&\f/g,_[b])).trim())&&(h[w++]=k);return y(t,e,i,0===a?C:c,h,d,p)}function z(t,e,i,n){return y(t,e,i,I,u(t,0,n),u(t,n+1,-1),n)}i(73408);var D=function(t,e,i){for(var n=0,r=0;n=r,r=S(),38===n&&12===r&&(e[i]=1),!k(r);)b();return u(g,t,v)},L=function(t,e){var i=-1,n=44;do switch(k(n)){case 0:38===n&&12===S()&&(e[i]=1),t[i]+=D(v-1,e,i);break;case 2:t[i]+=x(n);break;case 4:if(44===n){t[++i]=58===S()?"&\f":"",e[i]=t[i].length;break}default:t[i]+=a(n)}while(n=b());return t},W=function(t,e){var i;return i=L(A(t),e),g="",i},F=new WeakMap,j=function(t){if("rule"===t.type&&t.parent&&!(t.length<1)){for(var e=t.value,i=t.parent,n=t.column===i.column&&t.line===i.line;"rule"!==i.type;)if(!(i=i.parent))return;if((1!==t.props.length||58===e.charCodeAt(0)||F.get(i))&&!n){F.set(t,!0);for(var r=[],a=W(e,r),s=i.props,o=0,l=0;o<a.length;o++)for(var c=0;c<s.length;c++,l++)t.props[l]=r[o]?a[o].replace(/&\f/g,s[c]):s[c]+" "+a[o]}}},V=function(t){if("decl"===t.type){var e=t.value;108===e.charCodeAt(0)&&98===e.charCodeAt(2)&&(t.return="",t.value="")}},K=[function(t,e,i,n){if(t.length>-1&&!t.return)switch(t.type){case I:t.return=function t(e,i){switch(45^c(e,0)?(((i<<2^c(e,0))<<2^c(e,1))<<2^c(e,2))<<2^c(e,3):0){case 5103:return E+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return E+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return E+e+U+e+O+e+e;case 6828:case 4268:return E+e+O+e+e;case 6165:return E+e+O+"flex-"+e+e;case 5187:return E+e+o(e,/(\w+).+(:[^]+)/,E+"box-$1$2"+O+"flex-$1$2")+e;case 5443:return E+e+O+"flex-item-"+o(e,/flex-|-self/,"")+e;case 4675:return E+e+O+"flex-line-pack"+o(e,/align-content|flex-|-self/,"")+e;case 5548:return E+e+O+o(e,"shrink","negative")+e;case 5292:return E+e+O+o(e,"basis","preferred-size")+e;case 6060:return E+"box-"+o(e,"-grow","")+E+e+O+o(e,"grow","positive")+e;case 4554:return E+o(e,/([^-])(transform)/g,"$1"+E+"$2")+e;case 6187:return o(o(o(e,/(zoom-|grab)/,E+"$1"),/(image-set)/,E+"$1"),e,"")+e;case 5495:case 3959:return o(e,/(image-set\([^]*)/,E+"$1$`$1");case 4968:return o(o(e,/(.+:)(flex-)?(.*)/,E+"box-pack:$3"+O+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+E+e+e;case 4095:case 3583:case 4068:case 2532:return o(e,/(.+)-inline(.+)/,E+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(h(e)-1-i>6)switch(c(e,i+1)){case 109:if(45!==c(e,i+4))break;case 102:return o(e,/(.+:)(.+)-([^]+)/,"$1"+E+"$2-$3$1"+U+(108==c(e,i+3)?"$3":"$2-$3"))+e;case 115:return~l(e,"stretch")?t(o(e,"stretch","fill-available"),i)+e:e}break;case 4949:if(115!==c(e,i+1))break;case 6444:switch(c(e,h(e)-3-(~l(e,"!important")&&10))){case 107:return o(e,":",":"+E)+e;case 101:return o(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+E+(45===c(e,14)?"inline-":"")+"box$3$1"+E+"$2$3$1"+O+"$2box$3")+e}break;case 5936:switch(c(e,i+11)){case 114:return E+e+O+o(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return E+e+O+o(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return E+e+O+o(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return E+e+O+e+e}return e}(t.value,t.length);break;case T:return R([w(t,{value:o(t.value,"@","@"+E)})],n);case C:if(t.length){var r,a;return r=t.props,a=function(e){var i;switch(i=e,(i=/(::plac\w+|:read-\w+)/.exec(i))?i[0]:i){case":read-only":case":read-write":return R([w(t,{props:[o(e,/:(read-\w+)/,":"+U+"$1")]})],n);case"::placeholder":return R([w(t,{props:[o(e,/:(plac\w+)/,":"+E+"input-$1")]}),w(t,{props:[o(e,/:(plac\w+)/,":"+U+"$1")]}),w(t,{props:[o(e,/:(plac\w+)/,O+"input-$1")]})],n)}return""},r.map(a).join("")}}}],B=function(t){var e,i,r,s,_,w,O=t.key;if("css"===O){var U=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(U,function(t){-1!==t.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(t),t.setAttribute("data-s",""))})}var E=t.stylisPlugins||K,C={},I=[];i=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+O+' "]'),function(t){for(var e=t.getAttribute("data-emotion").split(" "),i=1;i<e.length;i++)C[e[i]]=!0;I.push(t)});var T=(w=(_=[j,V].concat(E,[M,(e=function(t){s.insert(t)},function(t){!t.root&&(t=t.return)&&e(t)})])).length,function(t,e,i,n){for(var r="",a=0;a<w;a++)r+=_[a](t,e,i,n)||"";return r}),D=function(t){var e,i;return R((i=function t(e,i,n,r,s,_,w,A,O){for(var U,E=0,C=0,I=w,T=0,R=0,M=0,D=1,L=1,W=1,F=0,j="",V=s,K=_,B=r,$=j;L;)switch(M=F,F=b()){case 40:if(108!=M&&58==c($,I-1)){-1!=l($+=o(x(F),"&","&\f"),"&\f")&&(W=-1);break}case 34:case 39:case 91:$+=x(F);break;case 9:case 10:case 13:case 32:$+=function(t){for(;m=S();)if(m<33)b();else break;return k(t)>2||k(m)>3?"":" "}(M);break;case 92:$+=function(t,e){for(var i;--e&&b()&&!(m<48)&&!(m>102)&&(!(m>57)||!(m<65))&&(!(m>70)||!(m<97)););return i=v+(e<6&&32==S()&&32==b()),u(g,t,i)}(v-1,7);continue;case 47:switch(S()){case 42:case 47:d((U=function(t,e){for(;b();)if(t+m===57)break;else if(t+m===84&&47===S())break;return"/*"+u(g,e,v-1)+"*"+a(47===t?t:b())}(b(),v),y(U,i,n,P,a(m),u(U,2,-2),0)),O);break;default:$+="/"}break;case 123*D:A[E++]=h($)*W;case 125*D:case 59:case 0:switch(F){case 0:case 125:L=0;case 59+C:-1==W&&($=o($,/\f/g,"")),R>0&&h($)-I&&d(R>32?z($+";",r,n,I-1):z(o($," ","")+";",r,n,I-2),O);break;case 59:$+=";";default:if(d(B=N($,i,n,E,C,s,A,j,V=[],K=[],I),_),123===F){if(0===C)t($,i,B,B,V,_,I,A,K);else switch(99===T&&110===c($,3)?100:T){case 100:case 108:case 109:case 115:t(e,B,B,r&&d(N(e,B,B,0,0,s,A,j,s,V=[],I),K),s,K,I,A,r?V:K);break;default:t($,B,B,B,[""],K,0,A,K)}}}E=C=R=0,D=W=1,j=$="",I=w;break;case 58:I=1+h($),R=M;default:if(D<1){if(123==F)--D;else if(125==F&&0==D++&&125==(m=v>0?c(g,--v):0,f--,10===m&&(f=1,p--),m))continue}switch($+=a(F),F*D){case 38:W=C>0?1:($+="\f",-1);break;case 44:A[E++]=(h($)-1)*W,W=1;break;case 64:45===S()&&($+=x(b())),T=S(),C=I=h(j=$+=function(t){for(;!k(S());)b();return u(g,t,v)}(v)),F++;break;case 45:45===M&&2==h($)&&(D=0)}}return _}("",null,null,null,[""],e=A(e=t),0,[0],e),g="",i),T)};r=function(t,e,i,n){s=i,D(t?t+"{"+e.styles+"}":e.styles),n&&(L.inserted[e.name]=!0)};var L={key:O,sheet:new n({key:O,container:i,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:C,registered:{},insert:r};return L.sheet.hydrate(I),L}},1745:function(t,e,i){"use strict";i.d(e,{c:()=>g,E:()=>w,h:()=>h,a:()=>v,C:()=>p}),i(92037),i(56113),i(13189),i(63277),i(82230),i(27899),i(37389),i(27968),i(46299),i(65223),i(87945);var n,r=i(69144),a=i(11025);function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)({}).hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(null,arguments)}var o=i(73408),l=i(99519),c=i(97021),u=i(84191),h={}.hasOwnProperty,d=r.createContext("undefined"!=typeof HTMLElement?(0,a.Z)({key:"css"}):null),p=d.Provider,f=r.createContext({}),_=(0,o.Z)(function(t){return(0,o.Z)(function(e){var i,n;return i=t,"function"==typeof(n=e)?n(i):s({},i,n)})}),v=function(t){var e=r.useContext(f);return t.theme!==e&&(e=_(e)(t.theme)),r.createElement(f.Provider,{value:e},t.children)},m="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",g=function(t,e){var i={};for(var n in e)h.call(e,n)&&(i[n]=e[n]);return i[m]=t,i},y=function(t){var e=t.cache,i=t.serialized,n=t.isStringTag;return(0,l.hC)(e,i,n),(0,u.L)(function(){return(0,l.My)(e,i,n)}),null},w=(n=function(t,e,i){var n=t.css;"string"==typeof n&&void 0!==e.registered[n]&&(n=e.registered[n]);var a=t[m],s=[n],o="";"string"==typeof t.className?o=(0,l.fp)(e.registered,s,t.className):null!=t.className&&(o=t.className+" ");var u=(0,c.O)(s,void 0,r.useContext(f));o+=e.key+"-"+u.name;var d={};for(var p in t)h.call(t,p)&&"css"!==p&&p!==m&&(d[p]=t[p]);return d.ref=i,d.className=o,r.createElement(r.Fragment,null,r.createElement(y,{cache:e,serialized:u,isStringTag:"string"==typeof a}),r.createElement(a,d))},(0,r.forwardRef)(function(t,e){return n(t,(0,r.useContext)(d),e)}))},79109:function(t,e,i){"use strict";i.d(e,{BX:()=>o,HY:()=>a,tZ:()=>s});var n=i(20836),r=i(1745);i(69144),i(11025),i(73408),i(53772),i(99519),i(97021),i(84191);var a=n.Fragment;function s(t,e,i){return r.h.call(e,"css")?n.jsx(r.E,(0,r.c)(t,e),i):n.jsx(t,e,i)}function o(t,e,i){return r.h.call(e,"css")?n.jsxs(r.E,(0,r.c)(t,e),i):n.jsxs(t,e,i)}},97021:function(t,e,i){"use strict";i.d(e,{O:()=>_}),i(87945),i(56113),i(92037);var n,r,a,s={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},o=/[A-Z]|^ms/g,l=/_EMO_([^_]+?)_([^]*?)_EMO_/g,c=function(t){return 45===t.charCodeAt(1)},u=function(t){return null!=t&&"boolean"!=typeof t},h=(n=function(t){return c(t)?t:t.replace(o,"-$&").toLowerCase()},r=Object.create(null),function(t){return void 0===r[t]&&(r[t]=n(t)),r[t]}),d=function(t,e){switch(t){case"animation":case"animationName":if("string"==typeof e)return e.replace(l,function(t,e,i){return a={name:e,styles:i,next:a},e})}return 1===s[t]||c(t)||"number"!=typeof e||0===e?e:e+"px"};function p(t,e,i){if(null==i)return"";if(void 0!==i.__emotion_styles)return i;switch(typeof i){case"boolean":return"";case"object":if(1===i.anim)return a={name:i.name,styles:i.styles,next:a},i.name;if(void 0!==i.styles){var n=i.next;if(void 0!==n)for(;void 0!==n;)a={name:n.name,styles:n.styles,next:a},n=n.next;return i.styles+";"}return function(t,e,i){var n="";if(Array.isArray(i))for(var r=0;r<i.length;r++)n+=p(t,e,i[r])+";";else for(var a in i){var s=i[a];if("object"!=typeof s)null!=e&&void 0!==e[s]?n+=a+"{"+e[s]+"}":u(s)&&(n+=h(a)+":"+d(a,s)+";");else if(Array.isArray(s)&&"string"==typeof s[0]&&(null==e||void 0===e[s[0]]))for(var o=0;o<s.length;o++)u(s[o])&&(n+=h(a)+":"+d(a,s[o])+";");else{var l=p(t,e,s);switch(a){case"animation":case"animationName":n+=h(a)+":"+l+";";break;default:n+=a+"{"+l+"}"}}}return n}(t,e,i);case"function":if(void 0!==t){var r=a,s=i(t);return a=r,p(t,e,s)}}if(null==e)return i;var o=e[i];return void 0!==o?o:i}var f=/label:\s*([^\s;{]+)\s*(;|$)/g;function _(t,e,i){if(1===t.length&&"object"==typeof t[0]&&null!==t[0]&&void 0!==t[0].styles)return t[0];var n,r=!0,s="";a=void 0;var o=t[0];null==o||void 0===o.raw?(r=!1,s+=p(i,e,o)):s+=o[0];for(var l=1;l<t.length;l++)s+=p(i,e,t[l]),r&&(s+=o[l]);f.lastIndex=0;for(var c="";null!==(n=f.exec(s));)c+="-"+n[1];return{name:function(t){for(var e,i=0,n=0,r=t.length;r>=4;++n,r-=4)e=(65535&(e=255&t.charCodeAt(n)|(255&t.charCodeAt(++n))<<8|(255&t.charCodeAt(++n))<<16|(255&t.charCodeAt(++n))<<24))*0x5bd1e995+((e>>>16)*59797<<16),e^=e>>>24,i=(65535&e)*0x5bd1e995+((e>>>16)*59797<<16)^(65535&i)*0x5bd1e995+((i>>>16)*59797<<16);switch(r){case 3:i^=(255&t.charCodeAt(n+2))<<16;case 2:i^=(255&t.charCodeAt(n+1))<<8;case 1:i^=255&t.charCodeAt(n),i=(65535&i)*0x5bd1e995+((i>>>16)*59797<<16)}return i^=i>>>13,(((i=(65535&i)*0x5bd1e995+((i>>>16)*59797<<16))^i>>>15)>>>0).toString(36)}(s)+c,styles:s,next:a}}},84191:function(t,e,i){"use strict";i.d(e,{L:()=>s});var n,r=i(69144),a=!!(n||(n=i.t(r,2))).useInsertionEffect&&(n||(n=i.t(r,2))).useInsertionEffect,s=a||function(t){return t()};a||r.useLayoutEffect},99519:function(t,e,i){"use strict";function n(t,e,i){var n="";return i.split(" ").forEach(function(i){void 0!==t[i]?e.push(t[i]+";"):i&&(n+=i+" ")}),n}i.d(e,{My:()=>a,fp:()=>n,hC:()=>r}),i(56113),i(79876);var r=function(t,e,i){var n=t.key+"-"+e.name;!1===i&&void 0===t.registered[n]&&(t.registered[n]=e.styles)},a=function(t,e,i){r(t,e,i);var n=t.key+"-"+e.name;if(void 0===t.inserted[e.name]){var a=e;do t.insert(e===a?"."+n:"",a,t.sheet,!0),a=a.next;while(void 0!==a)}}},73408:function(t,e,i){"use strict";i.d(e,{Z:()=>n}),i(65223);var n=function(t){var e=new WeakMap;return function(i){if(e.has(i))return e.get(i);var n=t(i);return e.set(i,n),n}}},82788:function(t,e,i){"use strict";i(50725),i(65223),i(92037),i(79876),i(13189),i(63277),i(82230),i(27899),i(37389),i(27968),i(46299),i(28419);var n=this&&this.__awaiter||function(t,e,i,n){return new(i||(i=Promise))(function(r,a){function s(t){try{l(n.next(t))}catch(t){a(t)}}function o(t){try{l(n.throw(t))}catch(t){a(t)}}function l(t){t.done?r(t.value):new i(function(e){e(t.value)}).then(s,o)}l((n=n.apply(t,e||[])).next())})},r=this&&this.__generator||function(t,e){var i,n,r,a,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){return function(a){if(i)throw TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(r=(r=s.trys).length>0&&r[r.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{i=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,o])}}},a=this;Object.defineProperty(e,"__esModule",{value:!0});var s=i(16119),o="browser-tabs-lock-key",l={key:function(t){return n(a,void 0,void 0,function(){return r(this,function(t){throw Error("Unsupported")})})},getItem:function(t){return n(a,void 0,void 0,function(){return r(this,function(t){throw Error("Unsupported")})})},clear:function(){return n(a,void 0,void 0,function(){return r(this,function(t){return[2,window.localStorage.clear()]})})},removeItem:function(t){return n(a,void 0,void 0,function(){return r(this,function(t){throw Error("Unsupported")})})},setItem:function(t,e){return n(a,void 0,void 0,function(){return r(this,function(t){throw Error("Unsupported")})})},keySync:function(t){return window.localStorage.key(t)},getItemSync:function(t){return window.localStorage.getItem(t)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(t){return window.localStorage.removeItem(t)},setItemSync:function(t,e){return window.localStorage.setItem(t,e)}};function c(t){return new Promise(function(e){return setTimeout(e,t)})}function u(t){for(var e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",i="",n=0;n<t;n++){var r=Math.floor(Math.random()*e.length);i+=e[r]}return i}var h=function(){function t(e){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+u(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=e,void 0===t.waiters&&(t.waiters=[])}return t.prototype.acquireLock=function(e,i){return void 0===i&&(i=5e3),n(this,void 0,void 0,function(){var n,a,s,h,d,p,f;return r(this,function(r){switch(r.label){case 0:n=Date.now()+u(4),a=Date.now()+i,s=o+"-"+e,h=void 0===this.storageHandler?l:this.storageHandler,r.label=1;case 1:if(!(Date.now()<a))return[3,8];return[4,c(30)];case 2:if(r.sent(),null!==h.getItemSync(s))return[3,5];return d=this.id+"-"+e+"-"+n,[4,c(Math.floor(25*Math.random()))];case 3:return r.sent(),h.setItemSync(s,JSON.stringify({id:this.id,iat:n,timeoutKey:d,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,c(30)];case 4:if(r.sent(),null!==(p=h.getItemSync(s))&&(f=JSON.parse(p)).id===this.id&&f.iat===n)return this.acquiredIatSet.add(n),this.refreshLockWhileAcquired(s,n),[2,!0];return[3,7];case 5:return t.lockCorrector(void 0===this.storageHandler?l:this.storageHandler),[4,this.waitForSomethingToChange(a)];case 6:r.sent(),r.label=7;case 7:return n=Date.now()+u(4),[3,1];case 8:return[2,!1]}})})},t.prototype.refreshLockWhileAcquired=function(t,e){return n(this,void 0,void 0,function(){var i=this;return r(this,function(a){return setTimeout(function(){return n(i,void 0,void 0,function(){var i,n,a;return r(this,function(r){switch(r.label){case 0:return[4,s.default().lock(e)];case 1:if(r.sent(),!this.acquiredIatSet.has(e)||null===(n=(i=void 0===this.storageHandler?l:this.storageHandler).getItemSync(t)))return s.default().unlock(e),[2];return(a=JSON.parse(n)).timeRefreshed=Date.now(),i.setItemSync(t,JSON.stringify(a)),s.default().unlock(e),this.refreshLockWhileAcquired(t,e),[2]}})})},1e3),[2]})})},t.prototype.waitForSomethingToChange=function(e){return n(this,void 0,void 0,function(){return r(this,function(i){switch(i.label){case 0:return[4,new Promise(function(i){var n=!1,r=Date.now(),a=!1;function s(){if(a||(window.removeEventListener("storage",s),t.removeFromWaiting(s),clearTimeout(o),a=!0),!n){n=!0;var e=50-(Date.now()-r);e>0?setTimeout(i,e):i(null)}}window.addEventListener("storage",s),t.addToWaiting(s);var o=setTimeout(s,Math.max(0,e-Date.now()))})];case 1:return i.sent(),[2]}})})},t.addToWaiting=function(e){this.removeFromWaiting(e),void 0!==t.waiters&&t.waiters.push(e)},t.removeFromWaiting=function(e){void 0!==t.waiters&&(t.waiters=t.waiters.filter(function(t){return t!==e}))},t.notifyWaiters=function(){void 0!==t.waiters&&t.waiters.slice().forEach(function(t){return t()})},t.prototype.releaseLock=function(t){return n(this,void 0,void 0,function(){return r(this,function(e){switch(e.label){case 0:return[4,this.releaseLock__private__(t)];case 1:return[2,e.sent()]}})})},t.prototype.releaseLock__private__=function(e){return n(this,void 0,void 0,function(){var i,n,a,c;return r(this,function(r){switch(r.label){case 0:if(i=void 0===this.storageHandler?l:this.storageHandler,n=o+"-"+e,null===(a=i.getItemSync(n)))return[2];if((c=JSON.parse(a)).id!==this.id)return[3,2];return[4,s.default().lock(c.iat)];case 1:r.sent(),this.acquiredIatSet.delete(c.iat),i.removeItemSync(n),s.default().unlock(c.iat),t.notifyWaiters(),r.label=2;case 2:return[2]}})})},t.lockCorrector=function(e){for(var i=Date.now()-5e3,n=[],r=0;;){var a=e.keySync(r);if(null===a)break;n.push(a),r++}for(var s=!1,l=0;l<n.length;l++){var c=n[l];if(c.includes(o)){var u=e.getItemSync(c);if(null!==u){var h=JSON.parse(u);(void 0===h.timeRefreshed&&h.timeAcquired<i||void 0!==h.timeRefreshed&&h.timeRefreshed<i)&&(e.removeItemSync(c),s=!0)}}}s&&t.notifyWaiters()},t.waiters=void 0,t}();e.default=h},16119:function(t,e,i){"use strict";i(65223),i(838),i(50725),Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){var t=this;this.locked=new Map,this.addToLocked=function(e,i){var n=t.locked.get(e);void 0===n?void 0===i?t.locked.set(e,[]):t.locked.set(e,[i]):void 0!==i&&(n.unshift(i),t.locked.set(e,n))},this.isLocked=function(e){return t.locked.has(e)},this.lock=function(e){return new Promise(function(i,n){t.isLocked(e)?t.addToLocked(e,i):(t.addToLocked(e),i())})},this.unlock=function(e){var i=t.locked.get(e);if(void 0===i||0===i.length){t.locked.delete(e);return}var n=i.pop();t.locked.set(e,i),void 0!==n&&setTimeout(n,0)}}return t.getInstance=function(){return void 0===t.instance&&(t.instance=new t),t.instance},t}();e.default=function(){return n.getInstance()}},54763:function(t,e,i){i(92037),i(50887),i(80194),i(25800),i(56113),t.exports=function(t,e){if("string"!=typeof t)throw TypeError("Expected a string");for(var i,n=String(t),r="",a=!!e&&!!e.extended,s=!!e&&!!e.globstar,o=!1,l=e&&"string"==typeof e.flags?e.flags:"",c=0,u=n.length;c<u;c++)switch(i=n[c]){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":r+="\\"+i;break;case"?":if(a){r+=".";break}case"[":case"]":if(a){r+=i;break}case"{":if(a){o=!0,r+="(";break}case"}":if(a){o=!1,r+=")";break}case",":if(o){r+="|";break}r+="\\"+i;break;case"*":for(var h=n[c-1],d=1;"*"===n[c+1];)d++,c++;var p=n[c+1];s?d>1&&("/"===h||void 0===h)&&("/"===p||void 0===p)?(r+="((?:[^/]*(?:/|$))*)",c++):r+="([^/]*)":r+=".*";break;default:r+=i}return l&&~l.indexOf("g")||(r="^"+r+"$"),new RegExp(r,l)}},53772:function(t,e,i){"use strict";var n=i(88131),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},o={};function l(t){return n.isMemo(t)?s:o[t.$$typeof]||r}o[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o[n.Memo]=s;var c=Object.defineProperty,u=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,f=Object.prototype;t.exports=function t(e,i,n){if("string"!=typeof i){if(f){var r=p(i);r&&r!==f&&t(e,r,n)}var s=u(i);h&&(s=s.concat(h(i)));for(var o=l(e),_=l(i),v=0;v<s.length;++v){var m=s[v];if(!a[m]&&!(n&&n[m])&&!(_&&_[m])&&!(o&&o[m])){var g=d(i,m);try{c(e,m,g)}catch(t){}}}}return e}},1470:function(t,e){"use strict";var i="function"==typeof Symbol&&Symbol.for,n=i?Symbol.for("react.element"):60103,r=i?Symbol.for("react.portal"):60106,a=i?Symbol.for("react.fragment"):60107,s=i?Symbol.for("react.strict_mode"):60108,o=i?Symbol.for("react.profiler"):60114,l=i?Symbol.for("react.provider"):60109,c=i?Symbol.for("react.context"):60110,u=i?Symbol.for("react.async_mode"):60111,h=i?Symbol.for("react.concurrent_mode"):60111,d=i?Symbol.for("react.forward_ref"):60112,p=i?Symbol.for("react.suspense"):60113,f=i?Symbol.for("react.suspense_list"):60120,_=i?Symbol.for("react.memo"):60115,v=i?Symbol.for("react.lazy"):60116,m=i?Symbol.for("react.block"):60121,g=i?Symbol.for("react.fundamental"):60117,y=i?Symbol.for("react.responder"):60118,w=i?Symbol.for("react.scope"):60119;function b(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case u:case h:case a:case o:case s:case p:return t;default:switch(t=t&&t.$$typeof){case c:case d:case v:case _:case l:return t;default:return e}}case r:return e}}}function S(t){return b(t)===h}e.AsyncMode=u,e.ConcurrentMode=h,e.ContextConsumer=c,e.ContextProvider=l,e.Element=n,e.ForwardRef=d,e.Fragment=a,e.Lazy=v,e.Memo=_,e.Portal=r,e.Profiler=o,e.StrictMode=s,e.Suspense=p,e.isAsyncMode=function(t){return S(t)||b(t)===u},e.isConcurrentMode=S,e.isContextConsumer=function(t){return b(t)===c},e.isContextProvider=function(t){return b(t)===l},e.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===n},e.isForwardRef=function(t){return b(t)===d},e.isFragment=function(t){return b(t)===a},e.isLazy=function(t){return b(t)===v},e.isMemo=function(t){return b(t)===_},e.isPortal=function(t){return b(t)===r},e.isProfiler=function(t){return b(t)===o},e.isStrictMode=function(t){return b(t)===s},e.isSuspense=function(t){return b(t)===p},e.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===a||t===h||t===o||t===s||t===p||t===f||"object"==typeof t&&null!==t&&(t.$$typeof===v||t.$$typeof===_||t.$$typeof===l||t.$$typeof===c||t.$$typeof===d||t.$$typeof===g||t.$$typeof===y||t.$$typeof===w||t.$$typeof===m)},e.typeOf=b},88131:function(t,e,i){"use strict";t.exports=i(1470)},27276:function(t,e,i){"use strict";var n=i(69144),r=Symbol.for("react.element"),a=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,o=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,i){var n,a={},c=null,u=null;for(n in void 0!==i&&(c=""+i),void 0!==e.key&&(c=""+e.key),void 0!==e.ref&&(u=e.ref),e)s.call(e,n)&&!l.hasOwnProperty(n)&&(a[n]=e[n]);if(t&&t.defaultProps)for(n in e=t.defaultProps)void 0===a[n]&&(a[n]=e[n]);return{$$typeof:r,type:t,key:c,ref:u,props:a,_owner:o.current}}e.Fragment=a,e.jsx=c,e.jsxs=c},14326:function(t,e,i){"use strict";i(65223),i(92037),i(87945),i(56113),i(79876);var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator,_={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},v=Object.assign,m={};function g(t,e,i){this.props=t,this.context=e,this.refs=m,this.updater=i||_}function y(){}function w(t,e,i){this.props=t,this.context=e,this.refs=m,this.updater=i||_}g.prototype.isReactComponent={},g.prototype.setState=function(t,e){if("object"!=typeof t&&"function"!=typeof t&&null!=t)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")},g.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},y.prototype=g.prototype;var b=w.prototype=new y;b.constructor=w,v(b,g.prototype),b.isPureReactComponent=!0;var S=Array.isArray,k=Object.prototype.hasOwnProperty,A={current:null},x={key:!0,ref:!0,__self:!0,__source:!0};function O(t,e,i){var r,a={},s=null,o=null;if(null!=e)for(r in void 0!==e.ref&&(o=e.ref),void 0!==e.key&&(s=""+e.key),e)k.call(e,r)&&!x.hasOwnProperty(r)&&(a[r]=e[r]);var l=arguments.length-2;if(1===l)a.children=i;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];a.children=c}if(t&&t.defaultProps)for(r in l=t.defaultProps)void 0===a[r]&&(a[r]=l[r]);return{$$typeof:n,type:t,key:s,ref:o,props:a,_owner:A.current}}function U(t){return"object"==typeof t&&null!==t&&t.$$typeof===n}var E=/\/+/g;function P(t,e){var i,n;return"object"==typeof t&&null!==t&&null!=t.key?(i=""+t.key,n={"=":"=0",":":"=2"},"$"+i.replace(/[=:]/g,function(t){return n[t]})):e.toString(36)}function C(t,e,i){if(null==t)return t;var a=[],s=0;return!function t(e,i,a,s,o){var l,c,u,h=typeof e;("undefined"===h||"boolean"===h)&&(e=null);var d=!1;if(null===e)d=!0;else switch(h){case"string":case"number":d=!0;break;case"object":switch(e.$$typeof){case n:case r:d=!0}}if(d)return o=o(d=e),e=""===s?"."+P(d,0):s,S(o)?(a="",null!=e&&(a=e.replace(E,"$&/")+"/"),t(o,i,a,"",function(t){return t})):null!=o&&(U(o)&&(l=o,c=a+(!o.key||d&&d.key===o.key?"":(""+o.key).replace(E,"$&/")+"/")+e,o={$$typeof:n,type:l.type,key:c,ref:l.ref,props:l.props,_owner:l._owner}),i.push(o)),1;if(d=0,s=""===s?".":s+":",S(e))for(var p=0;p<e.length;p++){var _=s+P(h=e[p],p);d+=t(h,i,a,_,o)}else if("function"==typeof(_=null===(u=e)||"object"!=typeof u?null:"function"==typeof(u=f&&u[f]||u["@@iterator"])?u:null))for(e=_.call(e),p=0;!(h=e.next()).done;)_=s+P(h=h.value,p++),d+=t(h,i,a,_,o);else if("object"===h)throw Error("Objects are not valid as a React child (found: "+("[object Object]"===(i=String(e))?"object with keys {"+Object.keys(e).join(", ")+"}":i)+"). If you meant to render a collection of children, use an array instead.");return d}(t,a,"","",function(t){return e.call(i,t,s++)}),a}function I(t){if(-1===t._status){var e=t._result;(e=e()).then(function(e){(0===t._status||-1===t._status)&&(t._status=1,t._result=e)},function(e){(0===t._status||-1===t._status)&&(t._status=2,t._result=e)}),-1===t._status&&(t._status=0,t._result=e)}if(1===t._status)return t._result.default;throw t._result}var T={current:null},R={transition:null};function M(){throw Error("act(...) is not supported in production builds of React.")}e.Children={map:C,forEach:function(t,e,i){C(t,function(){e.apply(this,arguments)},i)},count:function(t){var e=0;return C(t,function(){e++}),e},toArray:function(t){return C(t,function(t){return t})||[]},only:function(t){if(!U(t))throw Error("React.Children.only expected to receive a single React element child.");return t}},e.Component=g,e.Fragment=a,e.Profiler=o,e.PureComponent=w,e.StrictMode=s,e.Suspense=h,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:R,ReactCurrentOwner:A},e.act=M,e.cloneElement=function(t,e,i){if(null==t)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=v({},t.props),a=t.key,s=t.ref,o=t._owner;if(null!=e){if(void 0!==e.ref&&(s=e.ref,o=A.current),void 0!==e.key&&(a=""+e.key),t.type&&t.type.defaultProps)var l=t.type.defaultProps;for(c in e)k.call(e,c)&&!x.hasOwnProperty(c)&&(r[c]=void 0===e[c]&&void 0!==l?l[c]:e[c])}var c=arguments.length-2;if(1===c)r.children=i;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:n,type:t.type,key:a,ref:s,props:r,_owner:o}},e.createContext=function(t){return(t={$$typeof:c,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:t},t.Consumer=t},e.createElement=O,e.createFactory=function(t){var e=O.bind(null,t);return e.type=t,e},e.createRef=function(){return{current:null}},e.forwardRef=function(t){return{$$typeof:u,render:t}},e.isValidElement=U,e.lazy=function(t){return{$$typeof:p,_payload:{_status:-1,_result:t},_init:I}},e.memo=function(t,e){return{$$typeof:d,type:t,compare:void 0===e?null:e}},e.startTransition=function(t){var e=R.transition;R.transition={};try{t()}finally{R.transition=e}},e.unstable_act=M,e.useCallback=function(t,e){return T.current.useCallback(t,e)},e.useContext=function(t){return T.current.useContext(t)},e.useDebugValue=function(){},e.useDeferredValue=function(t){return T.current.useDeferredValue(t)},e.useEffect=function(t,e){return T.current.useEffect(t,e)},e.useId=function(){return T.current.useId()},e.useImperativeHandle=function(t,e,i){return T.current.useImperativeHandle(t,e,i)},e.useInsertionEffect=function(t,e){return T.current.useInsertionEffect(t,e)},e.useLayoutEffect=function(t,e){return T.current.useLayoutEffect(t,e)},e.useMemo=function(t,e){return T.current.useMemo(t,e)},e.useReducer=function(t,e,i){return T.current.useReducer(t,e,i)},e.useRef=function(t){return T.current.useRef(t)},e.useState=function(t){return T.current.useState(t)},e.useSyncExternalStore=function(t,e,i){return T.current.useSyncExternalStore(t,e,i)},e.useTransition=function(){return T.current.useTransition()},e.version="18.3.1"},69144:function(t,e,i){"use strict";t.exports=i(14326)},20836:function(t,e,i){"use strict";t.exports=i(27276)},34096:function(t,e,i){i(64728),i(50725),i(92037),i(79876),i(65223);var n=function(t){"use strict";var e,i=Object.prototype,n=i.hasOwnProperty,r=Object.defineProperty||function(t,e,i){t[e]=i.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",o=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,i){return t[e]=i}}function u(t,i,n,a){var s,o,l,c,u=Object.create((i&&i.prototype instanceof v?i:v).prototype);return r(u,"_invoke",{value:(s=t,o=n,l=new U(a||[]),c=d,function(t,i){if(c===p)throw Error("Generator is already running");if(c===f){if("throw"===t)throw i;return{value:e,done:!0}}for(l.method=t,l.arg=i;;){var n=l.delegate;if(n){var r=function t(i,n){var r=n.method,a=i.iterator[r];if(e===a)return n.delegate=null,"throw"===r&&i.iterator.return&&(n.method="return",n.arg=e,t(i,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=TypeError("The iterator does not provide a '"+r+"' method")),_;var s=h(a,i.iterator,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,_;var o=s.arg;return o?o.done?(n[i.resultName]=o.value,n.next=i.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,_):o:(n.method="throw",n.arg=TypeError("iterator result is not an object"),n.delegate=null,_)}(n,l);if(r){if(r===_)continue;return r}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(c===d)throw c=f,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);c=p;var a=h(s,o,l);if("normal"===a.type){if(c=l.done?f:"suspendedYield",a.arg===_)continue;return{value:a.arg,done:l.done}}"throw"===a.type&&(c=f,l.method="throw",l.arg=a.arg)}})}),u}function h(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var d="suspendedStart",p="executing",f="completed",_={};function v(){}function m(){}function g(){}var y={};c(y,s,function(){return this});var w=Object.getPrototypeOf,b=w&&w(w(E([])));b&&b!==i&&n.call(b,s)&&(y=b);var S=g.prototype=v.prototype=Object.create(y);function k(t){["next","throw","return"].forEach(function(e){c(t,e,function(t){return this._invoke(e,t)})})}function A(t,e){var i;r(this,"_invoke",{value:function(r,a){function s(){return new e(function(i,s){!function i(r,a,s,o){var l=h(t[r],t,a);if("throw"===l.type)o(l.arg);else{var c=l.arg,u=c.value;return u&&"object"==typeof u&&n.call(u,"__await")?e.resolve(u.__await).then(function(t){i("next",t,s,o)},function(t){i("throw",t,s,o)}):e.resolve(u).then(function(t){c.value=t,s(c)},function(t){return i("throw",t,s,o)})}}(r,a,i,s)})}return i=i?i.then(s,s):s()}})}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function U(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function E(t){if(null!=t){var i=t[s];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function i(){for(;++r<t.length;)if(n.call(t,r))return i.value=t[r],i.done=!1,i;return i.value=e,i.done=!0,i};return a.next=a}}throw TypeError(typeof t+" is not iterable")}return m.prototype=g,r(S,"constructor",{value:g,configurable:!0}),r(g,"constructor",{value:m,configurable:!0}),m.displayName=c(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,c(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},t.awrap=function(t){return{__await:t}},k(A.prototype),c(A.prototype,o,function(){return this}),t.AsyncIterator=A,t.async=function(e,i,n,r,a){void 0===a&&(a=Promise);var s=new A(u(e,i,n,r),a);return t.isGeneratorFunction(i)?s:s.next().then(function(t){return t.done?t.value:s.next()})},k(S),c(S,l,"Generator"),c(S,s,function(){return this}),c(S,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=E,U.prototype={constructor:U,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var i in this)"t"===i.charAt(0)&&n.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function r(n,r){return o.type="throw",o.arg=t,i.next=n,r&&(i.method="next",i.arg=e),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var s=this.tryEntries[a],o=s.completion;if("root"===s.tryLoc)return r("end");if(s.tryLoc<=this.prev){var l=n.call(s,"catchLoc"),c=n.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return r(s.catchLoc,!0);if(this.prev<s.finallyLoc)return r(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return r(s.catchLoc,!0)}else if(c){if(this.prev<s.finallyLoc)return r(s.finallyLoc)}else throw Error("try statement without catch or finally")}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var s=a?a.completion:{};return(s.type=t,s.arg=e,a)?(this.method="next",this.next=a.finallyLoc,_):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),O(i),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var r=n.arg;O(i)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,i,n){return this.delegate={iterator:E(t),resultName:i,nextLoc:n},"next"===this.method&&(this.arg=e),_}},t}(t.exports);try{regeneratorRuntime=n}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},94112:function(t,e,i){"use strict";var n=i(28917),r=i(76934),a=TypeError;t.exports=function(t){if(n(t))return t;throw new a(r(t)+" is not a function")}},29983:function(t,e,i){"use strict";var n=i(32410),r=i(76934),a=TypeError;t.exports=function(t){if(n(t))return t;throw new a(r(t)+" is not a constructor")}},38744:function(t,e,i){"use strict";var n=i(38549),r=String,a=TypeError;t.exports=function(t){if(n(t))return t;throw new a("Can't set "+r(t)+" as a prototype")}},80656:function(t,e,i){"use strict";var n=i(7873).has;t.exports=function(t){return n(t),t}},63133:function(t,e,i){"use strict";var n=i(21755),r=i(38270),a=i(79406).f,s=n("unscopables"),o=Array.prototype;void 0===o[s]&&a(o,s,{configurable:!0,value:r(null)}),t.exports=function(t){o[s][t]=!0}},44812:function(t,e,i){"use strict";var n=i(55321).charAt;t.exports=function(t,e,i){return e+(i?n(t,e).length:1)}},14644:function(t,e,i){"use strict";var n=i(84776),r=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new r("Incorrect invocation")}},98903:function(t,e,i){"use strict";var n=i(10136),r=String,a=TypeError;t.exports=function(t){if(n(t))return t;throw new a(r(t)+" is not an object")}},21963:function(t){"use strict";t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},81616:function(t,e,i){"use strict";var n=i(42623),r=i(53923),a=i(89255),s=n.ArrayBuffer,o=n.TypeError;t.exports=s&&r(s.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==a(t))throw new o("ArrayBuffer expected");return t.byteLength}},58047:function(t,e,i){"use strict";var n=i(42623),r=i(21963),a=i(81616),s=n.DataView;t.exports=function(t){if(!r||0!==a(t))return!1;try{return new s(t),!1}catch(t){return!0}}},65871:function(t,e,i){"use strict";var n=i(58047),r=TypeError;t.exports=function(t){if(n(t))throw new r("ArrayBuffer is detached");return t}},25126:function(t,e,i){"use strict";var n=i(42623),r=i(26004),a=i(53923),s=i(23006),o=i(65871),l=i(81616),c=i(7669),u=i(10354),h=n.structuredClone,d=n.ArrayBuffer,p=n.DataView,f=Math.min,_=d.prototype,v=p.prototype,m=r(_.slice),g=a(_,"resizable","get"),y=a(_,"maxByteLength","get"),w=r(v.getInt8),b=r(v.setInt8);t.exports=(u||c)&&function(t,e,i){var n,r=l(t),a=void 0===e?r:s(e),_=!g||!g(t);if(o(t),u&&(t=h(t,{transfer:[t]}),r===a&&(i||_)))return t;if(r>=a&&(!i||_))n=m(t,0,a);else{n=new d(a,i&&!_&&y?{maxByteLength:y(t)}:void 0);for(var v=new p(t),S=new p(n),k=f(a,r),A=0;A<k;A++)b(S,A,w(v,A))}return u||c(t),n}},44708:function(t,e,i){"use strict";var n,r,a,s=i(21963),o=i(97223),l=i(42623),c=i(28917),u=i(10136),h=i(29027),d=i(70422),p=i(76934),f=i(52801),_=i(51200),v=i(22700),m=i(84776),g=i(32957),y=i(6887),w=i(21755),b=i(92947),S=i(67875),k=S.enforce,A=S.get,x=l.Int8Array,O=x&&x.prototype,U=l.Uint8ClampedArray,E=U&&U.prototype,P=x&&g(x),C=O&&g(O),I=Object.prototype,T=l.TypeError,R=w("toStringTag"),M=b("TYPED_ARRAY_TAG"),N="TypedArrayConstructor",z=s&&!!y&&"Opera"!==d(l.opera),D=!1,L={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},W={BigInt64Array:8,BigUint64Array:8},F=function(t){var e=g(t);if(u(e)){var i=A(e);return i&&h(i,N)?i[N]:F(e)}},j=function(t){if(!u(t))return!1;var e=d(t);return h(L,e)||h(W,e)};for(n in L)(a=(r=l[n])&&r.prototype)?k(a)[N]=r:z=!1;for(n in W)(a=(r=l[n])&&r.prototype)&&(k(a)[N]=r);if((!z||!c(P)||P===Function.prototype)&&(P=function(){throw new T("Incorrect invocation")},z))for(n in L)l[n]&&y(l[n],P);if((!z||!C||C===I)&&(C=P.prototype,z))for(n in L)l[n]&&y(l[n].prototype,C);if(z&&g(E)!==C&&y(E,C),o&&!h(C,R))for(n in D=!0,v(C,R,{configurable:!0,get:function(){return u(this)?this[M]:void 0}}),L)l[n]&&f(l[n],M,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:z,TYPED_ARRAY_TAG:D&&M,aTypedArray:function(t){if(j(t))return t;throw new T("Target is not a typed array")},aTypedArrayConstructor:function(t){if(c(t)&&(!y||m(P,t)))return t;throw new T(p(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,i,n){if(o){if(i)for(var r in L){var a=l[r];if(a&&h(a.prototype,t))try{delete a.prototype[t]}catch(i){try{a.prototype[t]=e}catch(t){}}}(!C[t]||i)&&_(C,t,i?e:z&&O[t]||e,n)}},exportTypedArrayStaticMethod:function(t,e,i){var n,r;if(o){if(y){if(i){for(n in L)if((r=l[n])&&h(r,t))try{delete r[t]}catch(t){}}if(P[t]&&!i)return;try{return _(P,t,i?e:z&&P[t]||e)}catch(t){}}for(n in L)(r=l[n])&&(!r[t]||i)&&_(r,t,e)}},getTypedArrayConstructor:F,isView:function(t){if(!u(t))return!1;var e=d(t);return"DataView"===e||h(L,e)||h(W,e)},isTypedArray:j,TypedArray:P,TypedArrayPrototype:C}},66509:function(t,e,i){"use strict";var n=i(42623),r=i(26004),a=i(97223),s=i(21963),o=i(70459),l=i(52801),c=i(22700),u=i(682),h=i(81124),d=i(14644),p=i(17940),f=i(11934),_=i(23006),v=i(61102),m=i(34223),g=i(32957),y=i(6887),w=i(49546),b=i(88221),S=i(31858),k=i(22356),A=i(83244),x=i(67875),O=o.PROPER,U=o.CONFIGURABLE,E="ArrayBuffer",P="DataView",C="prototype",I="Wrong index",T=x.getterFor(E),R=x.getterFor(P),M=x.set,N=n[E],z=N,D=z&&z[C],L=n[P],W=L&&L[C],F=Object.prototype,j=n.Array,V=n.RangeError,K=r(w),B=r([].reverse),$=m.pack,J=m.unpack,q=function(t){return[255&t]},G=function(t){return[255&t,t>>8&255]},H=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},Z=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},Y=function(t){return $(v(t),23,4)},X=function(t){return $(t,52,8)},Q=function(t,e,i){c(t[C],e,{configurable:!0,get:function(){return i(this)[e]}})},tt=function(t,e,i,n){var r=R(t),a=_(i);if(a+e>r.byteLength)throw new V(I);var s=r.bytes,o=a+r.byteOffset,l=b(s,o,o+e);return n?l:B(l)},te=function(t,e,i,n,r,a){var s=R(t),o=_(i),l=n(+r),c=!!a;if(o+e>s.byteLength)throw new V(I);for(var u=s.bytes,h=o+s.byteOffset,d=0;d<e;d++)u[h+d]=l[c?d:e-d-1]};if(s){var ti=O&&N.name!==E;!h(function(){N(1)})||!h(function(){new N(-1)})||h(function(){return new N,new N(1.5),new N(NaN),1!==N.length||ti&&!U})?((z=function(t){return d(this,D),S(new N(_(t)),this,z)})[C]=D,D.constructor=z,k(z,N)):ti&&U&&l(N,"name",E),y&&g(W)!==F&&y(W,F);var tn=new L(new z(2)),tr=r(W.setInt8);tn.setInt8(0,0x80000000),tn.setInt8(1,0x80000001),(tn.getInt8(0)||!tn.getInt8(1))&&u(W,{setInt8:function(t,e){tr(this,t,e<<24>>24)},setUint8:function(t,e){tr(this,t,e<<24>>24)}},{unsafe:!0})}else D=(z=function(t){d(this,D);var e=_(t);M(this,{type:E,bytes:K(j(e),0),byteLength:e}),a||(this.byteLength=e,this.detached=!1)})[C],W=(L=function(t,e,i){d(this,W),d(t,D);var n=T(t),r=n.byteLength,s=p(e);if(s<0||s>r)throw new V("Wrong offset");if(i=void 0===i?r-s:f(i),s+i>r)throw new V("Wrong length");M(this,{type:P,buffer:t,byteLength:i,byteOffset:s,bytes:n.bytes}),a||(this.buffer=t,this.byteLength=i,this.byteOffset=s)})[C],a&&(Q(z,"byteLength",T),Q(L,"buffer",R),Q(L,"byteLength",R),Q(L,"byteOffset",R)),u(W,{getInt8:function(t){return tt(this,1,t)[0]<<24>>24},getUint8:function(t){return tt(this,1,t)[0]},getInt16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return Z(tt(this,4,t,arguments.length>1&&arguments[1]))},getUint32:function(t){return Z(tt(this,4,t,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(t){return J(tt(this,4,t,arguments.length>1&&arguments[1]),23)},getFloat64:function(t){return J(tt(this,8,t,arguments.length>1&&arguments[1]),52)},setInt8:function(t,e){te(this,1,t,q,e)},setUint8:function(t,e){te(this,1,t,q,e)},setInt16:function(t,e){te(this,2,t,G,e,arguments.length>2&&arguments[2])},setUint16:function(t,e){te(this,2,t,G,e,arguments.length>2&&arguments[2])},setInt32:function(t,e){te(this,4,t,H,e,arguments.length>2&&arguments[2])},setUint32:function(t,e){te(this,4,t,H,e,arguments.length>2&&arguments[2])},setFloat32:function(t,e){te(this,4,t,Y,e,arguments.length>2&&arguments[2])},setFloat64:function(t,e){te(this,8,t,X,e,arguments.length>2&&arguments[2])}});A(z,E),A(L,P),t.exports={ArrayBuffer:z,DataView:L}},49546:function(t,e,i){"use strict";var n=i(87620),r=i(35745),a=i(8785);t.exports=function(t){for(var e=n(this),i=a(e),s=arguments.length,o=r(s>1?arguments[1]:void 0,i),l=s>2?arguments[2]:void 0,c=void 0===l?i:r(l,i);c>o;)e[o++]=t;return e}},97338:function(t,e,i){"use strict";var n=i(8785);t.exports=function(t,e,i){for(var r=0,a=arguments.length>2?i:n(e),s=new t(a);a>r;)s[r]=e[r++];return s}},41039:function(t,e,i){"use strict";var n=i(1480),r=i(59528),a=i(87620),s=i(80107),o=i(30059),l=i(32410),c=i(8785),u=i(82920),h=i(85773),d=i(2691),p=Array;t.exports=function(t){var e,i,f,_,v,m,g=a(t),y=l(this),w=arguments.length,b=w>1?arguments[1]:void 0,S=void 0!==b;S&&(b=n(b,w>2?arguments[2]:void 0));var k=d(g),A=0;if(k&&!(this===p&&o(k)))for(i=y?new this:[],v=(_=h(g,k)).next;!(f=r(v,_)).done;A++)m=S?s(_,b,[f.value,A],!0):f.value,u(i,A,m);else for(e=c(g),i=y?new this(e):p(e);e>A;A++)m=S?b(g[A],A):g[A],u(i,A,m);return i.length=A,i}},8555:function(t,e,i){"use strict";var n=i(48246),r=i(35745),a=i(8785),s=function(t){return function(e,i,s){var o,l=n(e),c=a(l);if(0===c)return!t&&-1;var u=r(s,c);if(t&&i!=i){for(;c>u;)if((o=l[u++])!=o)return!0}else for(;c>u;u++)if((t||u in l)&&l[u]===i)return t||u||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},74620:function(t,e,i){"use strict";var n=i(1480),r=i(1994),a=i(87620),s=i(8785),o=function(t){var e=1===t;return function(i,o,l){for(var c,u=a(i),h=r(u),d=s(h),p=n(o,l);d-- >0;)if(p(c=h[d],d,u))switch(t){case 0:return c;case 1:return d}return e?-1:void 0}};t.exports={findLast:o(0),findLastIndex:o(1)}},94238:function(t,e,i){"use strict";var n=i(1480),r=i(26004),a=i(1994),s=i(87620),o=i(8785),l=i(59824),c=r([].push),u=function(t){var e=1===t,i=2===t,r=3===t,u=4===t,h=6===t,d=7===t,p=5===t||h;return function(f,_,v,m){for(var g,y,w=s(f),b=a(w),S=o(b),k=n(_,v),A=0,x=m||l,O=e?x(f,S):i||d?x(f,0):void 0;S>A;A++)if((p||A in b)&&(y=k(g=b[A],A,w),t)){if(e)O[A]=y;else if(y)switch(t){case 3:return!0;case 5:return g;case 6:return A;case 2:c(O,g)}else switch(t){case 4:return!1;case 7:c(O,g)}}return h?-1:r||u?u:O}};t.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},51530:function(t,e,i){"use strict";var n=i(81124);t.exports=function(t,e){var i=[][t];return!!i&&n(function(){i.call(null,e||function(){return 1},1)})}},31660:function(t,e,i){"use strict";var n=i(94112),r=i(87620),a=i(1994),s=i(8785),o=TypeError,l="Reduce of empty array with no initial value",c=function(t){return function(e,i,c,u){var h=r(e),d=a(h),p=s(h);if(n(i),0===p&&c<2)throw new o(l);var f=t?p-1:0,_=t?-1:1;if(c<2)for(;;){if(f in d){u=d[f],f+=_;break}if(f+=_,t?f<0:p<=f)throw new o(l)}for(;t?f>=0:p>f;f+=_)f in d&&(u=i(u,d[f],f,h));return u}};t.exports={left:c(!1),right:c(!0)}},92766:function(t,e,i){"use strict";var n=i(97223),r=i(43095),a=TypeError,s=Object.getOwnPropertyDescriptor,o=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(r(t)&&!s(t,"length").writable)throw new a("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},88221:function(t,e,i){"use strict";var n=i(26004);t.exports=n([].slice)},27370:function(t,e,i){"use strict";var n=i(88221),r=Math.floor,a=function(t,e){var i=t.length;if(i<8)for(var s,o,l=1;l<i;){for(o=l,s=t[l];o&&e(t[o-1],s)>0;)t[o]=t[--o];o!==l++&&(t[o]=s)}else for(var c=r(i/2),u=a(n(t,0,c),e),h=a(n(t,c),e),d=u.length,p=h.length,f=0,_=0;f<d||_<p;)t[f+_]=f<d&&_<p?0>=e(u[f],h[_])?u[f++]:h[_++]:f<d?u[f++]:h[_++];return t};t.exports=a},78201:function(t,e,i){"use strict";var n=i(43095),r=i(32410),a=i(10136),s=i(21755)("species"),o=Array;t.exports=function(t){var e;return n(t)&&(r(e=t.constructor)&&(e===o||n(e.prototype))?e=void 0:a(e)&&null===(e=e[s])&&(e=void 0)),void 0===e?o:e}},59824:function(t,e,i){"use strict";var n=i(78201);t.exports=function(t,e){return new(n(t))(0===e?0:e)}},58673:function(t,e,i){"use strict";var n=i(8785);t.exports=function(t,e){for(var i=n(t),r=new e(i),a=0;a<i;a++)r[a]=t[i-a-1];return r}},4584:function(t,e,i){"use strict";var n=i(8785),r=i(17940),a=RangeError;t.exports=function(t,e,i,s){var o=n(t),l=r(i),c=l<0?o+l:l;if(c>=o||c<0)throw new a("Incorrect index");for(var u=new e(o),h=0;h<o;h++)u[h]=h===c?s:t[h];return u}},80107:function(t,e,i){"use strict";var n=i(98903),r=i(99584);t.exports=function(t,e,i,a){try{return a?e(n(i)[0],i[1]):e(i)}catch(e){r(t,"throw",e)}}},69815:function(t,e,i){"use strict";var n=i(21755)("iterator"),r=!1;try{var a=0,s={next:function(){return{done:!!a++}},return:function(){r=!0}};s[n]=function(){return this},Array.from(s,function(){throw 2})}catch(t){}t.exports=function(t,e){try{if(!e&&!r)return!1}catch(t){return!1}var i=!1;try{var a={};a[n]=function(){return{next:function(){return{done:i=!0}}}},t(a)}catch(t){}return i}},89255:function(t,e,i){"use strict";var n=i(26004),r=n({}.toString),a=n("".slice);t.exports=function(t){return a(r(t),8,-1)}},70422:function(t,e,i){"use strict";var n=i(85830),r=i(28917),a=i(89255),s=i(21755)("toStringTag"),o=Object,l="Arguments"===a(function(){return arguments}()),c=function(t,e){try{return t[e]}catch(t){}};t.exports=n?a:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=c(e=o(t),s))?i:l?a(e):"Object"===(n=a(e))&&r(e.callee)?"Arguments":n}},22356:function(t,e,i){"use strict";var n=i(29027),r=i(94224),a=i(75990),s=i(79406);t.exports=function(t,e,i){for(var o=r(e),l=s.f,c=a.f,u=0;u<o.length;u++){var h=o[u];n(t,h)||i&&n(i,h)||l(t,h,c(e,h))}}},40260:function(t,e,i){"use strict";var n=i(81124);t.exports=!n(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},77528:function(t){"use strict";t.exports=function(t,e){return{value:t,done:e}}},52801:function(t,e,i){"use strict";var n=i(97223),r=i(79406),a=i(78407);t.exports=n?function(t,e,i){return r.f(t,e,a(1,i))}:function(t,e,i){return t[e]=i,t}},78407:function(t){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},82920:function(t,e,i){"use strict";var n=i(97223),r=i(79406),a=i(78407);t.exports=function(t,e,i){n?r.f(t,e,a(0,i)):t[e]=i}},22700:function(t,e,i){"use strict";var n=i(83358),r=i(79406);t.exports=function(t,e,i){return i.get&&n(i.get,e,{getter:!0}),i.set&&n(i.set,e,{setter:!0}),r.f(t,e,i)}},51200:function(t,e,i){"use strict";var n=i(28917),r=i(79406),a=i(83358),s=i(58511);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,c=void 0!==o.name?o.name:e;if(n(i)&&a(i,c,o),o.global)l?t[e]=i:s(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=i:r.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},682:function(t,e,i){"use strict";var n=i(51200);t.exports=function(t,e,i){for(var r in e)n(t,r,e[r],i);return t}},58511:function(t,e,i){"use strict";var n=i(42623),r=Object.defineProperty;t.exports=function(t,e){try{r(n,t,{value:e,configurable:!0,writable:!0})}catch(i){n[t]=e}return e}},68028:function(t,e,i){"use strict";var n=i(76934),r=TypeError;t.exports=function(t,e){if(!delete t[e])throw new r("Cannot delete property "+n(e)+" of "+n(t))}},97223:function(t,e,i){"use strict";var n=i(81124);t.exports=!n(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},7669:function(t,e,i){"use strict";var n,r,a,s,o=i(42623),l=i(6387),c=i(10354),u=o.structuredClone,h=o.ArrayBuffer,d=o.MessageChannel,p=!1;if(c)p=function(t){u(t,{transfer:[t]})};else if(h)try{!d&&(n=l("worker_threads"))&&(d=n.MessageChannel),d&&(r=new d,a=new h(2),s=function(t){r.port1.postMessage(null,[t])},2===a.byteLength&&(s(a),0===a.byteLength&&(p=s)))}catch(t){}t.exports=p},88627:function(t,e,i){"use strict";var n=i(42623),r=i(10136),a=n.document,s=r(a)&&r(a.createElement);t.exports=function(t){return s?a.createElement(t):{}}},98196:function(t){"use strict";var e=TypeError;t.exports=function(t){if(t>0x1fffffffffffff)throw e("Maximum allowed index exceeded");return t}},8808:function(t){"use strict";t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},47577:function(t){"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},13840:function(t,e,i){"use strict";var n=i(88627)("span").classList,r=n&&n.constructor&&n.constructor.prototype;t.exports=r===Object.prototype?void 0:r},85670:function(t){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},70453:function(t,e,i){"use strict";var n=i(92273).match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},89399:function(t,e,i){"use strict";var n=i(92273);t.exports=/MSIE|Trident/.test(n)},53497:function(t,e,i){"use strict";var n=i(92273);t.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},46455:function(t,e,i){"use strict";var n=i(92273);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},83639:function(t,e,i){"use strict";var n=i(84550);t.exports="NODE"===n},79708:function(t,e,i){"use strict";var n=i(92273);t.exports=/web0s(?!.*chrome)/i.test(n)},92273:function(t,e,i){"use strict";var n=i(42623).navigator,r=n&&n.userAgent;t.exports=r?String(r):""},52974:function(t,e,i){"use strict";var n,r,a=i(42623),s=i(92273),o=a.process,l=a.Deno,c=o&&o.versions||l&&l.version,u=c&&c.v8;u&&(r=(n=u.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!r&&s&&(!(n=s.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=s.match(/Chrome\/(\d+)/))&&(r=+n[1]),t.exports=r},88440:function(t,e,i){"use strict";var n=i(92273).match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},84550:function(t,e,i){"use strict";var n=i(42623),r=i(92273),a=i(89255),s=function(t){return r.slice(0,t.length)===t};t.exports=s("Bun/")?"BUN":s("Cloudflare-Workers")?"CLOUDFLARE":s("Deno/")?"DENO":s("Node.js/")?"NODE":n.Bun&&"string"==typeof Bun.version?"BUN":n.Deno&&"object"==typeof Deno.version?"DENO":"process"===a(n.process)?"NODE":n.window&&n.document?"BROWSER":"REST"},90742:function(t,e,i){"use strict";var n=i(26004),r=Error,a=n("".replace),s=String(new r("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(s);t.exports=function(t,e){if(l&&"string"==typeof t&&!r.prepareStackTrace)for(;e--;)t=a(t,o,"");return t}},40204:function(t,e,i){"use strict";var n=i(52801),r=i(90742),a=i(80421),s=Error.captureStackTrace;t.exports=function(t,e,i,o){a&&(s?s(t,e):n(t,"stack",r(i,o)))}},80421:function(t,e,i){"use strict";var n=i(81124),r=i(78407);t.exports=!n(function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",r(1,7)),7!==t.stack)})},96122:function(t,e,i){"use strict";var n=i(42623),r=i(75990).f,a=i(52801),s=i(51200),o=i(58511),l=i(22356),c=i(2849);t.exports=function(t,e){var i,u,h,d,p,f=t.target,_=t.global,v=t.stat;if(i=_?n:v?n[f]||o(f,{}):n[f]&&n[f].prototype)for(u in e){if(d=e[u],h=t.dontCallGetSet?(p=r(i,u))&&p.value:i[u],!c(_?u:f+(v?".":"#")+u,t.forced)&&void 0!==h){if(typeof d==typeof h)continue;l(d,h)}(t.sham||h&&h.sham)&&a(d,"sham",!0),s(i,u,d,t)}}},81124:function(t){"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},41395:function(t,e,i){"use strict";i(56113);var n=i(59528),r=i(51200),a=i(64191),s=i(81124),o=i(21755),l=i(52801),c=o("species"),u=RegExp.prototype;t.exports=function(t,e,i,h){var d=o(t),p=!s(function(){var e={};return e[d]=function(){return 7},7!==""[t](e)}),f=p&&!s(function(){var e=!1,i=/a/;return"split"===t&&((i={}).constructor={},i.constructor[c]=function(){return i},i.flags="",i[d]=/./[d]),i.exec=function(){return e=!0,null},i[d](""),!e});if(!p||!f||i){var _=/./[d],v=e(d,""[t],function(t,e,i,r,s){var o=e.exec;return o===a||o===u.exec?p&&!s?{done:!0,value:n(_,e,i,r)}:{done:!0,value:n(t,i,e,r)}:{done:!1}});r(String.prototype,t,v[0]),r(u,d,v[1])}h&&l(u[d],"sham",!0)}},63451:function(t,e,i){"use strict";var n=i(15),r=Function.prototype,a=r.apply,s=r.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?s.bind(a):function(){return s.apply(a,arguments)})},1480:function(t,e,i){"use strict";var n=i(77434),r=i(94112),a=i(15),s=n(n.bind);t.exports=function(t,e){return r(t),void 0===e?t:a?s(t,e):function(){return t.apply(e,arguments)}}},15:function(t,e,i){"use strict";var n=i(81124);t.exports=!n(function(){var t=(function(){}).bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},59528:function(t,e,i){"use strict";var n=i(15),r=Function.prototype.call;t.exports=n?r.bind(r):function(){return r.apply(r,arguments)}},70459:function(t,e,i){"use strict";var n=i(97223),r=i(29027),a=Function.prototype,s=n&&Object.getOwnPropertyDescriptor,o=r(a,"name"),l=o&&(!n||n&&s(a,"name").configurable);t.exports={EXISTS:o,PROPER:o&&"something"===(function(){}).name,CONFIGURABLE:l}},53923:function(t,e,i){"use strict";var n=i(26004),r=i(94112);t.exports=function(t,e,i){try{return n(r(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}}},77434:function(t,e,i){"use strict";var n=i(89255),r=i(26004);t.exports=function(t){if("Function"===n(t))return r(t)}},26004:function(t,e,i){"use strict";var n=i(15),r=Function.prototype,a=r.call,s=n&&r.bind.bind(a,a);t.exports=n?s:function(t){return function(){return a.apply(t,arguments)}}},6387:function(t,e,i){"use strict";var n=i(42623),r=i(83639);t.exports=function(t){if(r){try{return n.process.getBuiltinModule(t)}catch(t){}try{return Function('return require("'+t+'")')()}catch(t){}}}},93345:function(t,e,i){"use strict";var n=i(42623),r=i(28917);t.exports=function(t,e){var i;return arguments.length<2?r(i=n[t])?i:void 0:n[t]&&n[t][e]}},34174:function(t){"use strict";t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},2691:function(t,e,i){"use strict";var n=i(70422),r=i(84867),a=i(23671),s=i(86699),o=i(21755)("iterator");t.exports=function(t){if(!a(t))return r(t,o)||r(t,"@@iterator")||s[n(t)]}},85773:function(t,e,i){"use strict";var n=i(59528),r=i(94112),a=i(98903),s=i(76934),o=i(2691),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(r(i))return a(n(i,t));throw new l(s(t)+" is not iterable")}},84867:function(t,e,i){"use strict";var n=i(94112),r=i(23671);t.exports=function(t,e){var i=t[e];return r(i)?void 0:n(i)}},96959:function(t,e,i){"use strict";var n=i(94112),r=i(98903),a=i(59528),s=i(17940),o=i(34174),l="Invalid size",c=RangeError,u=TypeError,h=Math.max,d=function(t,e){this.set=t,this.size=h(e,0),this.has=n(t.has),this.keys=n(t.keys)};d.prototype={getIterator:function(){return o(r(a(this.keys,this.set)))},includes:function(t){return a(this.has,this.set,t)}},t.exports=function(t){r(t);var e=+t.size;if(e!=e)throw new u(l);var i=s(e);if(i<0)throw new c(l);return new d(t,i)}},38710:function(t,e,i){"use strict";var n=i(26004),r=i(87620),a=Math.floor,s=n("".charAt),o=n("".replace),l=n("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,u=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,i,n,h,d){var p=i+t.length,f=n.length,_=u;return void 0!==h&&(h=r(h),_=c),o(d,_,function(r,o){var c;switch(s(o,0)){case"$":return"$";case"&":return t;case"`":return l(e,0,i);case"'":return l(e,p);case"<":c=h[l(o,1,-1)];break;default:var u=+o;if(0===u)return r;if(u>f){var d=a(u/10);if(0===d)return r;if(d<=f)return void 0===n[d-1]?s(o,1):n[d-1]+s(o,1);return r}c=n[u-1]}return void 0===c?"":c})}},42623:function(t,e,i){"use strict";var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof i.g&&i.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},29027:function(t,e,i){"use strict";var n=i(26004),r=i(87620),a=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return a(r(t),e)}},52465:function(t){"use strict";t.exports={}},66206:function(t){"use strict";t.exports=function(t,e){try{1==arguments.length?console.error(t):console.error(t,e)}catch(t){}}},23783:function(t,e,i){"use strict";var n=i(93345);t.exports=n("document","documentElement")},21002:function(t,e,i){"use strict";var n=i(97223),r=i(81124),a=i(88627);t.exports=!n&&!r(function(){return 7!==Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a})},34223:function(t){"use strict";var e=Array,i=Math.abs,n=Math.pow,r=Math.floor,a=Math.log,s=Math.LN2;t.exports={pack:function(t,o,l){var c,u,h,d=e(l),p=8*l-o-1,f=(1<<p)-1,_=f>>1,v=23===o?n(2,-24)-n(2,-77):0,m=+(t<0||0===t&&1/t<0),g=0;for((t=i(t))!=t||t===1/0?(u=+(t!=t),c=f):(h=n(2,-(c=r(a(t)/s))),t*h<1&&(c--,h*=2),c+_>=1?t+=v/h:t+=v*n(2,1-_),t*h>=2&&(c++,h/=2),c+_>=f?(u=0,c=f):c+_>=1?(u=(t*h-1)*n(2,o),c+=_):(u=t*n(2,_-1)*n(2,o),c=0));o>=8;)d[g++]=255&u,u/=256,o-=8;for(c=c<<o|u,p+=o;p>0;)d[g++]=255&c,c/=256,p-=8;return d[g-1]|=128*m,d},unpack:function(t,e){var i,r=t.length,a=8*r-e-1,s=(1<<a)-1,o=s>>1,l=a-7,c=r-1,u=t[c--],h=127&u;for(u>>=7;l>0;)h=256*h+t[c--],l-=8;for(i=h&(1<<-l)-1,h>>=-l,l+=e;l>0;)i=256*i+t[c--],l-=8;if(0===h)h=1-o;else{if(h===s)return i?NaN:u?-1/0:1/0;i+=n(2,e),h-=o}return(u?-1:1)*i*n(2,h-e)}}},1994:function(t,e,i){"use strict";var n=i(26004),r=i(81124),a=i(89255),s=Object,o=n("".split);t.exports=r(function(){return!s("z").propertyIsEnumerable(0)})?function(t){return"String"===a(t)?o(t,""):s(t)}:s},31858:function(t,e,i){"use strict";var n=i(28917),r=i(10136),a=i(6887);t.exports=function(t,e,i){var s,o;return a&&n(s=e.constructor)&&s!==i&&r(o=s.prototype)&&o!==i.prototype&&a(t,o),t}},16861:function(t,e,i){"use strict";var n=i(26004),r=i(28917),a=i(6952),s=n(Function.toString);r(a.inspectSource)||(a.inspectSource=function(t){return s(t)}),t.exports=a.inspectSource},20334:function(t,e,i){"use strict";var n=i(10136),r=i(52801);t.exports=function(t,e){n(e)&&"cause"in e&&r(t,"cause",e.cause)}},67875:function(t,e,i){"use strict";var n,r,a,s=i(83839),o=i(42623),l=i(10136),c=i(52801),u=i(29027),h=i(6952),d=i(85514),p=i(52465),f="Object already initialized",_=o.TypeError,v=o.WeakMap;if(s||h.state){var m=h.state||(h.state=new v);m.get=m.get,m.has=m.has,m.set=m.set,n=function(t,e){if(m.has(t))throw new _(f);return e.facade=t,m.set(t,e),e},r=function(t){return m.get(t)||{}},a=function(t){return m.has(t)}}else{var g=d("state");p[g]=!0,n=function(t,e){if(u(t,g))throw new _(f);return e.facade=t,c(t,g,e),e},r=function(t){return u(t,g)?t[g]:{}},a=function(t){return u(t,g)}}t.exports={set:n,get:r,has:a,enforce:function(t){return a(t)?r(t):n(t,{})},getterFor:function(t){return function(e){var i;if(!l(e)||(i=r(e)).type!==t)throw new _("Incompatible receiver, "+t+" required");return i}}}},30059:function(t,e,i){"use strict";var n=i(21755),r=i(86699),a=n("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||s[a]===t)}},43095:function(t,e,i){"use strict";var n=i(89255);t.exports=Array.isArray||function(t){return"Array"===n(t)}},69488:function(t,e,i){"use strict";var n=i(70422);t.exports=function(t){var e=n(t);return"BigInt64Array"===e||"BigUint64Array"===e}},28917:function(t){"use strict";var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},32410:function(t,e,i){"use strict";var n=i(26004),r=i(81124),a=i(28917),s=i(70422),o=i(93345),l=i(16861),c=function(){},u=o("Reflect","construct"),h=/^\s*(?:class|function)\b/,d=n(h.exec),p=!h.test(c),f=function(t){if(!a(t))return!1;try{return u(c,[],t),!0}catch(t){return!1}},_=function(t){if(!a(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!d(h,l(t))}catch(t){return!0}};_.sham=!0,t.exports=!u||r(function(){var t;return f(f.call)||!f(Object)||!f(function(){t=!0})||t})?_:f},2849:function(t,e,i){"use strict";var n=i(81124),r=i(28917),a=/#|\.prototype\./,s=function(t,e){var i=l[o(t)];return i===u||i!==c&&(r(e)?n(e):!!e)},o=s.normalize=function(t){return String(t).replace(a,".").toLowerCase()},l=s.data={},c=s.NATIVE="N",u=s.POLYFILL="P";t.exports=s},64523:function(t,e,i){"use strict";var n=i(10136),r=Math.floor;t.exports=Number.isInteger||function(t){return!n(t)&&isFinite(t)&&r(t)===t}},23671:function(t){"use strict";t.exports=function(t){return null==t}},10136:function(t,e,i){"use strict";var n=i(28917);t.exports=function(t){return"object"==typeof t?null!==t:n(t)}},38549:function(t,e,i){"use strict";var n=i(10136);t.exports=function(t){return n(t)||null===t}},52512:function(t){"use strict";t.exports=!1},45903:function(t,e,i){"use strict";var n=i(10136),r=i(89255),a=i(21755)("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[a])?!!e:"RegExp"===r(t))}},36994:function(t,e,i){"use strict";var n=i(93345),r=i(28917),a=i(84776),s=i(22069),o=Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return r(e)&&a(e.prototype,o(t))}},92550:function(t,e,i){"use strict";var n=i(59528);t.exports=function(t,e,i){for(var r,a,s=i?t:t.iterator,o=t.next;!(r=n(o,s)).done;)if(void 0!==(a=e(r.value)))return a}},95342:function(t,e,i){"use strict";var n=i(1480),r=i(59528),a=i(98903),s=i(76934),o=i(30059),l=i(8785),c=i(84776),u=i(85773),h=i(2691),d=i(99584),p=TypeError,f=function(t,e){this.stopped=t,this.result=e},_=f.prototype;t.exports=function(t,e,i){var v,m,g,y,w,b,S,k=i&&i.that,A=!!(i&&i.AS_ENTRIES),x=!!(i&&i.IS_RECORD),O=!!(i&&i.IS_ITERATOR),U=!!(i&&i.INTERRUPTED),E=n(e,k),P=function(t){return v&&d(v,"normal",t),new f(!0,t)},C=function(t){return A?(a(t),U?E(t[0],t[1],P):E(t[0],t[1])):U?E(t,P):E(t)};if(x)v=t.iterator;else if(O)v=t;else{if(!(m=h(t)))throw new p(s(t)+" is not iterable");if(o(m)){for(g=0,y=l(t);y>g;g++)if((w=C(t[g]))&&c(_,w))return w;return new f(!1)}v=u(t,m)}for(b=x?t.next:v.next;!(S=r(b,v)).done;){try{w=C(S.value)}catch(t){d(v,"throw",t)}if("object"==typeof w&&w&&c(_,w))return w}return new f(!1)}},99584:function(t,e,i){"use strict";var n=i(59528),r=i(98903),a=i(84867);t.exports=function(t,e,i){var s,o;r(t);try{if(!(s=a(t,"return"))){if("throw"===e)throw i;return i}s=n(s,t)}catch(t){o=!0,s=t}if("throw"===e)throw i;if(o)throw s;return r(s),i}},48256:function(t,e,i){"use strict";var n=i(71295).IteratorPrototype,r=i(38270),a=i(78407),s=i(83244),o=i(86699),l=function(){return this};t.exports=function(t,e,i,c){var u=e+" Iterator";return t.prototype=r(n,{next:a(+!c,i)}),s(t,u,!1,!0),o[u]=l,t}},17591:function(t,e,i){"use strict";var n=i(96122),r=i(59528),a=i(52512),s=i(70459),o=i(28917),l=i(48256),c=i(32957),u=i(6887),h=i(83244),d=i(52801),p=i(51200),f=i(21755),_=i(86699),v=i(71295),m=s.PROPER,g=s.CONFIGURABLE,y=v.IteratorPrototype,w=v.BUGGY_SAFARI_ITERATORS,b=f("iterator"),S="keys",k="values",A="entries",x=function(){return this};t.exports=function(t,e,i,s,f,v,O){l(i,e,s);var U,E,P,C=function(t){if(t===f&&N)return N;if(!w&&t&&t in R)return R[t];switch(t){case S:case k:case A:return function(){return new i(this,t)}}return function(){return new i(this)}},I=e+" Iterator",T=!1,R=t.prototype,M=R[b]||R["@@iterator"]||f&&R[f],N=!w&&M||C(f),z="Array"===e&&R.entries||M;if(z&&(U=c(z.call(new t)))!==Object.prototype&&U.next&&(a||c(U)===y||(u?u(U,y):o(U[b])||p(U,b,x)),h(U,I,!0,!0),a&&(_[I]=x)),m&&f===k&&M&&M.name!==k&&(!a&&g?d(R,"name",k):(T=!0,N=function(){return r(M,this)})),f){if(E={values:C(k),keys:v?N:C(S),entries:C(A)},O)for(P in E)!w&&!T&&P in R||p(R,P,E[P]);else n({target:e,proto:!0,forced:w||T},E)}return(!a||O)&&R[b]!==N&&p(R,b,N,{name:f}),_[e]=N,E}},71295:function(t,e,i){"use strict";var n,r,a,s=i(81124),o=i(28917),l=i(10136),c=i(38270),u=i(32957),h=i(51200),d=i(21755),p=i(52512),f=d("iterator"),_=!1;[].keys&&("next"in(a=[].keys())?(r=u(u(a)))!==Object.prototype&&(n=r):_=!0),!l(n)||s(function(){var t={};return n[f].call(t)!==t})?n={}:p&&(n=c(n)),o(n[f])||h(n,f,function(){return this}),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:_}},86699:function(t){"use strict";t.exports={}},8785:function(t,e,i){"use strict";var n=i(11934);t.exports=function(t){return n(t.length)}},83358:function(t,e,i){"use strict";var n=i(26004),r=i(81124),a=i(28917),s=i(29027),o=i(97223),l=i(70459).CONFIGURABLE,c=i(16861),u=i(67875),h=u.enforce,d=u.get,p=String,f=Object.defineProperty,_=n("".slice),v=n("".replace),m=n([].join),g=o&&!r(function(){return 8!==f(function(){},"length",{value:8}).length}),y=String(String).split("String"),w=t.exports=function(t,e,i){"Symbol("===_(p(e),0,7)&&(e="["+v(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),i&&i.getter&&(e="get "+e),i&&i.setter&&(e="set "+e),(!s(t,"name")||l&&t.name!==e)&&(o?f(t,"name",{value:e,configurable:!0}):t.name=e),g&&i&&s(i,"arity")&&t.length!==i.arity&&f(t,"length",{value:i.arity});try{i&&s(i,"constructor")&&i.constructor?o&&f(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=h(t);return s(n,"source")||(n.source=m(y,"string"==typeof e?e:"")),t};Function.prototype.toString=w(function(){return a(this)&&d(this).source||c(this)},"toString")},61547:function(t,e,i){"use strict";var n=i(60680),r=i(66289),a=Math.abs;t.exports=function(t,e,i,s){var o=+t,l=a(o),c=n(o);if(l<s)return c*r(l/s/e)*s*e;var u=(1+e/2220446049250313e-31)*l,h=u-(u-l);return h>i||h!=h?1/0*c:c*h}},61102:function(t,e,i){"use strict";var n=i(61547);t.exports=Math.fround||function(t){return n(t,11920928955078125e-23,34028234663852886e22,11754943508222875e-54)}},66289:function(t){"use strict";t.exports=function(t){return t+0x10000000000000-0x10000000000000}},60680:function(t){"use strict";t.exports=Math.sign||function(t){var e=+t;return 0===e||e!=e?e:e<0?-1:1}},22664:function(t){"use strict";var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?i:e)(n)}},64635:function(t,e,i){"use strict";var n,r,a,s,o,l=i(42623),c=i(22054),u=i(1480),h=i(54469).set,d=i(95133),p=i(46455),f=i(53497),_=i(79708),v=i(83639),m=l.MutationObserver||l.WebKitMutationObserver,g=l.document,y=l.process,w=l.Promise,b=c("queueMicrotask");if(!b){var S=new d,k=function(){var t,e;for(v&&(t=y.domain)&&t.exit();e=S.get();)try{e()}catch(t){throw S.head&&n(),t}t&&t.enter()};p||v||_||!m||!g?!f&&w&&w.resolve?((s=w.resolve(void 0)).constructor=w,o=u(s.then,s),n=function(){o(k)}):v?n=function(){y.nextTick(k)}:(h=u(h,l),n=function(){h(k)}):(r=!0,a=g.createTextNode(""),new m(k).observe(a,{characterData:!0}),n=function(){a.data=r=!r}),b=function(t){S.head||n(),S.add(t)}}t.exports=b},41267:function(t,e,i){"use strict";var n=i(94112),r=TypeError,a=function(t){var e,i;this.promise=new t(function(t,n){if(void 0!==e||void 0!==i)throw new r("Bad Promise constructor");e=t,i=n}),this.resolve=n(e),this.reject=n(i)};t.exports.f=function(t){return new a(t)}},25441:function(t,e,i){"use strict";var n=i(86596);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},37135:function(t,e,i){"use strict";var n=i(97223),r=i(26004),a=i(59528),s=i(81124),o=i(99112),l=i(92692),c=i(3266),u=i(87620),h=i(1994),d=Object.assign,p=Object.defineProperty,f=r([].concat);t.exports=!d||s(function(){if(n&&1!==d({b:1},d(p({},"a",{enumerable:!0,get:function(){p(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol("assign detection"),r="abcdefghijklmnopqrst";return t[i]=7,r.split("").forEach(function(t){e[t]=t}),7!==d({},t)[i]||o(d({},e)).join("")!==r})?function(t,e){for(var i=u(t),r=arguments.length,s=1,d=l.f,p=c.f;r>s;)for(var _,v=h(arguments[s++]),m=d?f(o(v),d(v)):o(v),g=m.length,y=0;g>y;)_=m[y++],(!n||a(p,v,_))&&(i[_]=v[_]);return i}:d},38270:function(t,e,i){"use strict";var n,r=i(98903),a=i(22995),s=i(85670),o=i(52465),l=i(23783),c=i(88627),u=i(85514),h="prototype",d="script",p=u("IE_PROTO"),f=function(){},_=function(t){return"<"+d+">"+t+"</"+d+">"},v=function(t){t.write(_("")),t.close();var e=t.parentWindow.Object;return t=null,e},m=function(){var t,e=c("iframe");return e.style.display="none",l.appendChild(e),e.src=String("java"+d+":"),(t=e.contentWindow.document).open(),t.write(_("document.F=Object")),t.close(),t.F},g=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}g="undefined"!=typeof document?document.domain&&n?v(n):m():v(n);for(var t=s.length;t--;)delete g[h][s[t]];return g()};o[p]=!0,t.exports=Object.create||function(t,e){var i;return null!==t?(f[h]=r(t),i=new f,f[h]=null,i[p]=t):i=g(),void 0===e?i:a.f(i,e)}},22995:function(t,e,i){"use strict";var n=i(97223),r=i(23514),a=i(79406),s=i(98903),o=i(48246),l=i(99112);e.f=n&&!r?Object.defineProperties:function(t,e){s(t);for(var i,n=o(e),r=l(e),c=r.length,u=0;c>u;)a.f(t,i=r[u++],n[i]);return t}},79406:function(t,e,i){"use strict";var n=i(97223),r=i(21002),a=i(23514),s=i(98903),o=i(53154),l=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,h="enumerable",d="configurable",p="writable";e.f=n?a?function(t,e,i){if(s(t),e=o(e),s(i),"function"==typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var n=u(t,e);n&&n[p]&&(t[e]=i.value,i={configurable:d in i?i[d]:n[d],enumerable:h in i?i[h]:n[h],writable:!1})}return c(t,e,i)}:c:function(t,e,i){if(s(t),e=o(e),s(i),r)try{return c(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new l("Accessors not supported");return"value"in i&&(t[e]=i.value),t}},75990:function(t,e,i){"use strict";var n=i(97223),r=i(59528),a=i(3266),s=i(78407),o=i(48246),l=i(53154),c=i(29027),u=i(21002),h=Object.getOwnPropertyDescriptor;e.f=n?h:function(t,e){if(t=o(t),e=l(e),u)try{return h(t,e)}catch(t){}if(c(t,e))return s(!r(a.f,t,e),t[e])}},84315:function(t,e,i){"use strict";var n=i(5950),r=i(85670).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,r)}},92692:function(t,e){"use strict";e.f=Object.getOwnPropertySymbols},32957:function(t,e,i){"use strict";var n=i(29027),r=i(28917),a=i(87620),s=i(85514),o=i(40260),l=s("IE_PROTO"),c=Object,u=c.prototype;t.exports=o?c.getPrototypeOf:function(t){var e=a(t);if(n(e,l))return e[l];var i=e.constructor;return r(i)&&e instanceof i?i.prototype:e instanceof c?u:null}},84776:function(t,e,i){"use strict";var n=i(26004);t.exports=n({}.isPrototypeOf)},5950:function(t,e,i){"use strict";var n=i(26004),r=i(29027),a=i(48246),s=i(8555).indexOf,o=i(52465),l=n([].push);t.exports=function(t,e){var i,n=a(t),c=0,u=[];for(i in n)!r(o,i)&&r(n,i)&&l(u,i);for(;e.length>c;)r(n,i=e[c++])&&(~s(u,i)||l(u,i));return u}},99112:function(t,e,i){"use strict";var n=i(5950),r=i(85670);t.exports=Object.keys||function(t){return n(t,r)}},3266:function(t,e){"use strict";var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,r=n&&!i.call({1:2},1);e.f=r?function(t){var e=n(this,t);return!!e&&e.enumerable}:i},6887:function(t,e,i){"use strict";var n=i(53923),r=i(10136),a=i(65977),s=i(38744);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=n(Object.prototype,"__proto__","set"))(i,[]),e=i instanceof Array}catch(t){}return function(i,n){return a(i),s(n),r(i)&&(e?t(i,n):i.__proto__=n),i}}():void 0)},6397:function(t,e,i){"use strict";var n=i(59528),r=i(28917),a=i(10136),s=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&r(i=t.toString)&&!a(o=n(i,t))||r(i=t.valueOf)&&!a(o=n(i,t))||"string"!==e&&r(i=t.toString)&&!a(o=n(i,t)))return o;throw new s("Can't convert object to primitive value")}},94224:function(t,e,i){"use strict";var n=i(93345),r=i(26004),a=i(84315),s=i(92692),o=i(98903),l=r([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=a.f(o(t)),i=s.f;return i?l(e,i(t)):e}},40857:function(t){"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},6585:function(t,e,i){"use strict";var n=i(42623),r=i(46267),a=i(28917),s=i(2849),o=i(16861),l=i(21755),c=i(84550),u=i(52512),h=i(52974),d=r&&r.prototype,p=l("species"),f=!1,_=a(n.PromiseRejectionEvent),v=s("Promise",function(){var t=o(r),e=t!==String(r);if(!e&&66===h||u&&!(d.catch&&d.finally))return!0;if(!h||h<51||!/native code/.test(t)){var i=new r(function(t){t(1)}),n=function(t){t(function(){},function(){})};if((i.constructor={})[p]=n,!(f=i.then(function(){})instanceof n))return!0}return!e&&("BROWSER"===c||"DENO"===c)&&!_});t.exports={CONSTRUCTOR:v,REJECTION_EVENT:_,SUBCLASSING:f}},46267:function(t,e,i){"use strict";var n=i(42623);t.exports=n.Promise},11785:function(t,e,i){"use strict";var n=i(98903),r=i(10136),a=i(41267);t.exports=function(t,e){if(n(t),r(e)&&e.constructor===t)return e;var i=a.f(t);return(0,i.resolve)(e),i.promise}},89633:function(t,e,i){"use strict";var n=i(46267),r=i(69815),a=i(6585).CONSTRUCTOR;t.exports=a||!r(function(t){n.all(t).then(void 0,function(){})})},86876:function(t,e,i){"use strict";var n=i(79406).f;t.exports=function(t,e,i){i in t||n(t,i,{configurable:!0,get:function(){return e[i]},set:function(t){e[i]=t}})}},95133:function(t){"use strict";var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},i=this.tail;i?i.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},32955:function(t,e,i){"use strict";var n=i(59528),r=i(98903),a=i(28917),s=i(89255),o=i(64191),l=TypeError;t.exports=function(t,e){var i=t.exec;if(a(i)){var c=n(i,t,e);return null!==c&&r(c),c}if("RegExp"===s(t))return n(o,t,e);throw new l("RegExp#exec called on incompatible receiver")}},64191:function(t,e,i){"use strict";var n,r,a=i(59528),s=i(26004),o=i(86596),l=i(17219),c=i(96739),u=i(25127),h=i(38270),d=i(67875).get,p=i(8297),f=i(25129),_=u("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,m=v,g=s("".charAt),y=s("".indexOf),w=s("".replace),b=s("".slice),S=(r=/b*/g,a(v,n=/a/,"a"),a(v,r,"a"),0!==n.lastIndex||0!==r.lastIndex),k=c.BROKEN_CARET,A=void 0!==/()??/.exec("")[1];(S||A||k||p||f)&&(m=function(t){var e,i,n,r,s,c,u,p=d(this),f=o(t),x=p.raw;if(x)return x.lastIndex=this.lastIndex,e=a(m,x,f),this.lastIndex=x.lastIndex,e;var O=p.groups,U=k&&this.sticky,E=a(l,this),P=this.source,C=0,I=f;if(U&&(-1===y(E=w(E,"y",""),"g")&&(E+="g"),I=b(f,this.lastIndex),this.lastIndex>0&&(!this.multiline||this.multiline&&"\n"!==g(f,this.lastIndex-1))&&(P="(?: "+P+")",I=" "+I,C++),i=RegExp("^(?:"+P+")",E)),A&&(i=RegExp("^"+P+"$(?!\\s)",E)),S&&(n=this.lastIndex),r=a(v,U?i:this,I),U?r?(r.input=b(r.input,C),r[0]=b(r[0],C),r.index=this.lastIndex,this.lastIndex+=r[0].length):this.lastIndex=0:S&&r&&(this.lastIndex=this.global?r.index+r[0].length:n),A&&r&&r.length>1&&a(_,r[0],i,function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(r[s]=void 0)}),r&&O)for(s=0,r.groups=c=h(null);s<O.length;s++)c[(u=O[s])[0]]=r[u[1]];return r}),t.exports=m},17219:function(t,e,i){"use strict";var n=i(98903);t.exports=function(){var t=n(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},61358:function(t,e,i){"use strict";var n=i(59528),r=i(29027),a=i(84776),s=i(17219),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0===e&&!("flags"in o)&&!r(t,"flags")&&a(o,t)?n(s,t):e}},96739:function(t,e,i){"use strict";var n=i(81124),r=i(42623).RegExp,a=n(function(){var t=r("a","y");return t.lastIndex=2,null!==t.exec("abcd")}),s=a||n(function(){return!r("a","y").sticky}),o=a||n(function(){var t=r("^r","gy");return t.lastIndex=2,null!==t.exec("str")});t.exports={BROKEN_CARET:o,MISSED_STICKY:s,UNSUPPORTED_Y:a}},8297:function(t,e,i){"use strict";var n=i(81124),r=i(42623).RegExp;t.exports=n(function(){var t=r(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})},25129:function(t,e,i){"use strict";var n=i(81124),r=i(42623).RegExp;t.exports=n(function(){var t=r("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})},65977:function(t,e,i){"use strict";var n=i(23671),r=TypeError;t.exports=function(t){if(n(t))throw new r("Can't call method on "+t);return t}},22054:function(t,e,i){"use strict";var n=i(42623),r=i(97223),a=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!r)return n[t];var e=a(n,t);return e&&e.value}},55968:function(t,e,i){"use strict";var n=i(7873),r=i(66937),a=n.Set,s=n.add;t.exports=function(t){var e=new a;return r(t,function(t){s(e,t)}),e}},76794:function(t,e,i){"use strict";var n=i(80656),r=i(7873),a=i(55968),s=i(64091),o=i(96959),l=i(66937),c=i(92550),u=r.has,h=r.remove;t.exports=function(t){var e=n(this),i=o(t),r=a(e);return s(e)<=i.size?l(e,function(t){i.includes(t)&&h(r,t)}):c(i.getIterator(),function(t){u(e,t)&&h(r,t)}),r}},7873:function(t,e,i){"use strict";var n=i(26004),r=Set.prototype;t.exports={Set:Set,add:n(r.add),has:n(r.has),remove:n(r.delete),proto:r}},84629:function(t,e,i){"use strict";var n=i(80656),r=i(7873),a=i(64091),s=i(96959),o=i(66937),l=i(92550),c=r.Set,u=r.add,h=r.has;t.exports=function(t){var e=n(this),i=s(t),r=new c;return a(e)>i.size?l(i.getIterator(),function(t){h(e,t)&&u(r,t)}):o(e,function(t){i.includes(t)&&u(r,t)}),r}},27775:function(t,e,i){"use strict";var n=i(80656),r=i(7873).has,a=i(64091),s=i(96959),o=i(66937),l=i(92550),c=i(99584);t.exports=function(t){var e=n(this),i=s(t);if(a(e)<=i.size)return!1!==o(e,function(t){if(i.includes(t))return!1},!0);var u=i.getIterator();return!1!==l(u,function(t){if(r(e,t))return c(u,"normal",!1)})}},82137:function(t,e,i){"use strict";var n=i(80656),r=i(64091),a=i(66937),s=i(96959);t.exports=function(t){var e=n(this),i=s(t);return!(r(e)>i.size)&&!1!==a(e,function(t){if(!i.includes(t))return!1},!0)}},19947:function(t,e,i){"use strict";var n=i(80656),r=i(7873).has,a=i(64091),s=i(96959),o=i(92550),l=i(99584);t.exports=function(t){var e=n(this),i=s(t);if(a(e)<i.size)return!1;var c=i.getIterator();return!1!==o(c,function(t){if(!r(e,t))return l(c,"normal",!1)})}},66937:function(t,e,i){"use strict";var n=i(26004),r=i(92550),a=i(7873),s=a.Set,o=a.proto,l=n(o.forEach),c=n(o.keys),u=c(new s).next;t.exports=function(t,e,i){return i?r({iterator:c(t),next:u},e):l(t,e)}},90785:function(t,e,i){"use strict";var n=i(93345),r=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},a=function(t){return{size:t,has:function(){return!0},keys:function(){throw Error("e")}}};t.exports=function(t,e){var i=n("Set");try{new i()[t](r(0));try{return new i()[t](r(-1)),!1}catch(n){if(!e)return!0;try{return new i()[t](a(-1/0)),!1}catch(n){var s=new i;return s.add(1),s.add(2),e(s[t](a(1/0)))}}}catch(t){return!1}}},64091:function(t,e,i){"use strict";var n=i(53923),r=i(7873);t.exports=n(r.proto,"size","get")||function(t){return t.size}},80974:function(t,e,i){"use strict";var n=i(93345),r=i(22700),a=i(21755),s=i(97223),o=a("species");t.exports=function(t){var e=n(t);s&&e&&!e[o]&&r(e,o,{configurable:!0,get:function(){return this}})}},73854:function(t,e,i){"use strict";var n=i(80656),r=i(7873),a=i(55968),s=i(96959),o=i(92550),l=r.add,c=r.has,u=r.remove;t.exports=function(t){var e=n(this),i=s(t).getIterator(),r=a(e);return o(i,function(t){c(e,t)?u(r,t):l(r,t)}),r}},83244:function(t,e,i){"use strict";var n=i(79406).f,r=i(29027),a=i(21755)("toStringTag");t.exports=function(t,e,i){t&&!i&&(t=t.prototype),t&&!r(t,a)&&n(t,a,{configurable:!0,value:e})}},73406:function(t,e,i){"use strict";var n=i(80656),r=i(7873).add,a=i(55968),s=i(96959),o=i(92550);t.exports=function(t){var e=n(this),i=s(t).getIterator(),l=a(e);return o(i,function(t){r(l,t)}),l}},85514:function(t,e,i){"use strict";var n=i(25127),r=i(92947),a=n("keys");t.exports=function(t){return a[t]||(a[t]=r(t))}},6952:function(t,e,i){"use strict";var n=i(52512),r=i(42623),a=i(58511),s="__core-js_shared__",o=t.exports=r[s]||a(s,{});(o.versions||(o.versions=[])).push({version:"3.41.0",mode:n?"pure":"global",copyright:"\xa9 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.41.0/LICENSE",source:"https://github.com/zloirock/core-js"})},25127:function(t,e,i){"use strict";var n=i(6952);t.exports=function(t,e){return n[t]||(n[t]=e||{})}},7693:function(t,e,i){"use strict";var n=i(98903),r=i(29983),a=i(23671),s=i(21755)("species");t.exports=function(t,e){var i,o=n(t).constructor;return void 0===o||a(i=n(o)[s])?e:r(i)}},55321:function(t,e,i){"use strict";var n=i(26004),r=i(17940),a=i(86596),s=i(65977),o=n("".charAt),l=n("".charCodeAt),c=n("".slice),u=function(t){return function(e,i){var n,u,h=a(s(e)),d=r(i),p=h.length;return d<0||d>=p?t?"":void 0:(n=l(h,d))<55296||n>56319||d+1===p||(u=l(h,d+1))<56320||u>57343?t?o(h,d):n:t?c(h,d,d+2):(n-55296<<10)+(u-56320)+65536}};t.exports={codeAt:u(!1),charAt:u(!0)}},65660:function(t,e,i){"use strict";var n=i(26004),r=/[^\0-\u007E]/,a=/[.\u3002\uFF0E\uFF61]/g,s="Overflow: input needs wider integers to process",o=RangeError,l=n(a.exec),c=Math.floor,u=String.fromCharCode,h=n("".charCodeAt),d=n([].join),p=n([].push),f=n("".replace),_=n("".split),v=n("".toLowerCase),m=function(t){for(var e=[],i=0,n=t.length;i<n;){var r=h(t,i++);if(r>=55296&&r<=56319&&i<n){var a=h(t,i++);(64512&a)==56320?p(e,((1023&r)<<10)+(1023&a)+65536):(p(e,r),i--)}else p(e,r)}return e},g=function(t){return t+22+75*(t<26)},y=function(t,e,i){var n=0;for(t=i?c(t/700):t>>1,t+=c(t/e);t>455;)t=c(t/35),n+=36;return c(n+36*t/(t+38))},w=function(t){var e,i,n=[],r=(t=m(t)).length,a=128,l=0,h=72;for(e=0;e<t.length;e++)(i=t[e])<128&&p(n,u(i));var f=n.length,_=f;for(f&&p(n,"-");_<r;){var v=0x7fffffff;for(e=0;e<t.length;e++)(i=t[e])>=a&&i<v&&(v=i);var w=_+1;if(v-a>c((0x7fffffff-l)/w))throw new o(s);for(l+=(v-a)*w,a=v,e=0;e<t.length;e++){if((i=t[e])<a&&++l>0x7fffffff)throw new o(s);if(i===a){for(var b=l,S=36;;){var k=S<=h?1:S>=h+26?26:S-h;if(b<k)break;var A=b-k,x=36-k;p(n,u(g(k+A%x))),b=c(A/x),S+=36}p(n,u(g(b))),h=y(l,w,_===f),l=0,_++}}l++,a++}return d(n,"")};t.exports=function(t){var e,i,n=[],s=_(f(v(t),a,"."),".");for(e=0;e<s.length;e++)p(n,l(r,i=s[e])?"xn--"+w(i):i);return d(n,".")}},10354:function(t,e,i){"use strict";var n=i(42623),r=i(81124),a=i(52974),s=i(84550),o=n.structuredClone;t.exports=!!o&&!r(function(){if("DENO"===s&&a>92||"NODE"===s&&a>94||"BROWSER"===s&&a>97)return!1;var t=new ArrayBuffer(8),e=o(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength})},47504:function(t,e,i){"use strict";var n=i(52974),r=i(81124),a=i(42623).String;t.exports=!!Object.getOwnPropertySymbols&&!r(function(){var t=Symbol("symbol detection");return!a(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41})},54469:function(t,e,i){"use strict";var n,r,a,s,o=i(42623),l=i(63451),c=i(1480),u=i(28917),h=i(29027),d=i(81124),p=i(23783),f=i(88221),_=i(88627),v=i(72888),m=i(46455),g=i(83639),y=o.setImmediate,w=o.clearImmediate,b=o.process,S=o.Dispatch,k=o.Function,A=o.MessageChannel,x=o.String,O=0,U={},E="onreadystatechange";d(function(){n=o.location});var P=function(t){if(h(U,t)){var e=U[t];delete U[t],e()}},C=function(t){return function(){P(t)}},I=function(t){P(t.data)},T=function(t){o.postMessage(x(t),n.protocol+"//"+n.host)};y&&w||(y=function(t){v(arguments.length,1);var e=u(t)?t:k(t),i=f(arguments,1);return U[++O]=function(){l(e,void 0,i)},r(O),O},w=function(t){delete U[t]},g?r=function(t){b.nextTick(C(t))}:S&&S.now?r=function(t){S.now(C(t))}:A&&!m?(s=(a=new A).port2,a.port1.onmessage=I,r=c(s.postMessage,s)):o.addEventListener&&u(o.postMessage)&&!o.importScripts&&n&&"file:"!==n.protocol&&!d(T)?(r=T,o.addEventListener("message",I,!1)):r=E in _("script")?function(t){p.appendChild(_("script"))[E]=function(){p.removeChild(this),P(t)}}:function(t){setTimeout(C(t),0)}),t.exports={set:y,clear:w}},35745:function(t,e,i){"use strict";var n=i(17940),r=Math.max,a=Math.min;t.exports=function(t,e){var i=n(t);return i<0?r(i+e,0):a(i,e)}},43355:function(t,e,i){"use strict";var n=i(68121),r=TypeError;t.exports=function(t){var e=n(t,"number");if("number"==typeof e)throw new r("Can't convert number to bigint");return BigInt(e)}},23006:function(t,e,i){"use strict";var n=i(17940),r=i(11934),a=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=n(t),i=r(e);if(e!==i)throw new a("Wrong length or index");return i}},48246:function(t,e,i){"use strict";var n=i(1994),r=i(65977);t.exports=function(t){return n(r(t))}},17940:function(t,e,i){"use strict";var n=i(22664);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},11934:function(t,e,i){"use strict";var n=i(17940),r=Math.min;t.exports=function(t){var e=n(t);return e>0?r(e,0x1fffffffffffff):0}},87620:function(t,e,i){"use strict";var n=i(65977),r=Object;t.exports=function(t){return r(n(t))}},67432:function(t,e,i){"use strict";var n=i(53863),r=RangeError;t.exports=function(t,e){var i=n(t);if(i%e)throw new r("Wrong offset");return i}},53863:function(t,e,i){"use strict";var n=i(17940),r=RangeError;t.exports=function(t){var e=n(t);if(e<0)throw new r("The argument can't be less than 0");return e}},68121:function(t,e,i){"use strict";var n=i(59528),r=i(10136),a=i(36994),s=i(84867),o=i(6397),l=i(21755),c=TypeError,u=l("toPrimitive");t.exports=function(t,e){if(!r(t)||a(t))return t;var i,l=s(t,u);if(l){if(void 0===e&&(e="default"),!r(i=n(l,t,e))||a(i))return i;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},53154:function(t,e,i){"use strict";var n=i(68121),r=i(36994);t.exports=function(t){var e=n(t,"string");return r(e)?e:e+""}},85830:function(t,e,i){"use strict";var n=i(21755)("toStringTag"),r={};r[n]="z",t.exports="[object z]"===String(r)},86596:function(t,e,i){"use strict";var n=i(70422),r=String;t.exports=function(t){if("Symbol"===n(t))throw TypeError("Cannot convert a Symbol value to a string");return r(t)}},52308:function(t){"use strict";var e=Math.round;t.exports=function(t){var i=e(t);return i<0?0:i>255?255:255&i}},76934:function(t){"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},48095:function(t,e,i){"use strict";var n=i(96122),r=i(42623),a=i(59528),s=i(97223),o=i(5213),l=i(44708),c=i(66509),u=i(14644),h=i(78407),d=i(52801),p=i(64523),f=i(11934),_=i(23006),v=i(67432),m=i(52308),g=i(53154),y=i(29027),w=i(70422),b=i(10136),S=i(36994),k=i(38270),A=i(84776),x=i(6887),O=i(84315).f,U=i(48063),E=i(94238).forEach,P=i(80974),C=i(22700),I=i(79406),T=i(75990),R=i(97338),M=i(67875),N=i(31858),z=M.get,D=M.set,L=M.enforce,W=I.f,F=T.f,j=r.RangeError,V=c.ArrayBuffer,K=V.prototype,B=c.DataView,$=l.NATIVE_ARRAY_BUFFER_VIEWS,J=l.TYPED_ARRAY_TAG,q=l.TypedArray,G=l.TypedArrayPrototype,H=l.isTypedArray,Z="BYTES_PER_ELEMENT",Y="Wrong length",X=function(t,e){C(t,e,{configurable:!0,get:function(){return z(this)[e]}})},Q=function(t){var e;return A(K,t)||"ArrayBuffer"===(e=w(t))||"SharedArrayBuffer"===e},tt=function(t,e){return H(t)&&!S(e)&&e in t&&p(+e)&&e>=0},te=function(t,e){return tt(t,e=g(e))?h(2,t[e]):F(t,e)},ti=function(t,e,i){return tt(t,e=g(e))&&b(i)&&y(i,"value")&&!y(i,"get")&&!y(i,"set")&&!i.configurable&&(!y(i,"writable")||i.writable)&&(!y(i,"enumerable")||i.enumerable)?(t[e]=i.value,t):W(t,e,i)};s?($||(T.f=te,I.f=ti,X(G,"buffer"),X(G,"byteOffset"),X(G,"byteLength"),X(G,"length")),n({target:"Object",stat:!0,forced:!$},{getOwnPropertyDescriptor:te,defineProperty:ti}),t.exports=function(t,e,i){var s=t.match(/\d+/)[0]/8,l=t+(i?"Clamped":"")+"Array",c="get"+t,h="set"+t,p=r[l],g=p,y=g&&g.prototype,w={},S=function(t,e){var i=z(t);return i.view[c](e*s+i.byteOffset,!0)},A=function(t,e,n){var r=z(t);r.view[h](e*s+r.byteOffset,i?m(n):n,!0)},C=function(t,e){W(t,e,{get:function(){return S(this,e)},set:function(t){return A(this,e,t)},enumerable:!0})};$?o&&(g=e(function(t,e,i,n){return u(t,y),N(b(e)?Q(e)?void 0!==n?new p(e,v(i,s),n):void 0!==i?new p(e,v(i,s)):new p(e):H(e)?R(g,e):a(U,g,e):new p(_(e)),t,g)}),x&&x(g,q),E(O(p),function(t){t in g||d(g,t,p[t])}),g.prototype=y):(g=e(function(t,e,i,n){u(t,y);var r,o,l,c=0,h=0;if(b(e)){if(Q(e)){r=e,h=v(i,s);var d=e.byteLength;if(void 0===n){if(d%s||(o=d-h)<0)throw new j(Y)}else if((o=f(n)*s)+h>d)throw new j(Y);l=o/s}else if(H(e))return R(g,e);else return a(U,g,e)}else r=new V(o=(l=_(e))*s);for(D(t,{buffer:r,byteOffset:h,byteLength:o,length:l,view:new B(r)});c<l;)C(t,c++)}),x&&x(g,q),y=g.prototype=k(G)),y.constructor!==g&&d(y,"constructor",g),L(y).TypedArrayConstructor=g,J&&d(y,J,l);var I=g!==p;w[l]=g,n({global:!0,constructor:!0,forced:I,sham:!$},w),Z in g||d(g,Z,s),Z in y||d(y,Z,s),P(l)}):t.exports=function(){}},5213:function(t,e,i){"use strict";var n=i(42623),r=i(81124),a=i(69815),s=i(44708).NATIVE_ARRAY_BUFFER_VIEWS,o=n.ArrayBuffer,l=n.Int8Array;t.exports=!s||!r(function(){l(1)})||!r(function(){new l(-1)})||!a(function(t){new l,new l(null),new l(1.5),new l(t)},!0)||r(function(){return 1!==new l(new o(2),1,void 0).length})},48063:function(t,e,i){"use strict";var n=i(1480),r=i(59528),a=i(29983),s=i(87620),o=i(8785),l=i(85773),c=i(2691),u=i(30059),h=i(69488),d=i(44708).aTypedArrayConstructor,p=i(43355);t.exports=function(t){var e,i,f,_,v,m,g,y,w=a(this),b=s(t),S=arguments.length,k=S>1?arguments[1]:void 0,A=void 0!==k,x=c(b);if(x&&!u(x))for(y=(g=l(b,x)).next,b=[];!(m=r(y,g)).done;)b.push(m.value);for(A&&S>2&&(k=n(k,arguments[2])),i=o(b),_=h(f=new(d(w))(i)),e=0;i>e;e++)v=A?k(b[e],e):b[e],f[e]=_?p(v):+v;return f}},92947:function(t,e,i){"use strict";var n=i(26004),r=0,a=Math.random(),s=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+s(++r+a,36)}},19484:function(t,e,i){"use strict";var n=i(81124),r=i(21755),a=i(97223),s=i(52512),o=r("iterator");t.exports=!n(function(){var t=new URL("b?a=1&b=2&c=3","https://a"),e=t.searchParams,i=new URLSearchParams("a=1&a=2&b=3"),n="";return t.pathname="c%20d",e.forEach(function(t,i){e.delete("b"),n+=i+t}),i.delete("a",2),i.delete("b",void 0),s&&(!t.toJSON||!i.has("a",1)||i.has("a",2)||!i.has("a",void 0)||i.has("b"))||!e.size&&(s||!a)||!e.sort||"https://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==n||"x"!==new URL("https://x",void 0).host})},22069:function(t,e,i){"use strict";var n=i(47504);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},23514:function(t,e,i){"use strict";var n=i(97223),r=i(81124);t.exports=n&&r(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},72888:function(t){"use strict";var e=TypeError;t.exports=function(t,i){if(t<i)throw new e("Not enough arguments");return t}},83839:function(t,e,i){"use strict";var n=i(42623),r=i(28917),a=n.WeakMap;t.exports=r(a)&&/native code/.test(String(a))},21755:function(t,e,i){"use strict";var n=i(42623),r=i(25127),a=i(29027),s=i(92947),o=i(47504),l=i(22069),c=n.Symbol,u=r("wks"),h=l?c.for||c:c&&c.withoutSetter||s;t.exports=function(t){return a(u,t)||(u[t]=o&&a(c,t)?c[t]:h("Symbol."+t)),u[t]}},85863:function(t,e,i){"use strict";var n=i(93345),r=i(29027),a=i(52801),s=i(84776),o=i(6887),l=i(22356),c=i(86876),u=i(31858),h=i(25441),d=i(20334),p=i(40204),f=i(97223),_=i(52512);t.exports=function(t,e,i,v){var m="stackTraceLimit",g=v?2:1,y=t.split("."),w=y[y.length-1],b=n.apply(null,y);if(b){var S=b.prototype;if(!_&&r(S,"cause")&&delete S.cause,!i)return b;var k=n("Error"),A=e(function(t,e){var i=h(v?e:t,void 0),n=v?new b(t):new b;return void 0!==i&&a(n,"message",i),p(n,A,n.stack,2),this&&s(S,this)&&u(n,this,A),arguments.length>g&&d(n,arguments[g]),n});if(A.prototype=S,"Error"!==w?o?o(A,k):l(A,k,{name:!0}):f&&m in b&&(c(A,b,m),c(A,b,"prepareStackTrace")),l(A,b),!_)try{S.name!==w&&a(S,"name",w),S.constructor=A}catch(t){}return A}}},54357:function(t,e,i){"use strict";var n=i(97223),r=i(22700),a=i(58047),s=ArrayBuffer.prototype;!n||"detached"in s||r(s,"detached",{configurable:!0,get:function(){return a(this)}})},6763:function(t,e,i){"use strict";var n=i(96122),r=i(25126);r&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return r(this,arguments.length?arguments[0]:void 0,!1)}})},86775:function(t,e,i){"use strict";var n=i(96122),r=i(25126);r&&n({target:"ArrayBuffer",proto:!0},{transfer:function(){return r(this,arguments.length?arguments[0]:void 0,!0)}})},28419:function(t,e,i){"use strict";var n=i(96122),r=i(8555).includes,a=i(81124),s=i(63133);n({target:"Array",proto:!0,forced:a(function(){return![,].includes()})},{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),s("includes")},95467:function(t,e,i){"use strict";var n=i(48246),r=i(63133),a=i(86699),s=i(67875),o=i(79406).f,l=i(17591),c=i(77528),u=i(52512),h=i(97223),d="Array Iterator",p=s.set,f=s.getterFor(d);t.exports=l(Array,"Array",function(t,e){p(this,{type:d,target:n(t),index:0,kind:e})},function(){var t=f(this),e=t.target,i=t.index++;if(!e||i>=e.length)return t.target=null,c(void 0,!0);switch(t.kind){case"keys":return c(i,!1);case"values":return c(e[i],!1)}return c([i,e[i]],!1)},"values");var _=a.Arguments=a.Array;if(r("keys"),r("values"),r("entries"),!u&&h&&"values"!==_.name)try{o(_,"name",{value:"values"})}catch(t){}},79876:function(t,e,i){"use strict";var n=i(96122),r=i(87620),a=i(8785),s=i(92766),o=i(98196);n({target:"Array",proto:!0,arity:1,forced:i(81124)(function(){return 0x100000001!==[].push.call({length:0x100000000},1)})||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=r(this),i=a(e),n=arguments.length;o(i+n);for(var l=0;l<n;l++)e[i]=arguments[l],i++;return s(e,i),i}})},93008:function(t,e,i){"use strict";var n=i(96122),r=i(31660).left,a=i(51530),s=i(52974);n({target:"Array",proto:!0,forced:!i(83639)&&s>79&&s<83||!a("reduce")},{reduce:function(t){var e=arguments.length;return r(this,t,e,e>1?arguments[1]:void 0)}})},56711:function(t,e,i){"use strict";i(63133)("flat")},838:function(t,e,i){"use strict";var n=i(96122),r=i(87620),a=i(8785),s=i(92766),o=i(68028),l=i(98196);n({target:"Array",proto:!0,arity:1,forced:1!==[].unshift(0)||!function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(t){return t instanceof TypeError}}()},{unshift:function(t){var e=r(this),i=a(e),n=arguments.length;if(n){l(i+n);for(var c=i;c--;){var u=c+n;c in e?e[u]=e[c]:o(e,u)}for(var h=0;h<n;h++)e[h]=arguments[h]}return s(e,i+n)}})},92037:function(t,e,i){"use strict";var n=i(96122),r=i(42623),a=i(63451),s=i(85863),o="WebAssembly",l=r[o],c=7!==Error("e",{cause:7}).cause,u=function(t,e){var i={};i[t]=s(t,e,c),n({global:!0,constructor:!0,arity:1,forced:c},i)},h=function(t,e){if(l&&l[t]){var i={};i[t]=s(o+"."+t,e,c),n({target:o,stat:!0,constructor:!0,arity:1,forced:c},i)}};u("Error",function(t){return function(e){return a(t,this,arguments)}}),u("EvalError",function(t){return function(e){return a(t,this,arguments)}}),u("RangeError",function(t){return function(e){return a(t,this,arguments)}}),u("ReferenceError",function(t){return function(e){return a(t,this,arguments)}}),u("SyntaxError",function(t){return function(e){return a(t,this,arguments)}}),u("TypeError",function(t){return function(e){return a(t,this,arguments)}}),u("URIError",function(t){return function(e){return a(t,this,arguments)}}),h("CompileError",function(t){return function(e){return a(t,this,arguments)}}),h("LinkError",function(t){return function(e){return a(t,this,arguments)}}),h("RuntimeError",function(t){return function(e){return a(t,this,arguments)}})},13044:function(t,e,i){"use strict";var n=i(96122),r=i(59528),a=i(94112),s=i(41267),o=i(40857),l=i(95342);n({target:"Promise",stat:!0,forced:i(89633)},{all:function(t){var e=this,i=s.f(e),n=i.resolve,c=i.reject,u=o(function(){var i=a(e.resolve),s=[],o=0,u=1;l(t,function(t){var a=o++,l=!1;u++,r(i,e,t).then(function(t){!l&&(l=!0,s[a]=t,--u||n(s))},c)}),--u||n(s)});return u.error&&c(u.value),i.promise}})},42718:function(t,e,i){"use strict";var n=i(96122),r=i(52512),a=i(6585).CONSTRUCTOR,s=i(46267),o=i(93345),l=i(28917),c=i(51200),u=s&&s.prototype;if(n({target:"Promise",proto:!0,forced:a,real:!0},{catch:function(t){return this.then(void 0,t)}}),!r&&l(s)){var h=o("Promise").prototype.catch;u.catch!==h&&c(u,"catch",h,{unsafe:!0})}},61443:function(t,e,i){"use strict";var n,r,a,s,o=i(96122),l=i(52512),c=i(83639),u=i(42623),h=i(59528),d=i(51200),p=i(6887),f=i(83244),_=i(80974),v=i(94112),m=i(28917),g=i(10136),y=i(14644),w=i(7693),b=i(54469).set,S=i(64635),k=i(66206),A=i(40857),x=i(95133),O=i(67875),U=i(46267),E=i(6585),P=i(41267),C="Promise",I=E.CONSTRUCTOR,T=E.REJECTION_EVENT,R=E.SUBCLASSING,M=O.getterFor(C),N=O.set,z=U&&U.prototype,D=U,L=z,W=u.TypeError,F=u.document,j=u.process,V=P.f,K=V,B=!!(F&&F.createEvent&&u.dispatchEvent),$="unhandledrejection",J=function(t){var e;return!!(g(t)&&m(e=t.then))&&e},q=function(t,e){var i,n,r,a=e.value,s=1===e.state,o=s?t.ok:t.fail,l=t.resolve,c=t.reject,u=t.domain;try{o?(s||(2===e.rejection&&X(e),e.rejection=1),!0===o?i=a:(u&&u.enter(),i=o(a),u&&(u.exit(),r=!0)),i===t.promise?c(new W("Promise-chain cycle")):(n=J(i))?h(n,i,l,c):l(i)):c(a)}catch(t){u&&!r&&u.exit(),c(t)}},G=function(t,e){t.notified||(t.notified=!0,S(function(){for(var i,n=t.reactions;i=n.get();)q(i,t);t.notified=!1,e&&!t.rejection&&Z(t)}))},H=function(t,e,i){var n,r;B?((n=F.createEvent("Event")).promise=e,n.reason=i,n.initEvent(t,!1,!0),u.dispatchEvent(n)):n={promise:e,reason:i},!T&&(r=u["on"+t])?r(n):t===$&&k("Unhandled promise rejection",i)},Z=function(t){h(b,u,function(){var e,i=t.facade,n=t.value;if(Y(t)&&(e=A(function(){c?j.emit("unhandledRejection",n,i):H($,i,n)}),t.rejection=c||Y(t)?2:1,e.error))throw e.value})},Y=function(t){return 1!==t.rejection&&!t.parent},X=function(t){h(b,u,function(){var e=t.facade;c?j.emit("rejectionHandled",e):H("rejectionhandled",e,t.value)})},Q=function(t,e,i){return function(n){t(e,n,i)}},tt=function(t,e,i){t.done||(t.done=!0,i&&(t=i),t.value=e,t.state=2,G(t,!0))},te=function(t,e,i){if(!t.done){t.done=!0,i&&(t=i);try{if(t.facade===e)throw new W("Promise can't be resolved itself");var n=J(e);n?S(function(){var i={done:!1};try{h(n,e,Q(te,i,t),Q(tt,i,t))}catch(e){tt(i,e,t)}}):(t.value=e,t.state=1,G(t,!1))}catch(e){tt({done:!1},e,t)}}};if(I&&(L=(D=function(t){y(this,L),v(t),h(n,this);var e=M(this);try{t(Q(te,e),Q(tt,e))}catch(t){tt(e,t)}}).prototype,(n=function(t){N(this,{type:C,done:!1,notified:!1,parent:!1,reactions:new x,rejection:!1,state:0,value:null})}).prototype=d(L,"then",function(t,e){var i=M(this),n=V(w(this,D));return i.parent=!0,n.ok=!m(t)||t,n.fail=m(e)&&e,n.domain=c?j.domain:void 0,0===i.state?i.reactions.add(n):S(function(){q(n,i)}),n.promise}),r=function(){var t=new n,e=M(t);this.promise=t,this.resolve=Q(te,e),this.reject=Q(tt,e)},P.f=V=function(t){return t===D||t===a?new r(t):K(t)},!l&&m(U)&&z!==Object.prototype)){s=z.then,R||d(z,"then",function(t,e){var i=this;return new D(function(t,e){h(s,i,t,e)}).then(t,e)},{unsafe:!0});try{delete z.constructor}catch(t){}p&&p(z,L)}o({global:!0,constructor:!0,wrap:!0,forced:I},{Promise:D}),f(D,C,!1,!0),_(C)},38062:function(t,e,i){"use strict";var n=i(96122),r=i(52512),a=i(46267),s=i(81124),o=i(93345),l=i(28917),c=i(7693),u=i(11785),h=i(51200),d=a&&a.prototype;if(n({target:"Promise",proto:!0,real:!0,forced:!!a&&s(function(){d.finally.call({then:function(){}},function(){})})},{finally:function(t){var e=c(this,o("Promise")),i=l(t);return this.then(i?function(i){return u(e,t()).then(function(){return i})}:t,i?function(i){return u(e,t()).then(function(){throw i})}:t)}}),!r&&l(a)){var p=o("Promise").prototype.finally;d.finally!==p&&h(d,"finally",p,{unsafe:!0})}},50725:function(t,e,i){"use strict";i(61443),i(13044),i(42718),i(84127),i(22542),i(89750)},84127:function(t,e,i){"use strict";var n=i(96122),r=i(59528),a=i(94112),s=i(41267),o=i(40857),l=i(95342);n({target:"Promise",stat:!0,forced:i(89633)},{race:function(t){var e=this,i=s.f(e),n=i.reject,c=o(function(){var s=a(e.resolve);l(t,function(t){r(s,e,t).then(i.resolve,n)})});return c.error&&n(c.value),i.promise}})},22542:function(t,e,i){"use strict";var n=i(96122),r=i(41267);n({target:"Promise",stat:!0,forced:i(6585).CONSTRUCTOR},{reject:function(t){var e=r.f(this);return(0,e.reject)(t),e.promise}})},89750:function(t,e,i){"use strict";var n=i(96122),r=i(93345),a=i(52512),s=i(46267),o=i(6585).CONSTRUCTOR,l=i(11785),c=r("Promise"),u=a&&!o;n({target:"Promise",stat:!0,forced:a||o},{resolve:function(t){return l(u&&this===c?s:this,t)}})},80194:function(t,e,i){"use strict";var n=i(97223),r=i(42623),a=i(26004),s=i(2849),o=i(31858),l=i(52801),c=i(38270),u=i(84315).f,h=i(84776),d=i(45903),p=i(86596),f=i(61358),_=i(96739),v=i(86876),m=i(51200),g=i(81124),y=i(29027),w=i(67875).enforce,b=i(80974),S=i(21755),k=i(8297),A=i(25129),x=S("match"),O=r.RegExp,U=O.prototype,E=r.SyntaxError,P=a(U.exec),C=a("".charAt),I=a("".replace),T=a("".indexOf),R=a("".slice),M=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,N=/a/g,z=/a/g,D=new O(N)!==N,L=_.MISSED_STICKY,W=_.UNSUPPORTED_Y,F=n&&(!D||L||k||A||g(function(){return z[x]=!1,O(N)!==N||O(z)===z||"/a/i"!==String(O(N,"i"))})),j=function(t){for(var e,i=t.length,n=0,r="",a=!1;n<=i;n++){if("\\"===(e=C(t,n))){r+=e+C(t,++n);continue}a||"."!==e?("["===e?a=!0:"]"===e&&(a=!1),r+=e):r+="[\\s\\S]"}return r},V=function(t){for(var e,i=t.length,n=0,r="",a=[],s=c(null),o=!1,l=!1,u=0,h="";n<=i;n++){if("\\"===(e=C(t,n)))e+=C(t,++n);else if("]"===e)o=!1;else if(!o)switch(!0){case"["===e:o=!0;break;case"("===e:if(r+=e,"?:"===R(t,n+1,n+3))continue;P(M,R(t,n+1))&&(n+=2,l=!0),u++;continue;case">"===e&&l:if(""===h||y(s,h))throw new E("Invalid capture group name");s[h]=!0,a[a.length]=[h,u],l=!1,h="";continue}l?h+=e:r+=e}return[r,a]};if(s("RegExp",F)){for(var K=function(t,e){var i,n,r,a,s,c,u=h(U,this),_=d(t),v=void 0===e,m=[],g=t;if(!u&&_&&v&&t.constructor===K)return t;if((_||h(U,t))&&(t=t.source,v&&(e=f(g))),t=void 0===t?"":p(t),e=void 0===e?"":p(e),g=t,k&&"dotAll"in N&&(n=!!e&&T(e,"s")>-1)&&(e=I(e,/s/g,"")),i=e,L&&"sticky"in N&&(r=!!e&&T(e,"y")>-1)&&W&&(e=I(e,/y/g,"")),A&&(t=(a=V(t))[0],m=a[1]),s=o(O(t,e),u?this:U,K),(n||r||m.length)&&(c=w(s),n&&(c.dotAll=!0,c.raw=K(j(t),i)),r&&(c.sticky=!0),m.length&&(c.groups=m)),t!==g)try{l(s,"source",""===g?"(?:)":g)}catch(t){}return s},B=u(O),$=0;B.length>$;)v(K,O,B[$++]);U.constructor=K,K.prototype=U,m(r,"RegExp",K,{constructor:!0})}b("RegExp")},25800:function(t,e,i){"use strict";var n=i(97223),r=i(8297),a=i(89255),s=i(22700),o=i(67875).get,l=RegExp.prototype,c=TypeError;n&&r&&s(l,"dotAll",{configurable:!0,get:function(){if(this!==l){if("RegExp"===a(this))return!!o(this).dotAll;throw new c("Incompatible receiver, RegExp required")}}})},56113:function(t,e,i){"use strict";var n=i(96122),r=i(64191);n({target:"RegExp",proto:!0,forced:/./.exec!==r},{exec:r})},50887:function(t,e,i){"use strict";var n=i(42623),r=i(97223),a=i(22700),s=i(17219),o=i(81124),l=n.RegExp,c=l.prototype;r&&o(function(){var t=!0;try{l(".","d")}catch(e){t=!1}var e={},i="",n=t?"dgimsy":"gimsy",r=function(t,n){Object.defineProperty(e,t,{get:function(){return i+=n,!0}})},a={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var s in t&&(a.hasIndices="d"),a)r(s,a[s]);return Object.getOwnPropertyDescriptor(c,"flags").get.call(e)!==n||i!==n})&&a(c,"flags",{configurable:!0,get:s})},13189:function(t,e,i){"use strict";var n=i(96122),r=i(76794);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("difference",function(t){return 0===t.size})},{difference:r})},63277:function(t,e,i){"use strict";var n=i(96122),r=i(81124),a=i(84629);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("intersection",function(t){return 2===t.size&&t.has(1)&&t.has(2)})||r(function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))})},{intersection:a})},82230:function(t,e,i){"use strict";var n=i(96122),r=i(27775);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("isDisjointFrom",function(t){return!t})},{isDisjointFrom:r})},27899:function(t,e,i){"use strict";var n=i(96122),r=i(82137);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("isSubsetOf",function(t){return t})},{isSubsetOf:r})},37389:function(t,e,i){"use strict";var n=i(96122),r=i(19947);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("isSupersetOf",function(t){return!t})},{isSupersetOf:r})},27968:function(t,e,i){"use strict";var n=i(96122),r=i(73854);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("symmetricDifference")},{symmetricDifference:r})},46299:function(t,e,i){"use strict";var n=i(96122),r=i(73406);n({target:"Set",proto:!0,real:!0,forced:!i(90785)("union")},{union:r})},70727:function(t,e,i){"use strict";var n=i(96122),r=i(26004),a=i(35745),s=RangeError,o=String.fromCharCode,l=String.fromCodePoint,c=r([].join);n({target:"String",stat:!0,arity:1,forced:!!l&&1!==l.length},{fromCodePoint:function(t){for(var e,i=[],n=arguments.length,r=0;n>r;){if(e=+arguments[r++],a(e,1114111)!==e)throw new s(e+" is not a valid code point");i[r]=e<65536?o(e):o(((e-=65536)>>10)+55296,e%1024+56320)}return c(i,"")}})},21751:function(t,e,i){"use strict";var n=i(55321).charAt,r=i(86596),a=i(67875),s=i(17591),o=i(77528),l="String Iterator",c=a.set,u=a.getterFor(l);s(String,"String",function(t){c(this,{type:l,string:r(t),index:0})},function(){var t,e=u(this),i=e.string,r=e.index;return r>=i.length?o(void 0,!0):(t=n(i,r),e.index+=t.length,o(t,!1))})},99881:function(t,e,i){"use strict";var n=i(96122),r=i(59528),a=i(77434),s=i(48256),o=i(77528),l=i(65977),c=i(11934),u=i(86596),h=i(98903),d=i(23671),p=i(89255),f=i(45903),_=i(61358),v=i(84867),m=i(51200),g=i(81124),y=i(21755),w=i(7693),b=i(44812),S=i(32955),k=i(67875),A=i(52512),x=y("matchAll"),O="RegExp String",U=O+" Iterator",E=k.set,P=k.getterFor(U),C=RegExp.prototype,I=TypeError,T=a("".indexOf),R=a("".matchAll),M=!!R&&!g(function(){R("a",/./)}),N=s(function(t,e,i,n){E(this,{type:U,regexp:t,string:e,global:i,unicode:n,done:!1})},O,function(){var t=P(this);if(t.done)return o(void 0,!0);var e=t.regexp,i=t.string,n=S(e,i);return null===n?(t.done=!0,o(void 0,!0)):(t.global?""===u(n[0])&&(e.lastIndex=b(i,c(e.lastIndex),t.unicode)):t.done=!0,o(n,!1))}),z=function(t){var e,i,n,r=h(this),a=u(t),s=w(r,RegExp),o=u(_(r));return e=new s(s===RegExp?r.source:r,o),i=!!~T(o,"g"),n=!!~T(o,"u"),e.lastIndex=c(r.lastIndex),new N(e,a,i,n)};n({target:"String",proto:!0,forced:M},{matchAll:function(t){var e,i,n,a=l(this);if(d(t)){if(M)return R(a,t)}else{if(f(t)&&!~T(u(l(_(t))),"g"))throw new I("`.matchAll` does not allow non-global regexes");if(M)return R(a,t);if(void 0===(i=v(t,x))&&A&&"RegExp"===p(t)&&(i=z),i)return r(i,t,a)}return e=u(a),n=RegExp(t,"g"),A?r(z,n,e):n[x](e)}}),A||x in C||m(C,x,z)},87945:function(t,e,i){"use strict";var n=i(63451),r=i(59528),a=i(26004),s=i(41395),o=i(81124),l=i(98903),c=i(28917),u=i(23671),h=i(17940),d=i(11934),p=i(86596),f=i(65977),_=i(44812),v=i(84867),m=i(38710),g=i(32955),y=i(21755)("replace"),w=Math.max,b=Math.min,S=a([].concat),k=a([].push),A=a("".indexOf),x=a("".slice),O="$0"==="a".replace(/./,"$0"),U=!!/./[y]&&""===/./[y]("a","$0");s("replace",function(t,e,i){var a=U?"$":"$0";return[function(t,i){var n=f(this),a=u(t)?void 0:v(t,y);return a?r(a,t,n,i):r(e,p(n),t,i)},function(t,r){var s=l(this),o=p(t);if("string"==typeof r&&-1===A(r,a)&&-1===A(r,"$<")){var u=i(e,s,o,r);if(u.done)return u.value}var f=c(r);f||(r=p(r));var v=s.global;v&&(C=s.unicode,s.lastIndex=0);for(var y=[];null!==(I=g(s,o))&&(k(y,I),v);){""===p(I[0])&&(s.lastIndex=_(o,d(s.lastIndex),C))}for(var O="",U=0,E=0;E<y.length;E++){for(var P,C,I,T,R=p((I=y[E])[0]),M=w(b(h(I.index),o.length),0),N=[],z=1;z<I.length;z++)k(N,void 0===(P=I[z])?P:String(P));var D=I.groups;if(f){var L=S([R],N,M,o);void 0!==D&&k(L,D),T=p(n(r,void 0,L))}else T=m(R,o,M,N,D,r);M>=U&&(O+=x(o,U,M)+T,U=M+R.length)}return O+x(o,U)}]},!!o(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})||!O||U)},75184:function(t,e,i){"use strict";var n=i(44708),r=i(8785),a=i(17940),s=n.aTypedArray;(0,n.exportTypedArrayMethod)("at",function(t){var e=s(this),i=r(e),n=a(t),o=n>=0?n:i+n;return o<0||o>=i?void 0:e[o]})},12100:function(t,e,i){"use strict";var n=i(44708),r=i(49546),a=i(43355),s=i(70422),o=i(59528),l=i(26004),c=i(81124),u=n.aTypedArray,h=n.exportTypedArrayMethod,d=l("".slice);h("fill",function(t){var e=arguments.length;return u(this),o(r,this,"Big"===d(s(this),0,3)?a(t):+t,e>1?arguments[1]:void 0,e>2?arguments[2]:void 0)},c(function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t}))},67581:function(t,e,i){"use strict";var n=i(44708),r=i(74620).findLastIndex,a=n.aTypedArray;(0,n.exportTypedArrayMethod)("findLastIndex",function(t){return r(a(this),t,arguments.length>1?arguments[1]:void 0)})},29646:function(t,e,i){"use strict";var n=i(44708),r=i(74620).findLast,a=n.aTypedArray;(0,n.exportTypedArrayMethod)("findLast",function(t){return r(a(this),t,arguments.length>1?arguments[1]:void 0)})},63782:function(t,e,i){"use strict";var n=i(42623),r=i(59528),a=i(44708),s=i(8785),o=i(67432),l=i(87620),c=i(81124),u=n.RangeError,h=n.Int8Array,d=h&&h.prototype,p=d&&d.set,f=a.aTypedArray,_=a.exportTypedArrayMethod,v=!c(function(){var t=new Uint8ClampedArray(2);return r(p,t,{length:1,0:3},1),3!==t[1]}),m=v&&a.NATIVE_ARRAY_BUFFER_VIEWS&&c(function(){var t=new h(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]});_("set",function(t){f(this);var e=o(arguments.length>1?arguments[1]:void 0,1),i=l(t);if(v)return r(p,this,i,e);var n=this.length,a=s(i),c=0;if(a+e>n)throw new u("Wrong length");for(;c<a;)this[e+c]=i[c++]},!v||m)},14311:function(t,e,i){"use strict";var n=i(42623),r=i(77434),a=i(81124),s=i(94112),o=i(27370),l=i(44708),c=i(70453),u=i(89399),h=i(52974),d=i(88440),p=l.aTypedArray,f=l.exportTypedArrayMethod,_=n.Uint16Array,v=_&&r(_.prototype.sort),m=!!v&&!(a(function(){v(new _(2),null)})&&a(function(){v(new _(2),{})})),g=!!v&&!a(function(){if(h)return h<74;if(c)return c<67;if(u)return!0;if(d)return d<602;var t,e,i=new _(516),n=Array(516);for(t=0;t<516;t++)e=t%4,i[t]=515-t,n[t]=t-2*e+3;for(v(i,function(t,e){return(t/4|0)-(e/4|0)}),t=0;t<516;t++)if(i[t]!==n[t])return!0});f("sort",function(t){return(void 0!==t&&s(t),g)?v(this,t):o(p(this),function(e,i){return void 0!==t?+t(e,i)||0:i!=i?-1:e!=e?1:0===e&&0===i?1/e>0&&1/i<0?1:-1:e>i})},!g||m)},72547:function(t,e,i){"use strict";var n=i(58673),r=i(44708),a=r.aTypedArray,s=r.exportTypedArrayMethod,o=r.getTypedArrayConstructor;s("toReversed",function(){return n(a(this),o(this))})},86936:function(t,e,i){"use strict";var n=i(44708),r=i(26004),a=i(94112),s=i(97338),o=n.aTypedArray,l=n.getTypedArrayConstructor,c=n.exportTypedArrayMethod,u=r(n.TypedArrayPrototype.sort);c("toSorted",function(t){void 0!==t&&a(t);var e=o(this);return u(s(l(e),e),t)})},73588:function(t,e,i){"use strict";i(48095)("Uint8",function(t){return function(e,i,n){return t(this,e,i,n)}})},20889:function(t,e,i){"use strict";var n=i(4584),r=i(44708),a=i(69488),s=i(17940),o=i(43355),l=r.aTypedArray,c=r.getTypedArrayConstructor;(0,r.exportTypedArrayMethod)("with",{with:function(t,e){var i=l(this),r=s(t),u=a(i)?o(e):+e;return n(i,c(i),r,u)}}.with,!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}())},65223:function(t,e,i){"use strict";var n=i(42623),r=i(47577),a=i(13840),s=i(95467),o=i(52801),l=i(83244),c=i(21755)("iterator"),u=s.values,h=function(t,e){if(t){if(t[c]!==u)try{o(t,c,u)}catch(e){t[c]=u}if(l(t,e,!0),r[e]){for(var i in s)if(t[i]!==s[i])try{o(t,i,s[i])}catch(e){t[i]=s[i]}}}};for(var d in r)h(n[d]&&n[d].prototype,d);h(a,"DOMTokenList")},50886:function(t,e,i){"use strict";var n=i(96122),r=i(42623),a=i(93345),s=i(78407),o=i(79406).f,l=i(29027),c=i(14644),u=i(31858),h=i(25441),d=i(8808),p=i(90742),f=i(97223),_=i(52512),v="DOMException",m=a("Error"),g=a(v),y=function(){c(this,w);var t=arguments.length,e=h(t<1?void 0:arguments[0]),i=h(t<2?void 0:arguments[1],"Error"),n=new g(e,i),r=new m(e);return r.name=v,o(n,"stack",s(1,p(r.stack,1))),u(n,this,y),n},w=y.prototype=g.prototype,b="stack"in new m(v),S="stack"in new g(1,2),k=g&&f&&Object.getOwnPropertyDescriptor(r,v),A=!!k&&!(k.writable&&k.configurable),x=b&&!A&&!S;n({global:!0,constructor:!0,forced:_||x},{DOMException:x?y:g});var O=a(v),U=O.prototype;if(U.constructor!==O){for(var E in _||o(U,"constructor",s(1,O)),d)if(l(d,E)){var P=d[E],C=P.s;l(O,C)||o(O,C,s(6,P.c))}}},64728:function(t,e,i){"use strict";var n=i(96122),r=i(42623),a=i(22700),s=i(97223),o=TypeError,l=Object.defineProperty,c=r.self!==r;try{if(s){var u=Object.getOwnPropertyDescriptor(r,"self");!c&&u&&u.get&&u.enumerable||a(r,"self",{get:function(){return r},set:function(t){if(this!==r)throw new o("Illegal invocation");l(r,"self",{value:t,writable:!0,configurable:!0,enumerable:!0})},configurable:!0,enumerable:!0})}else n({global:!0,simple:!0,forced:c},{self:r})}catch(t){}},79093:function(t,e,i){"use strict";i(95467),i(70727);var n=i(96122),r=i(42623),a=i(22054),s=i(93345),o=i(59528),l=i(26004),c=i(97223),u=i(19484),h=i(51200),d=i(22700),p=i(682),f=i(83244),_=i(48256),v=i(67875),m=i(14644),g=i(28917),y=i(29027),w=i(1480),b=i(70422),S=i(98903),k=i(10136),A=i(86596),x=i(38270),O=i(78407),U=i(85773),E=i(2691),P=i(77528),C=i(72888),I=i(21755),T=i(27370),R=I("iterator"),M="URLSearchParams",N=M+"Iterator",z=v.set,D=v.getterFor(M),L=v.getterFor(N),W=a("fetch"),F=a("Request"),j=a("Headers"),V=F&&F.prototype,K=j&&j.prototype,B=r.TypeError,$=r.encodeURIComponent,J=String.fromCharCode,q=s("String","fromCodePoint"),G=parseInt,H=l("".charAt),Z=l([].join),Y=l([].push),X=l("".replace),Q=l([].shift),tt=l([].splice),te=l("".split),ti=l("".slice),tn=l(/./.exec),tr=/\+/g,ta=/^[0-9a-f]+$/i,ts=function(t,e){var i=ti(t,e,e+2);return tn(ta,i)?G(i,16):NaN},to=function(t){for(var e=0,i=128;i>0&&(t&i)!=0;i>>=1)e++;return e},tl=function(t){var e=null;switch(t.length){case 1:e=t[0];break;case 2:e=(31&t[0])<<6|63&t[1];break;case 3:e=(15&t[0])<<12|(63&t[1])<<6|63&t[2];break;case 4:e=(7&t[0])<<18|(63&t[1])<<12|(63&t[2])<<6|63&t[3]}return e>1114111?null:e},tc=function(t){for(var e=(t=X(t,tr," ")).length,i="",n=0;n<e;){var r=H(t,n);if("%"===r){if("%"===H(t,n+1)||n+3>e){i+="%",n++;continue}var a=ts(t,n+1);if(a!=a){i+=r,n++;continue}n+=2;var s=to(a);if(0===s)r=J(a);else{if(1===s||s>4){i+="�",n++;continue}for(var o=[a],l=1;l<s&&!(++n+3>e)&&"%"===H(t,n);){var c=ts(t,n+1);if(c!=c){n+=3;break}if(c>191||c<128)break;Y(o,c),n+=2,l++}if(o.length!==s){i+="�";continue}var u=tl(o);null===u?i+="�":r=q(u)}}i+=r,n++}return i},tu=/[!'()~]|%20/g,th={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},td=function(t){return th[t]},tp=function(t){return X($(t),tu,td)},tf=_(function(t,e){z(this,{type:N,target:D(t).entries,index:0,kind:e})},M,function(){var t=L(this),e=t.target,i=t.index++;if(!e||i>=e.length)return t.target=null,P(void 0,!0);var n=e[i];switch(t.kind){case"keys":return P(n.key,!1);case"values":return P(n.value,!1)}return P([n.key,n.value],!1)},!0),t_=function(t){this.entries=[],this.url=null,void 0!==t&&(k(t)?this.parseObject(t):this.parseQuery("string"==typeof t?"?"===H(t,0)?ti(t,1):t:A(t)))};t_.prototype={type:M,bindURL:function(t){this.url=t,this.update()},parseObject:function(t){var e,i,n,r,a,s,l,c=this.entries,u=E(t);if(u)for(i=(e=U(t,u)).next;!(n=o(i,e)).done;){if((s=o(a=(r=U(S(n.value))).next,r)).done||(l=o(a,r)).done||!o(a,r).done)throw new B("Expected sequence with length 2");Y(c,{key:A(s.value),value:A(l.value)})}else for(var h in t)y(t,h)&&Y(c,{key:h,value:A(t[h])})},parseQuery:function(t){if(t)for(var e,i,n=this.entries,r=te(t,"&"),a=0;a<r.length;)(e=r[a++]).length&&Y(n,{key:tc(Q(i=te(e,"="))),value:tc(Z(i,"="))})},serialize:function(){for(var t,e=this.entries,i=[],n=0;n<e.length;)Y(i,tp((t=e[n++]).key)+"="+tp(t.value));return Z(i,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var tv=function(){m(this,tm);var t=arguments.length>0?arguments[0]:void 0,e=z(this,new t_(t));c||(this.size=e.entries.length)},tm=tv.prototype;if(p(tm,{append:function(t,e){var i=D(this);C(arguments.length,2),Y(i.entries,{key:A(t),value:A(e)}),!c&&this.length++,i.updateURL()},delete:function(t){for(var e=D(this),i=C(arguments.length,1),n=e.entries,r=A(t),a=i<2?void 0:arguments[1],s=void 0===a?a:A(a),o=0;o<n.length;){var l=n[o];if(l.key===r&&(void 0===s||l.value===s)){if(tt(n,o,1),void 0!==s)break}else o++}c||(this.size=n.length),e.updateURL()},get:function(t){var e=D(this).entries;C(arguments.length,1);for(var i=A(t),n=0;n<e.length;n++)if(e[n].key===i)return e[n].value;return null},getAll:function(t){var e=D(this).entries;C(arguments.length,1);for(var i=A(t),n=[],r=0;r<e.length;r++)e[r].key===i&&Y(n,e[r].value);return n},has:function(t){for(var e=D(this).entries,i=C(arguments.length,1),n=A(t),r=i<2?void 0:arguments[1],a=void 0===r?r:A(r),s=0;s<e.length;){var o=e[s++];if(o.key===n&&(void 0===a||o.value===a))return!0}return!1},set:function(t,e){var i,n=D(this);C(arguments.length,1);for(var r=n.entries,a=!1,s=A(t),o=A(e),l=0;l<r.length;l++)(i=r[l]).key===s&&(a?tt(r,l--,1):(a=!0,i.value=o));a||Y(r,{key:s,value:o}),c||(this.size=r.length),n.updateURL()},sort:function(){var t=D(this);T(t.entries,function(t,e){return t.key>e.key?1:-1}),t.updateURL()},forEach:function(t){for(var e,i=D(this).entries,n=w(t,arguments.length>1?arguments[1]:void 0),r=0;r<i.length;)n((e=i[r++]).value,e.key,this)},keys:function(){return new tf(this,"keys")},values:function(){return new tf(this,"values")},entries:function(){return new tf(this,"entries")}},{enumerable:!0}),h(tm,R,tm.entries,{name:"entries"}),h(tm,"toString",function(){return D(this).serialize()},{enumerable:!0}),c&&d(tm,"size",{get:function(){return D(this).entries.length},configurable:!0,enumerable:!0}),f(tv,M),n({global:!0,constructor:!0,forced:!u},{URLSearchParams:tv}),!u&&g(j)){var tg=l(K.has),ty=l(K.set),tw=function(t){if(k(t)){var e,i=t.body;if(b(i)===M)return tg(e=t.headers?new j(t.headers):new j,"content-type")||ty(e,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),x(t,{body:O(0,A(i)),headers:O(0,e)})}return t};if(g(W)&&n({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(t){return W(t,arguments.length>1?tw(arguments[1]):{})}}),g(F)){var tb=function(t){return m(this,V),new F(t,arguments.length>1?tw(arguments[1]):{})};V.constructor=tb,tb.prototype=V,n({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:tb})}}t.exports={URLSearchParams:tv,getState:D}},70957:function(t,e,i){"use strict";var n=i(51200),r=i(26004),a=i(86596),s=i(72888),o=URLSearchParams,l=o.prototype,c=r(l.append),u=r(l.delete),h=r(l.forEach),d=r([].push),p=new o("a=1&a=2&b=3");p.delete("a",1),p.delete("b",void 0),p+""!="a=2"&&n(l,"delete",function(t){var e,i=arguments.length,n=i<2?void 0:arguments[1];if(i&&void 0===n)return u(this,t);var r=[];h(this,function(t,e){d(r,{key:e,value:t})}),s(i,1);for(var o=a(t),l=a(n),p=0,f=0,_=!1,v=r.length;p<v;)e=r[p++],_||e.key===o?(_=!0,u(this,e.key)):f++;for(;f<v;)((e=r[f++]).key!==o||e.value!==l)&&c(this,e.key,e.value)},{enumerable:!0,unsafe:!0})},24551:function(t,e,i){"use strict";var n=i(51200),r=i(26004),a=i(86596),s=i(72888),o=URLSearchParams,l=o.prototype,c=r(l.getAll),u=r(l.has),h=new o("a=1");(h.has("a",2)||!h.has("a",void 0))&&n(l,"has",function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return u(this,t);var n=c(this,t);s(e,1);for(var r=a(i),o=0;o<n.length;)if(n[o++]===r)return!0;return!1},{enumerable:!0,unsafe:!0})},45261:function(t,e,i){"use strict";i(79093)},22349:function(t,e,i){"use strict";var n=i(97223),r=i(26004),a=i(22700),s=URLSearchParams.prototype,o=r(s.forEach);!n||"size"in s||a(s,"size",{get:function(){var t=0;return o(this,function(){t++}),t},configurable:!0,enumerable:!0})},92530:function(t,e,i){"use strict";i(21751);var n,r=i(96122),a=i(97223),s=i(19484),o=i(42623),l=i(1480),c=i(26004),u=i(51200),h=i(22700),d=i(14644),p=i(29027),f=i(37135),_=i(41039),v=i(88221),m=i(55321).codeAt,g=i(65660),y=i(86596),w=i(83244),b=i(72888),S=i(79093),k=i(67875),A=k.set,x=k.getterFor("URL"),O=S.URLSearchParams,U=S.getState,E=o.URL,P=o.TypeError,C=o.parseInt,I=Math.floor,T=Math.pow,R=c("".charAt),M=c(/./.exec),N=c([].join),z=c(1..toString),D=c([].pop),L=c([].push),W=c("".replace),F=c([].shift),j=c("".split),V=c("".slice),K=c("".toLowerCase),B=c([].unshift),$="Invalid scheme",J="Invalid host",q="Invalid port",G=/[a-z]/i,H=/[\d+-.a-z]/i,Z=/\d/,Y=/^0x/i,X=/^[0-7]+$/,Q=/^\d+$/,tt=/^[\da-f]+$/i,te=/[\0\t\n\r #%/:<>?@[\\\]^|]/,ti=/[\0\t\n\r #/:<>?@[\\\]^|]/,tn=/^[\u0000-\u0020]+/,tr=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,ta=/[\t\n\r]/g,ts=function(t){var e,i,n,r,a,s,o,l=j(t,".");if(l.length&&""===l[l.length-1]&&l.length--,(e=l.length)>4)return t;for(n=0,i=[];n<e;n++){if(""===(r=l[n]))return t;if(a=10,r.length>1&&"0"===R(r,0)&&(a=M(Y,r)?16:8,r=V(r,8===a?1:2)),""===r)s=0;else{if(!M(10===a?Q:8===a?X:tt,r))return t;s=C(r,a)}L(i,s)}for(n=0;n<e;n++)if(s=i[n],n===e-1){if(s>=T(256,5-e))return null}else if(s>255)return null;for(n=0,o=D(i);n<i.length;n++)o+=i[n]*T(256,3-n);return o},to=function(t){var e,i,n,r,a,s,o,l=[0,0,0,0,0,0,0,0],c=0,u=null,h=0,d=function(){return R(t,h)};if(":"===d()){if(":"!==R(t,1))return;h+=2,u=++c}for(;d();){if(8===c)return;if(":"===d()){if(null!==u)return;h++,u=++c;continue}for(e=i=0;i<4&&M(tt,d());)e=16*e+C(d(),16),h++,i++;if("."===d()){if(0===i||(h-=i,c>6))return;for(n=0;d();){if(r=null,n>0){if("."!==d()||!(n<4))return;h++}if(!M(Z,d()))return;for(;M(Z,d());){if(a=C(d(),10),null===r)r=a;else{if(0===r)return;r=10*r+a}if(r>255)return;h++}l[c]=256*l[c]+r,(2==++n||4===n)&&c++}if(4!==n)return;break}if(":"===d()){if(h++,!d())return}else if(d())return;l[c++]=e}if(null!==u)for(s=c-u,c=7;0!==c&&s>0;)o=l[c],l[c--]=l[u+s-1],l[u+--s]=o;else if(8!==c)return;return l},tl=function(t){for(var e=null,i=1,n=null,r=0,a=0;a<8;a++)0!==t[a]?(r>i&&(e=n,i=r),n=null,r=0):(null===n&&(n=a),++r);return r>i?n:e},tc=function(t){var e,i,n,r;if("number"==typeof t){for(i=0,e=[];i<4;i++)B(e,t%256),t=I(t/256);return N(e,".")}if("object"==typeof t){for(i=0,e="",n=tl(t);i<8;i++)(!r||0!==t[i])&&(r&&(r=!1),n===i?(e+=i?":":"::",r=!0):(e+=z(t[i],16),i<7&&(e+=":")));return"["+e+"]"}return t},tu={},th=f({},tu,{" ":1,'"':1,"<":1,">":1,"`":1}),td=f({},th,{"#":1,"?":1,"{":1,"}":1}),tp=f({},td,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),tf=function(t,e){var i=m(t,0);return i>32&&i<127&&!p(e,t)?t:encodeURIComponent(t)},t_={ftp:21,file:null,http:80,https:443,ws:80,wss:443},tv=function(t,e){var i;return 2===t.length&&M(G,R(t,0))&&(":"===(i=R(t,1))||!e&&"|"===i)},tm=function(t){var e;return t.length>1&&tv(V(t,0,2))&&(2===t.length||"/"===(e=R(t,2))||"\\"===e||"?"===e||"#"===e)},tg={},ty={},tw={},tb={},tS={},tk={},tA={},tx={},tO={},tU={},tE={},tP={},tC={},tI={},tT={},tR={},tM={},tN={},tz={},tD={},tL={},tW=function(t,e,i){var n,r,a,s=y(t);if(e){if(r=this.parse(s))throw new P(r);this.searchParams=null}else{if(void 0!==i&&(n=new tW(i,!0)),r=this.parse(s,null,n))throw new P(r);(a=U(new O)).bindURL(this),this.searchParams=a}};tW.prototype={type:"URL",parse:function(t,e,i){var r=e||tg,a=0,s="",o=!1,l=!1,c=!1;for(t=y(t),e||(this.scheme="",this.username="",this.password="",this.host=null,this.port=null,this.path=[],this.query=null,this.fragment=null,this.cannotBeABaseURL=!1,t=W(t,tn,""),t=W(t,tr,"$1")),u=_(t=W(t,ta,""));a<=u.length;){switch(h=u[a],r){case tg:if(h&&M(G,h))s+=K(h),r=ty;else{if(e)return $;r=tw;continue}break;case ty:if(h&&(M(H,h)||"+"===h||"-"===h||"."===h))s+=K(h);else if(":"===h){if(e&&(this.isSpecial()!==p(t_,s)||"file"===s&&(this.includesCredentials()||null!==this.port)||"file"===this.scheme&&!this.host))return;if(this.scheme=s,e){this.isSpecial()&&t_[this.scheme]===this.port&&(this.port=null);return}s="","file"===this.scheme?r=tI:this.isSpecial()&&i&&i.scheme===this.scheme?r=tb:this.isSpecial()?r=tx:"/"===u[a+1]?(r=tS,a++):(this.cannotBeABaseURL=!0,L(this.path,""),r=tz)}else{if(e)return $;s="",r=tw,a=0;continue}break;case tw:if(!i||i.cannotBeABaseURL&&"#"!==h)return $;if(i.cannotBeABaseURL&&"#"===h){this.scheme=i.scheme,this.path=v(i.path),this.query=i.query,this.fragment="",this.cannotBeABaseURL=!0,r=tL;break}r="file"===i.scheme?tI:tk;continue;case tb:if("/"===h&&"/"===u[a+1])r=tO,a++;else{r=tk;continue}break;case tS:if("/"===h){r=tU;break}r=tN;continue;case tk:if(this.scheme=i.scheme,h===n)this.username=i.username,this.password=i.password,this.host=i.host,this.port=i.port,this.path=v(i.path),this.query=i.query;else if("/"===h||"\\"===h&&this.isSpecial())r=tA;else if("?"===h)this.username=i.username,this.password=i.password,this.host=i.host,this.port=i.port,this.path=v(i.path),this.query="",r=tD;else if("#"===h)this.username=i.username,this.password=i.password,this.host=i.host,this.port=i.port,this.path=v(i.path),this.query=i.query,this.fragment="",r=tL;else{this.username=i.username,this.password=i.password,this.host=i.host,this.port=i.port,this.path=v(i.path),this.path.length--,r=tN;continue}break;case tA:if(this.isSpecial()&&("/"===h||"\\"===h))r=tO;else if("/"===h)r=tU;else{this.username=i.username,this.password=i.password,this.host=i.host,this.port=i.port,r=tN;continue}break;case tx:if(r=tO,"/"!==h||"/"!==R(s,a+1))continue;a++;break;case tO:if("/"!==h&&"\\"!==h){r=tU;continue}break;case tU:if("@"===h){o&&(s="%40"+s),o=!0,d=_(s);for(var u,h,d,f,m,g,w=0;w<d.length;w++){var b=d[w];if(":"===b&&!c){c=!0;continue}var S=tf(b,tp);c?this.password+=S:this.username+=S}s=""}else if(h===n||"/"===h||"?"===h||"#"===h||"\\"===h&&this.isSpecial()){if(o&&""===s)return"Invalid authority";a-=_(s).length+1,s="",r=tE}else s+=h;break;case tE:case tP:if(e&&"file"===this.scheme){r=tR;continue}if(":"!==h||l){if(h===n||"/"===h||"?"===h||"#"===h||"\\"===h&&this.isSpecial()){if(this.isSpecial()&&""===s)return J;if(e&&""===s&&(this.includesCredentials()||null!==this.port))return;if(f=this.parseHost(s))return f;if(s="",r=tM,e)return;continue}"["===h?l=!0:"]"===h&&(l=!1),s+=h}else{if(""===s)return J;if(f=this.parseHost(s))return f;if(s="",r=tC,e===tP)return}break;case tC:if(M(Z,h))s+=h;else{if(!(h===n||"/"===h||"?"===h||"#"===h||"\\"===h&&this.isSpecial())&&!e)return q;if(""!==s){var k=C(s,10);if(k>65535)return q;this.port=this.isSpecial()&&k===t_[this.scheme]?null:k,s=""}if(e)return;r=tM;continue}break;case tI:if(this.scheme="file","/"===h||"\\"===h)r=tT;else if(i&&"file"===i.scheme)switch(h){case n:this.host=i.host,this.path=v(i.path),this.query=i.query;break;case"?":this.host=i.host,this.path=v(i.path),this.query="",r=tD;break;case"#":this.host=i.host,this.path=v(i.path),this.query=i.query,this.fragment="",r=tL;break;default:tm(N(v(u,a),""))||(this.host=i.host,this.path=v(i.path),this.shortenPath()),r=tN;continue}else{r=tN;continue}break;case tT:if("/"===h||"\\"===h){r=tR;break}i&&"file"===i.scheme&&!tm(N(v(u,a),""))&&(tv(i.path[0],!0)?L(this.path,i.path[0]):this.host=i.host),r=tN;continue;case tR:if(h===n||"/"===h||"\\"===h||"?"===h||"#"===h){if(!e&&tv(s))r=tN;else if(""===s){if(this.host="",e)return;r=tM}else{if(f=this.parseHost(s))return f;if("localhost"===this.host&&(this.host=""),e)return;s="",r=tM}continue}s+=h;break;case tM:if(this.isSpecial()){if(r=tN,"/"!==h&&"\\"!==h)continue}else if(e||"?"!==h){if(e||"#"!==h){if(h!==n&&(r=tN,"/"!==h))continue}else this.fragment="",r=tL}else this.query="",r=tD;break;case tN:if(h===n||"/"===h||"\\"===h&&this.isSpecial()||!e&&("?"===h||"#"===h)){if(".."===(m=K(m=s))||"%2e."===m||".%2e"===m||"%2e%2e"===m?(this.shortenPath(),"/"===h||"\\"===h&&this.isSpecial()||L(this.path,"")):"."===(g=s)||"%2e"===K(g)?"/"===h||"\\"===h&&this.isSpecial()||L(this.path,""):("file"===this.scheme&&!this.path.length&&tv(s)&&(this.host&&(this.host=""),s=R(s,0)+":"),L(this.path,s)),s="","file"===this.scheme&&(h===n||"?"===h||"#"===h))for(;this.path.length>1&&""===this.path[0];)F(this.path);"?"===h?(this.query="",r=tD):"#"===h&&(this.fragment="",r=tL)}else s+=tf(h,td);break;case tz:"?"===h?(this.query="",r=tD):"#"===h?(this.fragment="",r=tL):h!==n&&(this.path[0]+=tf(h,tu));break;case tD:e||"#"!==h?h!==n&&("'"===h&&this.isSpecial()?this.query+="%27":"#"===h?this.query+="%23":this.query+=tf(h,tu)):(this.fragment="",r=tL);break;case tL:h!==n&&(this.fragment+=tf(h,th))}a++}},parseHost:function(t){var e,i,n;if("["===R(t,0)){if("]"!==R(t,t.length-1)||!(e=to(V(t,1,-1))))return J;this.host=e}else if(this.isSpecial()){if(M(te,t=g(t))||null===(e=ts(t)))return J;this.host=e}else{if(M(ti,t))return J;for(n=0,e="",i=_(t);n<i.length;n++)e+=tf(i[n],tu);this.host=e}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return p(t_,this.scheme)},shortenPath:function(){var t=this.path,e=t.length;e&&("file"!==this.scheme||1!==e||!tv(t[0],!0))&&t.length--},serialize:function(){var t=this.scheme,e=this.username,i=this.password,n=this.host,r=this.port,a=this.path,s=this.query,o=this.fragment,l=t+":";return null!==n?(l+="//",this.includesCredentials()&&(l+=e+(i?":"+i:"")+"@"),l+=tc(n),null!==r&&(l+=":"+r)):"file"===t&&(l+="//"),l+=this.cannotBeABaseURL?a[0]:a.length?"/"+N(a,"/"):"",null!==s&&(l+="?"+s),null!==o&&(l+="#"+o),l},setHref:function(t){var e=this.parse(t);if(e)throw new P(e);this.searchParams.update()},getOrigin:function(){var t=this.scheme,e=this.port;if("blob"===t)try{return new tF(t.path[0]).origin}catch(t){return"null"}return"file"!==t&&this.isSpecial()?t+"://"+tc(this.host)+(null!==e?":"+e:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(t){this.parse(y(t)+":",tg)},getUsername:function(){return this.username},setUsername:function(t){var e=_(y(t));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var i=0;i<e.length;i++)this.username+=tf(e[i],tp)}},getPassword:function(){return this.password},setPassword:function(t){var e=_(y(t));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var i=0;i<e.length;i++)this.password+=tf(e[i],tp)}},getHost:function(){var t=this.host,e=this.port;return null===t?"":null===e?tc(t):tc(t)+":"+e},setHost:function(t){this.cannotBeABaseURL||this.parse(t,tE)},getHostname:function(){var t=this.host;return null===t?"":tc(t)},setHostname:function(t){this.cannotBeABaseURL||this.parse(t,tP)},getPort:function(){var t=this.port;return null===t?"":y(t)},setPort:function(t){this.cannotHaveUsernamePasswordPort()||(""===(t=y(t))?this.port=null:this.parse(t,tC))},getPathname:function(){var t=this.path;return this.cannotBeABaseURL?t[0]:t.length?"/"+N(t,"/"):""},setPathname:function(t){this.cannotBeABaseURL||(this.path=[],this.parse(t,tM))},getSearch:function(){var t=this.query;return t?"?"+t:""},setSearch:function(t){""===(t=y(t))?this.query=null:("?"===R(t,0)&&(t=V(t,1)),this.query="",this.parse(t,tD)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var t=this.fragment;return t?"#"+t:""},setHash:function(t){if(""===(t=y(t))){this.fragment=null;return}"#"===R(t,0)&&(t=V(t,1)),this.fragment="",this.parse(t,tL)},update:function(){this.query=this.searchParams.serialize()||null}};var tF=function(t){var e=d(this,tj),i=b(arguments.length,1)>1?arguments[1]:void 0,n=A(e,new tW(t,!1,i));a||(e.href=n.serialize(),e.origin=n.getOrigin(),e.protocol=n.getProtocol(),e.username=n.getUsername(),e.password=n.getPassword(),e.host=n.getHost(),e.hostname=n.getHostname(),e.port=n.getPort(),e.pathname=n.getPathname(),e.search=n.getSearch(),e.searchParams=n.getSearchParams(),e.hash=n.getHash())},tj=tF.prototype,tV=function(t,e){return{get:function(){return x(this)[t]()},set:e&&function(t){return x(this)[e](t)},configurable:!0,enumerable:!0}};if(a&&(h(tj,"href",tV("serialize","setHref")),h(tj,"origin",tV("getOrigin")),h(tj,"protocol",tV("getProtocol","setProtocol")),h(tj,"username",tV("getUsername","setUsername")),h(tj,"password",tV("getPassword","setPassword")),h(tj,"host",tV("getHost","setHost")),h(tj,"hostname",tV("getHostname","setHostname")),h(tj,"port",tV("getPort","setPort")),h(tj,"pathname",tV("getPathname","setPathname")),h(tj,"search",tV("getSearch","setSearch")),h(tj,"searchParams",tV("getSearchParams")),h(tj,"hash",tV("getHash","setHash"))),u(tj,"toJSON",function(){return x(this).serialize()},{enumerable:!0}),u(tj,"toString",function(){return x(this).serialize()},{enumerable:!0}),E){var tK=E.createObjectURL,tB=E.revokeObjectURL;tK&&u(tF,"createObjectURL",l(tK,E)),tB&&u(tF,"revokeObjectURL",l(tB,E))}w(tF,"URL"),r({global:!0,constructor:!0,forced:!s,sham:!a},{URL:tF})},91634:function(t,e,i){"use strict";i(92530)},98383:function(t,e,i){"use strict";var n=i(96122),r=i(59528);n({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return r(URL.prototype.toString,this)}})},6072:function(t,e,i){"use strict";function n(t,e){if(e.has(t))throw TypeError("Cannot initialize the same private elements twice on an object")}i.d(e,{_:()=>n}),i(92037)},69661:function(t,e,i){"use strict";function n(t,e){return e.get?e.get.call(t):e.value}i.d(e,{_:()=>n})},60543:function(t,e,i){"use strict";function n(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw TypeError("attempted to set read only private field");e.value=i}}i.d(e,{_:()=>n}),i(92037)},33101:function(t,e,i){"use strict";function n(t,e,i){if(!e.has(t))throw TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}i.d(e,{_:()=>n}),i(92037)},34056:function(t,e,i){"use strict";i.d(e,{_:()=>a});var n=i(69661),r=i(33101);function a(t,e){var i=(0,r._)(t,e,"get");return(0,n._)(t,i)}},12499:function(t,e,i){"use strict";i.d(e,{_:()=>r});var n=i(6072);function r(t,e,i){(0,n._)(t,e),e.set(t,i)}},17970:function(t,e,i){"use strict";function n(t,e,i){if(!e.has(t))throw TypeError("attempted to get private field on non-instance");return i}i.d(e,{_:()=>n}),i(92037)},91412:function(t,e,i){"use strict";i.d(e,{_:()=>r});var n=i(6072);function r(t,e){(0,n._)(t,e),e.add(t)}},17431:function(t,e,i){"use strict";function n(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}i.d(e,{_:()=>n})},72708:function(t,e,i){"use strict";i.d(e,{TN:()=>n.TN,_f:()=>n._f,af:()=>n.af});var n=i(8527);i(64310)},71353:function(t,e,i){"use strict";i.d(e,{Ly:()=>p,Pm:()=>f,QL:()=>v}),i(13189),i(63277),i(82230),i(27899),i(37389),i(27968),i(46299),i(65223),i(87945),i(56113),i(28419);var n={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},r=new Set(["first_factor","second_factor","multi_factor"]),a=new Set(["strict_mfa","strict","moderate","lax"]),s=t=>"number"==typeof t&&t>0,o=t=>r.has(t),l=t=>a.has(t),c=t=>t.replace(/^(org:)*/,"org:"),u=(t,e)=>{let{orgId:i,orgRole:n,orgPermissions:r}=e;return(t.role||t.permission)&&i&&n&&r?t.permission?r.includes(c(t.permission)):t.role?c(n)===c(t.role):null:null},h=(t,e)=>{let{org:i,user:n}=p(t),[r,a]=e.split(":"),s=a||r;return"org"===r?i.includes(s):"user"===r?n.includes(s):[...i,...n].includes(s)},d=(t,e)=>{let{features:i,plans:n}=e;return t.feature&&i?h(i,t.feature):t.plan&&n?h(n,t.plan):null},p=t=>{let e=t?t.split(",").map(t=>t.trim()):[];return{org:e.filter(t=>t.split(":")[0].includes("o")).map(t=>t.split(":")[1]),user:e.filter(t=>t.split(":")[0].includes("u")).map(t=>t.split(":")[1])}},f=t=>{if(!t)return!1;let e="string"==typeof t&&l(t),i="object"==typeof t&&o(t.level)&&s(t.afterMinutes);return(!!e||!!i)&&(t=>"string"==typeof t?n[t]:t).bind(null,t)},_=(t,e)=>{let{factorVerificationAge:i}=e;if(!t.reverification||!i)return null;let n=f(t.reverification);if(!n)return null;let{level:r,afterMinutes:a}=n(),[s,o]=i,l=-1!==s?a>s:null,c=-1!==o?a>o:null;switch(r){case"first_factor":return l;case"second_factor":return -1!==o?c:l;case"multi_factor":return -1===o?l:l&&c}},v=t=>e=>{if(!t.userId)return!1;let i=d(e,t),n=u(e,t),r=_(e,t);return[i||n,r].some(t=>null===t)?[i||n,r].some(t=>!0===t):[i||n,r].every(t=>!0===t)}},18532:function(t,e,i){"use strict";function n(t){return["captcha_invalid","captcha_not_enabled","captcha_missing_token"].includes(t.errors[0].code)}function r(t){let e=null==t?void 0:t.status;return!!e&&e>=400&&e<500}function a(t){return("".concat(t.message).concat(t.name)||"").toLowerCase().replace(/\s+/g,"").includes("networkerror")}function s(t){return o(t)||u(t)||l(t)}function o(t){return"clerkError"in t}function l(t){return"clerkRuntimeError"in t}function c(t){return l(t)&&"reverification_cancelled"===t.code}function u(t){return"code"in t&&[4001,32602,32603].includes(t.code)&&"message"in t}function h(t){var e,i;return o(t)&&(null===(i=t.errors)||void 0===i?void 0:null===(e=i[0])||void 0===e?void 0:e.code)==="user_locked"}function d(t){var e,i;return o(t)&&(null===(i=t.errors)||void 0===i?void 0:null===(e=i[0])||void 0===e?void 0:e.code)==="form_password_pwned"}function p(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.length>0?t.map(f):[]}function f(t){var e,i,n,r,a,s;return{code:t.code,message:t.message,longMessage:t.long_message,meta:{paramName:null==t?void 0:null===(e=t.meta)||void 0===e?void 0:e.param_name,sessionId:null==t?void 0:null===(i=t.meta)||void 0===i?void 0:i.session_id,emailAddresses:null==t?void 0:null===(n=t.meta)||void 0===n?void 0:n.email_addresses,identifiers:null==t?void 0:null===(r=t.meta)||void 0===r?void 0:r.identifiers,zxcvbn:null==t?void 0:null===(a=t.meta)||void 0===a?void 0:a.zxcvbn,plan:null==t?void 0:null===(s=t.meta)||void 0===s?void 0:s.plan}}}function _(t){var e,i,n,r,a,s;return{code:(null==t?void 0:t.code)||"",message:(null==t?void 0:t.message)||"",long_message:null==t?void 0:t.longMessage,meta:{param_name:null==t?void 0:null===(e=t.meta)||void 0===e?void 0:e.paramName,session_id:null==t?void 0:null===(i=t.meta)||void 0===i?void 0:i.sessionId,email_addresses:null==t?void 0:null===(n=t.meta)||void 0===n?void 0:n.emailAddresses,identifiers:null==t?void 0:null===(r=t.meta)||void 0===r?void 0:r.identifiers,zxcvbn:null==t?void 0:null===(a=t.meta)||void 0===a?void 0:a.zxcvbn,plan:null==t?void 0:null===(s=t.meta)||void 0===s?void 0:s.plan}}}i.d(e,{G1:()=>y,LT:()=>_,N:()=>g,RK:()=>k,UZ:()=>d,ZC:()=>u,ay:()=>h,eE:()=>a,gO:()=>v,iR:()=>p,ix:()=>r,kD:()=>o,mh:()=>c,nU:()=>f,pG:()=>n,sZ:()=>s,t5:()=>S,u$:()=>w,uX:()=>l,w$:()=>m}),i(28419),i(87945),i(56113),i(92037),i(80194),i(25800),i(99881),i(65223);var v=class t extends Error{constructor(e,{data:i,status:n,clerkTraceId:r,retryAfter:a}){super(e),this.toString=()=>{let t="[".concat(this.name,"]\nMessage:").concat(this.message,"\nStatus:").concat(this.status,"\nSerialized errors: ").concat(this.errors.map(t=>JSON.stringify(t)));return this.clerkTraceId&&(t+="\nClerk Trace ID: ".concat(this.clerkTraceId)),t},Object.setPrototypeOf(this,t.prototype),this.status=n,this.message=e,this.clerkTraceId=r,this.retryAfter=a,this.clerkError=!0,this.errors=p(i)}},m=class t extends Error{constructor(e,{code:i}){let n="\uD83D\uDD12 Clerk:",r=RegExp(n.replace(" ","\\s*"),"i"),a=e.replace(r,""),s="".concat(n," ").concat(a.trim(),'\n\n(code="').concat(i,'")\n');super(s),this.toString=()=>"[".concat(this.name,"]\nMessage:").concat(this.message),Object.setPrototypeOf(this,t.prototype),this.code=i,this.message=s,this.clerkRuntimeError=!0,this.name="ClerkRuntimeError"}},g=class t extends Error{constructor(e){super(e),this.code=e,this.name="EmailLinkError",Object.setPrototypeOf(this,t.prototype)}};function y(t){return"EmailLinkError"===t.name}var w={Expired:"expired",Failed:"failed",ClientMismatch:"client_mismatch"},b=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function S(t){let{packageName:e,customMessages:i}=t,n=e,r={...b,...i};function a(t,e){if(!e)return"".concat(n,": ").concat(t);let i=t;for(let n of t.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let t=(e[n[1]]||"").toString();i=i.replace("{{".concat(n[1],"}}"),t)}return"".concat(n,": ").concat(i)}return{setPackageName(t){let{packageName:e}=t;return"string"==typeof e&&(n=e),this},setMessages(t){let{customMessages:e}=t;return Object.assign(r,e||{}),this},throwInvalidPublishableKeyError(t){throw Error(a(r.InvalidPublishableKeyErrorMessage,t))},throwInvalidProxyUrl(t){throw Error(a(r.InvalidProxyUrlErrorMessage,t))},throwMissingPublishableKeyError(){throw Error(a(r.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(a(r.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(t){throw Error(a(r.MissingClerkProvider,t))},throw(t){throw Error(a(t))}}}var k=class extends m{constructor(t,{code:e}){super(t,{code:e}),this.code=e}}},64310:function(t,e,i){"use strict";i.d(e,{Ko:()=>p,U9:()=>_,ac:()=>d,qx:()=>f,r2:()=>l,yA:()=>u}),i(92037),i(65223);var n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,o=t=>{throw TypeError(t)},l=(t,e)=>{for(var i in e)n(t,i,{get:e[i],enumerable:!0})},c=(t,e,i,o)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let l of a(e))s.call(t,l)||l===i||n(t,l,{get:()=>e[l],enumerable:!(o=r(e,l))||o.enumerable});return t},u=(t,e,i)=>(c(t,e,"default"),i&&c(i,e,"default")),h=(t,e,i)=>e.has(t)||o("Cannot "+i),d=(t,e,i)=>(h(t,e,"read from private field"),i?i.call(t):e.get(t)),p=(t,e,i)=>e.has(t)?o("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),f=(t,e,i,n)=>(h(t,e,"write to private field"),n?n.call(t,i):e.set(t,i),i),_=(t,e,i)=>(h(t,e,"access private method"),i)},18796:function(t,e,i){"use strict";i.d(e,{Z:()=>n});var n=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i]}},53959:function(t,e,i){"use strict";i.d(e,{rx:()=>a,uB:()=>r,vf:()=>n});var n=()=>!1,r=()=>!1,a=()=>{try{return!0}catch{}return!1}},87989:function(t,e,i){"use strict";i.d(e,{W:()=>r}),i(50725);var n=i(18796),r=()=>{let t=n.Z,e=n.Z;return{promise:new Promise((i,n)=>{t=i,e=n}),resolve:t,reject:e}}},67736:function(t,e,i){"use strict";function n(t){let e=t||"";return e.charAt(0).toUpperCase()+e.slice(1)}function r(t){return t?t.replace(/([-_][a-z])/g,t=>t.toUpperCase().replace(/-|_/,"")):""}function a(t){return t?t.replace(/[A-Z]/g,t=>"_".concat(t.toLowerCase())):""}i.d(e,{MI:()=>n,TD:()=>r,a1:()=>a,aw:()=>l,fQ:()=>c,hF:()=>u,zb:()=>o}),i(56113),i(87945),i(65223),i(93008);var s=t=>{let e=i=>{if(!i)return i;if(Array.isArray(i))return i.map(t=>"object"==typeof t||Array.isArray(t)?e(t):t);let n={...i};for(let i of Object.keys(n)){let r=t(i.toString());r!==i&&(n[r]=n[i],delete n[i]),"object"==typeof n[r]&&(n[r]=e(n[r]))}return n};return e},o=s(a),l=s(r);function c(t){if("boolean"==typeof t)return t;if(null==t)return!1;if("string"==typeof t){if("true"===t.toLowerCase())return!0;if("false"===t.toLowerCase())return!1}let e=parseInt(t,10);return!isNaN(e)&&e>0}function u(t){return Object.entries(t).reduce((t,e)=>{let[i,n]=e;return void 0!==n&&(t[i]=n),t},{})}},86225:function(t,e,i){"use strict";i.d(e,{n:()=>s}),i(79876),i(65223);var n=(t,e,i,n,r)=>{let{notify:a}=r||{},s=t.get(i);s||(s=[],t.set(i,s)),s.push(n),a&&e.has(i)&&n(e.get(i))},r=(t,e,i)=>(t.get(e)||[]).map(t=>t(i)),a=(t,e,i)=>{let n=t.get(e);n&&(i?n.splice(n.indexOf(i)>>>0,1):t.set(e,[]))},s=()=>{let t=new Map,e=new Map,i=new Map;return{on:function(){for(var i=arguments.length,r=Array(i),a=0;a<i;a++)r[a]=arguments[a];return n(t,e,...r)},prioritizedOn:function(){for(var t=arguments.length,r=Array(t),a=0;a<t;a++)r[a]=arguments[a];return n(i,e,...r)},emit:(n,a)=>{e.set(n,a),r(i,n,a),r(t,n,a)},off:function(){for(var e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];return a(t,...i)},prioritizedOff:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return a(i,...e)},internal:{retrieveListeners:e=>t.get(e)||[]}}}},76182:function(t,e,i){"use strict";i.d(e,{JF:()=>r,WY:()=>s,mv:()=>n,vO:()=>a});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],r=[".accounts.dev",".accountsstage.dev",".accounts.lclclerk.com"],a=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"];function s(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"svg";return"https://img.clerk.com/static/".concat(t,".").concat(e)}},8527:function(t,e,i){"use strict";function n(){return"undefined"!=typeof window}i.d(e,{Er:()=>a,TN:()=>s,_f:()=>n,af:()=>o}),i(80194),i(25800),i(56113);var r=RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i");function a(){var t,e;let i=n()?null===(t=window)||void 0===t?void 0:t.navigator:null;return!!i&&!((e=null==i?void 0:i.userAgent)&&r.test(e))&&!(null==i?void 0:i.webdriver)}function s(){var t,e,i;let r=n()?null===(t=window)||void 0===t?void 0:t.navigator:null;if(!r)return!1;let a=null==r?void 0:r.onLine;return(null==r?void 0:null===(e=r.connection)||void 0===e?void 0:e.rtt)!==0&&(null==r?void 0:null===(i=r.connection)||void 0===i?void 0:i.downlink)!==0&&a}function o(){return s()&&a()}},32833:function(t,e,i){"use strict";i.d(e,{M:()=>n}),i(50886);var n=t=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(t):"undefined"!=typeof global&&global.Buffer?new global.Buffer(t).toString("base64"):t},75576:function(t,e,i){"use strict";i.d(e,{X:()=>o}),i(50725);var n={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(t,e)=>e<5,retryImmediately:!1,jitter:!0},r=async t=>new Promise(e=>setTimeout(e,t)),a=(t,e)=>e?t*(1+Math.random()):t,s=t=>{let e=0,i=()=>{let i=t.initialDelay*Math.pow(t.factor,e);return i=a(i,t.jitter),Math.min(t.maxDelayBetweenRetries||i,i)};return async()=>{await r(i()),e++}},o=async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=0,{shouldRetry:o,initialDelay:l,maxDelayBetweenRetries:c,factor:u,retryImmediately:h,jitter:d}={...n,...e},p=s({initialDelay:l,maxDelayBetweenRetries:c,factor:u,jitter:d});for(;;)try{return await t()}catch(t){if(!o(t,++i))throw t;h&&1===i?await r(a(100,d)):await p()}}},75938:function(t,e,i){"use strict";i.d(e,{MY:()=>c,P:()=>d,_d:()=>p,kZ:()=>u,mA:()=>h,nQ:()=>o}),i(56113),i(92037),i(65223),i(73588),i(75184),i(12100),i(29646),i(67581),i(63782),i(14311),i(72547),i(86936),i(20889),i(54357),i(86775),i(6763),i(87945),i(50725);var n=i(10290),r=i(32833),a=i(76182),s="pk_live_";function o(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t=t||"")||!l(t)){if(e.fatal&&!t)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(e.fatal&&!l(t))throw Error("Publishable key not valid.");return null}let i=t.startsWith(s)?"production":"development",r=(0,n.S)(t.split("_")[2]);return r=r.slice(0,-1),e.proxyUrl?r=e.proxyUrl:"development"!==i&&e.domain&&e.isSatellite&&(r="clerk.".concat(e.domain)),{instanceType:i,frontendApi:r}}function l(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{let e=t.startsWith(s)||t.startsWith("pk_test_"),i=(0,n.S)(t.split("_")[2]||"").endsWith("$");return e&&i}catch{return!1}}function c(){let t=new Map;return{isDevOrStagingUrl:e=>{if(!e)return!1;let i="string"==typeof e?e:e.hostname,n=t.get(i);return void 0===n&&(n=a.vO.some(t=>i.endsWith(t)),t.set(i,n)),n}}}function u(t){return t.startsWith("test_")||t.startsWith("pk_test_")}function h(t){return t.startsWith("live_")||t.startsWith("pk_live_")}async function d(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis.crypto.subtle,i=new TextEncoder().encode(t),n=String.fromCharCode(...new Uint8Array(await e.digest("sha-1",i)));return(0,r.M)(n).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var p=(t,e)=>"".concat(t,"_").concat(e)},10290:function(t,e,i){"use strict";i.d(e,{S:()=>n}),i(50886);var n=t=>"undefined"!=typeof atob&&"function"==typeof atob?atob(t):"undefined"!=typeof global&&global.Buffer?new global.Buffer(t,"base64").toString():t},31375:function(t,e,i){"use strict";i.d(e,{J6:()=>R,NS:()=>U,Zg:()=>T,nx:()=>C,uT:()=>I}),i(65223),i(50886),i(79876),i(50725),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349);var n,r,a,s,o,l,c,u,h,d,p,f,_,v,m,g,y,w,b,S=i(67736),k=i(75938),A=i(64310),x=class{isEventThrottled(t){var e;if(!(0,A.ac)(this,a,l))return!1;let i=Date.now(),c=(0,A.U9)(this,a,s).call(this,t),u=null===(e=(0,A.ac)(this,a,o))||void 0===e?void 0:e[c];if(!u){let t={...(0,A.ac)(this,a,o),[c]:i};localStorage.setItem((0,A.ac)(this,n),JSON.stringify(t))}if(u&&i-u>(0,A.ac)(this,r)){let t=(0,A.ac)(this,a,o);delete t[c],localStorage.setItem((0,A.ac)(this,n),JSON.stringify(t))}return!!u}constructor(){(0,A.Ko)(this,a),(0,A.Ko)(this,n,"clerk_telemetry_throttler"),(0,A.Ko)(this,r,864e5)}};n=new WeakMap,r=new WeakMap,a=new WeakSet,s=function(t){let{sk:e,pk:i,payload:n,...r}=t,a={...n,...r};return JSON.stringify(Object.keys({...n,...r}).sort().map(t=>a[t]))},o=function(){let t=localStorage.getItem((0,A.ac)(this,n));return t?JSON.parse(t):{}},l=function(){if("undefined"==typeof window)return!1;let t=window.localStorage;if(!t)return!1;try{let e="test";return t.setItem(e,e),t.removeItem(e),!0}catch(e){return e instanceof DOMException&&("QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&t.length>0&&t.removeItem((0,A.ac)(this,n)),!1}};var O={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},U=class{get isEnabled(){var t,e;return!("development"!==(0,A.ac)(this,h).instanceType||(0,A.ac)(this,c).disabled||"undefined"!=typeof process&&(0,S.fQ)(process.env.CLERK_TELEMETRY_DISABLED))&&("undefined"==typeof window||null===(e=window)||void 0===e||null===(t=e.navigator)||void 0===t||!t.webdriver)}get isDebug(){return(0,A.ac)(this,c).debug||"undefined"!=typeof process&&(0,S.fQ)(process.env.CLERK_TELEMETRY_DEBUG)}record(t){let e=(0,A.U9)(this,f,b).call(this,t.event,t.payload);(0,A.U9)(this,f,y).call(this,e.event,e),(0,A.U9)(this,f,_).call(this,e,t.eventSamplingRate)&&((0,A.ac)(this,d).push(e),(0,A.U9)(this,f,m).call(this))}constructor(t){var e,i,n,r,a,s;(0,A.Ko)(this,f),(0,A.Ko)(this,c),(0,A.Ko)(this,u),(0,A.Ko)(this,h,{}),(0,A.Ko)(this,d,[]),(0,A.Ko)(this,p),(0,A.qx)(this,c,{maxBufferSize:null!==(e=t.maxBufferSize)&&void 0!==e?e:O.maxBufferSize,samplingRate:null!==(i=t.samplingRate)&&void 0!==i?i:O.samplingRate,disabled:null!==(n=t.disabled)&&void 0!==n&&n,debug:null!==(r=t.debug)&&void 0!==r&&r,endpoint:O.endpoint}),t.clerkVersion||"undefined"!=typeof window?(0,A.ac)(this,h).clerkVersion=null!==(a=t.clerkVersion)&&void 0!==a?a:"":(0,A.ac)(this,h).clerkVersion="",(0,A.ac)(this,h).sdk=t.sdk,(0,A.ac)(this,h).sdkVersion=t.sdkVersion,(0,A.ac)(this,h).publishableKey=null!==(s=t.publishableKey)&&void 0!==s?s:"";let o=(0,k.nQ)(t.publishableKey);o&&((0,A.ac)(this,h).instanceType=o.instanceType),t.secretKey&&((0,A.ac)(this,h).secretKey=t.secretKey.substring(0,16)),(0,A.qx)(this,u,new x)}};c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,f=new WeakSet,_=function(t,e){return this.isEnabled&&!this.isDebug&&(0,A.U9)(this,f,v).call(this,t,e)},v=function(t,e){let i=Math.random();return i<=(0,A.ac)(this,c).samplingRate&&(void 0===e||i<=e)&&!(0,A.ac)(this,u).isEventThrottled(t)},m=function(){if("undefined"==typeof window){(0,A.U9)(this,f,g).call(this);return}if((0,A.ac)(this,d).length>=(0,A.ac)(this,c).maxBufferSize){(0,A.ac)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,A.ac)(this,p)),(0,A.U9)(this,f,g).call(this);return}!(0,A.ac)(this,p)&&("requestIdleCallback"in window?(0,A.qx)(this,p,requestIdleCallback(()=>{(0,A.U9)(this,f,g).call(this)})):(0,A.qx)(this,p,setTimeout(()=>{(0,A.U9)(this,f,g).call(this)},0)))},g=function(){fetch(new URL("/v1/event",(0,A.ac)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,A.ac)(this,d)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,A.qx)(this,d,[])}).catch(()=>void 0)},y=function(t,e){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",t),console.log(e),console.groupEnd()):console.log("[clerk/telemetry]",t,e))},w=function(){let t={name:(0,A.ac)(this,h).sdk,version:(0,A.ac)(this,h).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(t={...t,...window.Clerk.constructor.sdkMetadata}),t},b=function(t,e){var i,n;let r=(0,A.U9)(this,f,w).call(this);return{event:t,cv:null!==(i=(0,A.ac)(this,h).clerkVersion)&&void 0!==i?i:"",it:null!==(n=(0,A.ac)(this,h).instanceType)&&void 0!==n?n:"",sdk:r.name,sdkv:r.version,...(0,A.ac)(this,h).publishableKey?{pk:(0,A.ac)(this,h).publishableKey}:{},...(0,A.ac)(this,h).secretKey?{sk:(0,A.ac)(this,h).secretKey}:{},payload:e}};var E="COMPONENT_MOUNTED";function P(t){return function(e,i,n){var r,a,s;return{event:t,eventSamplingRate:.1,payload:{component:e,appearanceProp:!!(null==i?void 0:i.appearance),baseTheme:!!(null==i?void 0:null===(r=i.appearance)||void 0===r?void 0:r.baseTheme),elements:!!(null==i?void 0:null===(a=i.appearance)||void 0===a?void 0:a.elements),variables:!!(null==i?void 0:null===(s=i.appearance)||void 0===s?void 0:s.variables),...n}}}}function C(t,e,i){return P(E)(t,e,i)}function I(t,e,i){return P("COMPONENT_OPENED")(t,e,i)}function T(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{event:E,eventSamplingRate:.1,payload:{component:t,...e}}}function R(t,e){return{event:"METHOD_CALLED",payload:{method:t,...e}}}},79695:function(t,e,i){"use strict";i.d(e,{Q:()=>o}),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223);var n=i(18796),r='const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener("message",r=>{const e=r.data;switch(e.type){case"setTimeout":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id}),delete workerToTabIds[e.id]},e.ms);break;case"clearTimeout":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case"setInterval":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case"clearInterval":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\n',a=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("undefined"==typeof Worker)return null;try{let i=new Blob([t],{type:"application/javascript; charset=utf-8"}),n=globalThis.URL.createObjectURL(i);return new Worker(n,e)}catch{return console.warn("Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP"),null}},s=()=>{let t=globalThis.setTimeout.bind(globalThis),e=globalThis.setInterval.bind(globalThis);return{setTimeout:t,setInterval:e,clearTimeout:globalThis.clearTimeout.bind(globalThis),clearInterval:globalThis.clearInterval.bind(globalThis),cleanup:n.Z}},o=()=>{let t=0,e=()=>t++,i=new Map,n=(t,e)=>null==t?void 0:t.postMessage(e),o=t=>{var e;null===(e=i.get(t.data.id))||void 0===e||e()},l=a(r,{name:"clerk-timers"});if(null==l||l.addEventListener("message",o),!l)return s();let c=()=>{l||null==(l=a(r,{name:"clerk-timers"}))||l.addEventListener("message",o)};return{setTimeout:(t,r)=>{c();let a=e();return i.set(a,()=>{t(),i.delete(a)}),n(l,{type:"setTimeout",id:a,ms:r}),a},setInterval:(t,r)=>{c();let a=e();return i.set(a,t),n(l,{type:"setInterval",id:a,ms:r}),a},clearTimeout:t=>{c(),i.delete(t),n(l,{type:"clearTimeout",id:t})},clearInterval:t=>{c(),i.delete(t),n(l,{type:"clearInterval",id:t})},cleanup:()=>{l&&(l.terminate(),l=null,i.clear())}}}},98387:function(t,e,i){"use strict";function n(t){try{return new Date(t||new Date)}catch{return new Date}}function r(t){let{date:e,relativeTo:i}=t;if(!e||!i)return null;let r=n(e),a=function(t,e){let{absolute:i=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t||!e)return 0;let n=Date.UTC(t.getFullYear(),t.getMonth(),t.getDate()),r=Math.floor((Date.UTC(e.getFullYear(),e.getMonth(),e.getDate())-n)/864e5);return i?Math.abs(r):r}(n(i),r,{absolute:!1});return a<-6?{relativeDateCase:"other",date:r}:a<-1?{relativeDateCase:"previous6Days",date:r}:-1===a?{relativeDateCase:"lastDay",date:r}:0===a?{relativeDateCase:"sameDay",date:r}:1===a?{relativeDateCase:"nextDay",date:r}:a<7?{relativeDateCase:"next6Days",date:r}:{relativeDateCase:"other",date:r}}function a(t,e){let i=n(t);return i.setFullYear(i.getFullYear()+e),i}i.d(e,{lY:()=>r,P9:()=>n,Bc:()=>a}),i(64310)},81188:function(t,e,i){"use strict";i.d(e,{x6:()=>s,x9:()=>a}),i(13189),i(63277),i(82230),i(27899),i(37389),i(27968),i(46299),i(65223);var n=i(53959),r=new Set,a=(t,e,i)=>{let a=(0,n.uB)()||(0,n.rx)(),s=null!=i?i:t;!r.has(s)&&!a&&(r.add(s),console.warn('Clerk - DEPRECATION WARNING: "'.concat(t,'" is deprecated and will be removed in the next major release.\n').concat(e)))},s=(t,e,i,n)=>{let r=t[e];Object.defineProperty(t,e,{get:()=>(a(e,i,n),r),set(t){r=t}})};i(64310)},73531:function(t,e,i){"use strict";i.d(e,{G1:()=>n.G1,LT:()=>n.LT,N:()=>n.N,RK:()=>n.RK,UZ:()=>n.UZ,ZC:()=>n.ZC,ay:()=>n.ay,eE:()=>n.eE,gO:()=>n.gO,iR:()=>n.iR,ix:()=>n.ix,kD:()=>n.kD,mh:()=>n.mh,nU:()=>n.nU,pG:()=>n.pG,sZ:()=>n.sZ,t5:()=>n.t5,u$:()=>n.u$,uX:()=>n.uX,w$:()=>n.w$});var n=i(18532);i(64310)},41402:function(t,e,i){"use strict";i.d(e,{MY:()=>n.MY,P:()=>n.P,_d:()=>n._d,kZ:()=>n.kZ,mA:()=>n.mA,nQ:()=>n.nQ});var n=i(75938);i(10290),i(32833),i(64310)},40748:function(t,e,i){"use strict";i.d(e,{v:()=>r}),i(50725),i(92037);var n=i(75576);async function r(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,{async:i,defer:r,beforeLoad:a,crossOrigin:s,nonce:o}=e||{};return(0,n.X)(()=>new Promise((e,n)=>{t||n(Error("loadScript cannot be called without a src")),document&&document.body||n("loadScript cannot be called when document does not exist");let l=document.createElement("script");s&&l.setAttribute("crossorigin",s),l.async=i||!1,l.defer=r||!1,l.addEventListener("load",()=>{l.remove(),e(l)}),l.addEventListener("error",()=>{l.remove(),n()}),l.src=t,l.nonce=o,null==a||a(l),document.body.appendChild(l)}),{shouldRetry:(t,e)=>e<=5})}i(64310)},82358:function(t,e,i){"use strict";i.d(e,{k:()=>r}),i(13189),i(63277),i(82230),i(27899),i(37389),i(27968),i(46299),i(65223);var n=new Set,r={warnOnce:t=>{!n.has(t)&&(n.add(t),console.warn(t))},logOnce:t=>{!n.has(t)&&(console.log(t),n.add(t))}};i(64310)},67509:function(t,e,i){"use strict";i.d(e,{A:()=>n}),i(28419),i(91634),i(98383),i(45261),i(70957),i(24551),i(22349),i(65223),i(75938);var n="__clerk_netlify_cache_bust";i(10290),i(32833),i(64310)},96111:function(t,e,i){"use strict";i.d(e,{W:()=>r});var n=i(79695);function r(){let t,{delayInMs:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{delayInMs:1e3},i=(0,n.Q)(),r=!1,a=()=>{t&&(i.clearTimeout(t),i.cleanup()),r=!0},s=async n=>{r=!1,await n(a),!r&&(t=i.setTimeout(()=>{s(n)},e))};return{run:s,stop:a}}i(64310)},60153:function(t,e,i){"use strict";i.d(e,{X:()=>n.X});var n=i(75576);i(64310)},72810:function(t,e,i){"use strict";i.d(e,{NS:()=>n.NS,Zg:()=>n.Zg,nx:()=>n.nx,uT:()=>n.uT});var n=i(31375);i(67736),i(75938),i(10290),i(32833),i(64310)},65027:function(t,e,i){"use strict";i.d(e,{MI:()=>n.MI,TD:()=>n.TD,a1:()=>n.a1,aw:()=>n.aw,hF:()=>n.hF,zb:()=>n.zb});var n=i(67736);i(64310)},94944:function(t,e,i){"use strict";i.d(e,{xy:()=>a,LQ:()=>o,sD:()=>c,A5:()=>s,d5:()=>r}),i(45261),i(70957),i(24551),i(22349),i(65223),i(87945),i(56113);var n=i(76182);function r(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return(t||"").replace(/^.+:\/\//,"")}function a(t){let e;if(!t)return"";if(t.match(/^(clerk\.)+\w*$/))e=/(clerk\.)*(?=clerk\.)/;else{if(t.match(/\.clerk.accounts/))return t;e=/^(clerk\.)*/gi}let i=t.replace(e,"");return"clerk.".concat(i)}function s(t){return n.mv.some(e=>t.startsWith("accounts.")&&t.endsWith(e))}function o(t){return n.JF.some(e=>t.endsWith(e)&&!t.endsWith(".clerk"+e))}var l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,c=t=>l.test(t);i(64310)},45100:function(t,e,i){"use strict";function n(t){return Promise.all(Array.from(t).map(t=>t.then(t=>({status:"fulfilled",value:t}),t=>({status:"rejected",reason:t}))))}i.d(e,{WK:()=>a.W,Lu:()=>n,YZ:()=>o,vf:()=>l.vf,EB:()=>r,ZT:()=>s.Z}),i(50725),i(65223);var r=(t,e)=>{if(t&&e)for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&null!==t[i]&&"object"==typeof t[i]?(void 0===e[i]&&(e[i]=new(Object.getPrototypeOf(t[i])).constructor),r(t[i],e[i])):Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},a=i(87989),s=i(18796);function o(t,e,i){return"function"==typeof t?t(e):void 0!==t?t:void 0!==i?i:void 0}var l=i(53959);i(64310)},32208:function(t,e,i){"use strict";i.d(e,{C6:()=>s,h_:()=>a,iW:()=>r}),i(50725);var n=i(8527);function r(){return(0,n.Er)()&&"function"==typeof window.PublicKeyCredential}async function a(){try{return r()&&await window.PublicKeyCredential.isConditionalMediationAvailable()}catch{return!1}}async function s(){try{return"undefined"!=typeof window&&await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()}catch{return!1}}i(64310)}},e={};function i(n){var r=e[n];if(void 0!==r)return r.exports;var a=e[n]={id:n,loaded:!1,exports:{}};return t[n].call(a.exports,a,a.exports,i),a.loaded=!0,a.exports}return i.m=t,i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},(()=>{var t,e=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__;i.t=function(n,r){if(1&r&&(n=this(n)),8&r||"object"==typeof n&&n&&(4&r&&n.__esModule||16&r&&"function"==typeof n.then))return n;var a=Object.create(null);i.r(a);var s={};t=t||[null,e({}),e([]),e(e)];for(var o=2&r&&n;"object"==typeof o&&!~t.indexOf(o);o=e(o))Object.getOwnPropertyNames(o).forEach(t=>{s[t]=()=>n[t]});return s.default=()=>n,i.d(a,s),a}})(),i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.f={},i.e=t=>Promise.all(Object.keys(i.f).reduce((e,n)=>(i.f[n](t,e),e),[])),i.hmd=t=>((t=Object.create(t)).children||(t.children=[]),Object.defineProperty(t,"exports",{enumerable:!0,set:()=>{throw Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+t.id)}}),t),i.u=t=>""+(({150:"op-api-keys-page",158:"organizationlist",199:"cookieSuffix",200:"vendors",211:"prefetchorganizationlist",219:"pricingTable",237:"checkout",270:"userbutton",307:"keylessPrompt",325:"zxcvbn-common",33:"planDetails",344:"framework",378:"impersonationfab",470:"userprofile",507:"paymentSources",554:"organizationprofile",573:"ui-common",616:"apiKeys",642:"revoke-api-key-modal",662:"userverification",708:"zxcvbn-ts-core",710:"signup",722:"signin",776:"oauthConsent",780:"organizationswitcher",809:"up-api-keys-page",82:"up-billing-page",861:"waitlist",875:"blankcaptcha",877:"sessionTasks",916:"op-billing-page",920:"onetap",956:"coinbase-wallet-sdk",96:"createorganization"})[t]||t)+"_clerk.legacy.browser_"+i.h().slice(0,6)+"_5.69.0.js",i.h=()=>"afa84c5cbf4a39f3",(()=>{i.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}})()})(),i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{var t={},e="@clerk/clerk-js:";i.l=function(n,r,a,s){if(t[n]){t[n].push(r);return}if(void 0!==a)for(var o,l,c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var h=c[u];if(h.getAttribute("src")==n||h.getAttribute("data-webpack")==e+a){o=h;break}}o||(l=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,i.nc&&o.setAttribute("nonce",i.nc),o.setAttribute("data-webpack",e+a),o.src=n),t[n]=[r];var d=function(e,i){o.onerror=o.onload=null,clearTimeout(p);var r=t[n];if(delete t[n],o.parentNode&&o.parentNode.removeChild(o),r&&r.forEach(function(t){return t(i)}),e)return e(i)},p=setTimeout(d.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=d.bind(null,o.onerror),o.onload=d.bind(null,o.onload),l&&document.head.appendChild(o)}})(),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{i.g.importScripts&&(t=i.g.location+"");var t,e=i.g.document;if(!t&&e&&(e.currentScript&&"SCRIPT"===e.currentScript.tagName.toUpperCase()&&(t=e.currentScript.src),!t)){var n=e.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&(!t||!/^http(s?):/.test(t));)t=n[r--].src}if(!t)throw Error("Automatic publicPath is not supported in this browser");t=t.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=t})(),(()=>{var t={899:0};i.f.j=function(e,n){var r=i.o(t,e)?t[e]:void 0;if(0!==r){if(r)n.push(r[2]);else{var a=new Promise((i,n)=>r=t[e]=[i,n]);n.push(r[2]=a);var s=i.p+i.u(e),o=Error();i.l(s,function(n){if(i.o(t,e)&&(0!==(r=t[e])&&(t[e]=void 0),r)){var a=n&&("load"===n.type?"missing":n.type),s=n&&n.target&&n.target.src;o.message="Loading chunk "+e+" failed.\n("+a+": "+s+")",o.name="ChunkLoadError",o.type=a,o.request=s,r[1](o)}},"chunk-"+e,e)}}};var e=(e,n)=>{var r,a,s=n[0],o=n[1],l=n[2],c=0;if(s.some(e=>0!==t[e])){for(r in o)i.o(o,r)&&(i.m[r]=o[r]);l&&l(i)}for(e&&e(n);c<s.length;c++)a=s[c],i.o(t,a)&&t[a]&&t[a][0](),t[a]=0},n=globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))})(),i(87355)})());