(()=>{var $;function U(C){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(C)}function x(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function Q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){E(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function E(C,G,H){if(G=N(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function N(C){var G=z(C,"string");return U(G)=="symbol"?G:String(G)}function z(C,G){if(U(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,HC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},D={lessThanXSeconds:{one:"m\xEB pak se nj\xEB sekond\xEB",other:"m\xEB pak se {{count}} sekonda"},xSeconds:{one:"1 sekond\xEB",other:"{{count}} sekonda"},halfAMinute:"gjys\xEBm minuti",lessThanXMinutes:{one:"m\xEB pak se nj\xEB minute",other:"m\xEB pak se {{count}} minuta"},xMinutes:{one:"1 minut\xEB",other:"{{count}} minuta"},aboutXHours:{one:"rreth 1 or\xEB",other:"rreth {{count}} or\xEB"},xHours:{one:"1 or\xEB",other:"{{count}} or\xEB"},xDays:{one:"1 dit\xEB",other:"{{count}} dit\xEB"},aboutXWeeks:{one:"rreth 1 jav\xEB",other:"rreth {{count}} jav\xEB"},xWeeks:{one:"1 jav\xEB",other:"{{count}} jav\xEB"},aboutXMonths:{one:"rreth 1 muaj",other:"rreth {{count}} muaj"},xMonths:{one:"1 muaj",other:"{{count}} muaj"},aboutXYears:{one:"rreth 1 vit",other:"rreth {{count}} vite"},xYears:{one:"1 vit",other:"{{count}} vite"},overXYears:{one:"mbi 1 vit",other:"mbi {{count}} vite"},almostXYears:{one:"pothuajse 1 vit",other:"pothuajse {{count}} vite"}},S=function C(G,H,J){var X,Y=D[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"n\xEB "+X;else return X+" m\xEB par\xEB";return X};function q(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var M={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} 'n\xEB' {{time}}",long:"{{date}} 'n\xEB' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:q({formats:M,defaultWidth:"full"}),time:q({formats:R,defaultWidth:"full"}),dateTime:q({formats:L,defaultWidth:"full"})},j={lastWeek:"'t\xEB' eeee 'e shkuar n\xEB' p",yesterday:"'dje n\xEB' p",today:"'sot n\xEB' p",tomorrow:"'nes\xEBr n\xEB' p",nextWeek:"eeee 'at' p",other:"P"},w=function C(G,H,J,X){return j[G]};function I(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var B=C.defaultWidth,A=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[A]||C.values[B]}var T=C.argumentCallback?C.argumentCallback(G):G;return X[T]}}var _={narrow:["P","M"],abbreviated:["PK","MK"],wide:["Para Krishtit","Mbas Krishtit"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["4-mujori I","4-mujori II","4-mujori III","4-mujori IV"]},F={narrow:["J","S","M","P","M","Q","K","G","S","T","N","D"],abbreviated:["Jan","Shk","Mar","Pri","Maj","Qer","Kor","Gus","Sht","Tet","N\xEBn","Dhj"],wide:["Janar","Shkurt","Mars","Prill","Maj","Qershor","Korrik","Gusht","Shtator","Tetor","N\xEBntor","Dhjetor"]},v={narrow:["D","H","M","M","E","P","S"],short:["Di","H\xEB","Ma","M\xEB","En","Pr","Sh"],abbreviated:["Die","H\xEBn","Mar","M\xEBr","Enj","Pre","Sht"],wide:["Diel\xEB","H\xEBn\xEB","Mart\xEB","M\xEBrkur\xEB","Enjte","Premte","Shtun\xEB"]},P={narrow:{am:"p",pm:"m",midnight:"m",noon:"d",morning:"m\xEBngjes",afternoon:"dite",evening:"mbr\xEBmje",night:"nat\xEB"},abbreviated:{am:"PD",pm:"MD",midnight:"mesn\xEBt\xEB",noon:"drek",morning:"m\xEBngjes",afternoon:"mbasdite",evening:"mbr\xEBmje",night:"nat\xEB"},wide:{am:"p.d.",pm:"m.d.",midnight:"mesn\xEBt\xEB",noon:"drek",morning:"m\xEBngjes",afternoon:"mbasdite",evening:"mbr\xEBmje",night:"nat\xEB"}},k={narrow:{am:"p",pm:"m",midnight:"m",noon:"d",morning:"n\xEB m\xEBngjes",afternoon:"n\xEB mbasdite",evening:"n\xEB mbr\xEBmje",night:"n\xEB mesnat\xEB"},abbreviated:{am:"PD",pm:"MD",midnight:"mesnat\xEB",noon:"drek",morning:"n\xEB m\xEBngjes",afternoon:"n\xEB mbasdite",evening:"n\xEB mbr\xEBmje",night:"n\xEB mesnat\xEB"},wide:{am:"p.d.",pm:"m.d.",midnight:"mesnat\xEB",noon:"drek",morning:"n\xEB m\xEBngjes",afternoon:"n\xEB mbasdite",evening:"n\xEB mbr\xEBmje",night:"n\xEB mesnat\xEB"}},h=function C(G,H){var J=Number(G);if((H===null||H===void 0?void 0:H.unit)==="hour")return String(J);if(J===1)return J+"-r\xEB";if(J===4)return J+"t";return J+"-t\xEB"},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],B=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],A=Array.isArray(B)?c(B,function(K){return K.test(Z)}):m(B,function(K){return K.test(Z)}),T;T=C.valueCallback?C.valueCallback(A):A,T=H.valueCallback?H.valueCallback(T):T;var GC=G.slice(Z.length);return{value:T,rest:GC}}}function m(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function c(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function y(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var B=G.slice(X.length);return{value:Z,rest:B}}}var d=/^(\d+)(-rë|-të|t|)?/i,g=/\d+/i,p={narrow:/^(p|m)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(para krishtit|mbas krishtit)/i},u={any:[/^b/i,/^(p|m)/i]},l={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]-mujori (i{1,3}|iv)/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jsmpqkftnd]/i,abbreviated:/^(jan|shk|mar|pri|maj|qer|kor|gus|sht|tet|nën|dhj)/i,wide:/^(janar|shkurt|mars|prill|maj|qershor|korrik|gusht|shtator|tetor|nëntor|dhjetor)/i},s={narrow:[/^j/i,/^s/i,/^m/i,/^p/i,/^m/i,/^q/i,/^k/i,/^g/i,/^s/i,/^t/i,/^n/i,/^d/i],any:[/^ja/i,/^shk/i,/^mar/i,/^pri/i,/^maj/i,/^qer/i,/^kor/i,/^gu/i,/^sht/i,/^tet/i,/^n/i,/^d/i]},o={narrow:/^[dhmeps]/i,short:/^(di|hë|ma|më|en|pr|sh)/i,abbreviated:/^(die|hën|mar|mër|enj|pre|sht)/i,wide:/^(dielë|hënë|martë|mërkurë|enjte|premte|shtunë)/i},r={narrow:[/^d/i,/^h/i,/^m/i,/^m/i,/^e/i,/^p/i,/^s/i],any:[/^d/i,/^h/i,/^ma/i,/^më/i,/^e/i,/^p/i,/^s/i]},a={narrow:/^(p|m|me|në (mëngjes|mbasdite|mbrëmje|mesnatë))/i,any:/^([pm]\.?\s?d\.?|drek|në (mëngjes|mbasdite|mbrëmje|mesnatë))/i},e={any:{am:/^p/i,pm:/^m/i,midnight:/^me/i,noon:/^dr/i,morning:/mëngjes/i,afternoon:/mbasdite/i,evening:/mbrëmje/i,night:/natë/i}},t={ordinalNumber:y({matchPattern:d,parsePattern:g,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},CC={code:"sq",formatDistance:S,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{sq:CC})})})();

//# debugId=D48C8CA47786923564756E2164756E21
