(()=>{var A;function B(G){return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},B(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return B(H)=="symbol"?H:String(H)}function z(G,H){if(B(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(B(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"llai na eiliad",other:"llai na {{count}} eiliad"},xSeconds:{one:"1 eiliad",other:"{{count}} eiliad"},halfAMinute:"hanner munud",lessThanXMinutes:{one:"llai na munud",two:"llai na 2 funud",other:"llai na {{count}} munud"},xMinutes:{one:"1 munud",two:"2 funud",other:"{{count}} munud"},aboutXHours:{one:"tua 1 awr",other:"tua {{count}} awr"},xHours:{one:"1 awr",other:"{{count}} awr"},xDays:{one:"1 diwrnod",two:"2 ddiwrnod",other:"{{count}} diwrnod"},aboutXWeeks:{one:"tua 1 wythnos",two:"tua pythefnos",other:"tua {{count}} wythnos"},xWeeks:{one:"1 wythnos",two:"pythefnos",other:"{{count}} wythnos"},aboutXMonths:{one:"tua 1 mis",two:"tua 2 fis",other:"tua {{count}} mis"},xMonths:{one:"1 mis",two:"2 fis",other:"{{count}} mis"},aboutXYears:{one:"tua 1 flwyddyn",two:"tua 2 flynedd",other:"tua {{count}} mlynedd"},xYears:{one:"1 flwyddyn",two:"2 flynedd",other:"{{count}} mlynedd"},overXYears:{one:"dros 1 flwyddyn",two:"dros 2 flynedd",other:"dros {{count}} mlynedd"},almostXYears:{one:"bron 1 flwyddyn",two:"bron 2 flynedd",other:"bron {{count}} mlynedd"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else if(J===2&&!!Z.two)Y=Z.two;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"mewn "+Y;else return Y+" yn \xF4l";return Y};function $(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd/MM/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} 'am' {{time}}",long:"{{date}} 'am' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee 'diwethaf am' p",yesterday:"'ddoe am' p",today:"'heddiw am' p",tomorrow:"'yfory am' p",nextWeek:"eeee 'am' p",other:"P"},w=function G(H,J,X,Y){return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[C]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["C","O"],abbreviated:["CC","OC"],wide:["Cyn Crist","Ar \xF4l Crist"]},f={narrow:["1","2","3","4"],abbreviated:["Ch1","Ch2","Ch3","Ch4"],wide:["Chwarter 1af","2ail chwarter","3ydd chwarter","4ydd chwarter"]},F={narrow:["I","Ch","Ma","E","Mi","Me","G","A","Md","H","T","Rh"],abbreviated:["Ion","Chwe","Maw","Ebr","Mai","Meh","Gor","Aws","Med","Hyd","Tach","Rhag"],wide:["Ionawr","Chwefror","Mawrth","Ebrill","Mai","Mehefin","Gorffennaf","Awst","Medi","Hydref","Tachwedd","Rhagfyr"]},v={narrow:["S","Ll","M","M","I","G","S"],short:["Su","Ll","Ma","Me","Ia","Gw","Sa"],abbreviated:["Sul","Llun","Maw","Mer","Iau","Gwe","Sad"],wide:["dydd Sul","dydd Llun","dydd Mawrth","dydd Mercher","dydd Iau","dydd Gwener","dydd Sadwrn"]},P={narrow:{am:"b",pm:"h",midnight:"hn",noon:"hd",morning:"bore",afternoon:"prynhawn",evening:"gyda'r nos",night:"nos"},abbreviated:{am:"yb",pm:"yh",midnight:"hanner nos",noon:"hanner dydd",morning:"bore",afternoon:"prynhawn",evening:"gyda'r nos",night:"nos"},wide:{am:"y.b.",pm:"y.h.",midnight:"hanner nos",noon:"hanner dydd",morning:"bore",afternoon:"prynhawn",evening:"gyda'r nos",night:"nos"}},k={narrow:{am:"b",pm:"h",midnight:"hn",noon:"hd",morning:"yn y bore",afternoon:"yn y prynhawn",evening:"gyda'r nos",night:"yn y nos"},abbreviated:{am:"yb",pm:"yh",midnight:"hanner nos",noon:"hanner dydd",morning:"yn y bore",afternoon:"yn y prynhawn",evening:"gyda'r nos",night:"yn y nos"},wide:{am:"y.b.",pm:"y.h.",midnight:"hanner nos",noon:"hanner dydd",morning:"yn y bore",afternoon:"yn y prynhawn",evening:"gyda'r nos",night:"yn y nos"}},h=function G(H,J){var X=Number(H);if(X<20)switch(X){case 0:return X+"fed";case 1:return X+"af";case 2:return X+"ail";case 3:case 4:return X+"ydd";case 5:case 6:return X+"ed";case 7:case 8:case 9:case 10:case 12:case 15:case 18:return X+"fed";case 11:case 13:case 14:case 16:case 17:case 19:return X+"eg"}else if(X>=50&&X<=60||X===80||X>=100)return X+"fed";return X+"ain"},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var C=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(K){return K.test(C)}):y(T,function(K){return K.test(C)}),U;U=G.valueCallback?G.valueCallback(q):q,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(C.length);return{value:U,rest:HG}}}function y(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function m(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var C=G.valueCallback?G.valueCallback(Z[0]):Z[0];C=J.valueCallback?J.valueCallback(C):C;var T=H.slice(Y.length);return{value:C,rest:T}}}var p=/^(\d+)(af|ail|ydd|ed|fed|eg|ain)?/i,d=/\d+/i,g={narrow:/^(c|o)/i,abbreviated:/^(c\.?\s?c\.?|o\.?\s?c\.?)/i,wide:/^(cyn christ|ar ôl crist|ar ol crist)/i},l={wide:[/^c/i,/^(ar ôl crist|ar ol crist)/i],any:[/^c/i,/^o/i]},u={narrow:/^[1234]/i,abbreviated:/^ch[1234]/i,wide:/^(chwarter 1af)|([234](ail|ydd)? chwarter)/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(i|ch|m|e|g|a|h|t|rh)/i,abbreviated:/^(ion|chwe|maw|ebr|mai|meh|gor|aws|med|hyd|tach|rhag)/i,wide:/^(ionawr|chwefror|mawrth|ebrill|mai|mehefin|gorffennaf|awst|medi|hydref|tachwedd|rhagfyr)/i},s={narrow:[/^i/i,/^ch/i,/^m/i,/^e/i,/^m/i,/^m/i,/^g/i,/^a/i,/^m/i,/^h/i,/^t/i,/^rh/i],any:[/^io/i,/^ch/i,/^maw/i,/^e/i,/^mai/i,/^meh/i,/^g/i,/^a/i,/^med/i,/^h/i,/^t/i,/^rh/i]},o={narrow:/^(s|ll|m|i|g)/i,short:/^(su|ll|ma|me|ia|gw|sa)/i,abbreviated:/^(sul|llun|maw|mer|iau|gwe|sad)/i,wide:/^dydd (sul|llun|mawrth|mercher|iau|gwener|sadwrn)/i},r={narrow:[/^s/i,/^ll/i,/^m/i,/^m/i,/^i/i,/^g/i,/^s/i],wide:[/^dydd su/i,/^dydd ll/i,/^dydd ma/i,/^dydd me/i,/^dydd i/i,/^dydd g/i,/^dydd sa/i],any:[/^su/i,/^ll/i,/^ma/i,/^me/i,/^i/i,/^g/i,/^sa/i]},a={narrow:/^(b|h|hn|hd|(yn y|y|yr|gyda'r) (bore|prynhawn|nos|hwyr))/i,any:/^(y\.?\s?[bh]\.?|hanner nos|hanner dydd|(yn y|y|yr|gyda'r) (bore|prynhawn|nos|hwyr))/i},e={any:{am:/^b|(y\.?\s?b\.?)/i,pm:/^h|(y\.?\s?h\.?)|(yr hwyr)/i,midnight:/^hn|hanner nos/i,noon:/^hd|hanner dydd/i,morning:/bore/i,afternoon:/prynhawn/i,evening:/^gyda'r nos$/i,night:/blah/i}},t={ordinalNumber:m({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"cy",formatDistance:S,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{cy:GG})})})();

//# debugId=62524C766740C1E364756E2164756E21
