/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py

package com.facebook.yoga;

public enum YogaDisplay {
  FLEX(0),
  NONE(1),
  CONTENTS(2);

  private final int mIntValue;

  YogaDisplay(int intValue) {
    mIntValue = intValue;
  }

  public int intValue() {
    return mIntValue;
  }

  public static YogaDisplay fromInt(int value) {
    switch (value) {
      case 0: return FLEX;
      case 1: return NONE;
      case 2: return CONTENTS;
      default: throw new IllegalArgumentException("Unknown enum value: " + value);
    }
  }
}
