import type { Clerk<PERSON>untimeError } from '@clerk/shared/error';
import { ClerkWebAuthnError } from '@clerk/shared/error';
import type { CredentialReturn, PublicKeyCredentialCreationOptionsJSON, PublicKeyCredentialCreationOptionsWithoutExtensions, PublicKeyCredentialRequestOptionsJSON, PublicKeyCredentialRequestOptionsWithoutExtensions, PublicKeyCredentialWithAuthenticatorAssertionResponse, PublicKeyCredentialWithAuthenticatorAttestationResponse } from '@clerk/types';
type WebAuthnCreateCredentialReturn = CredentialReturn<PublicKeyCredentialWithAuthenticatorAttestationResponse>;
type WebAuthnGetCredentialReturn = CredentialReturn<PublicKeyCredentialWithAuthenticatorAssertionResponse>;
declare class Base64Converter {
    static encode(buffer: ArrayBuffer): string;
    static decode(base64url: string): ArrayBuffer;
}
declare function webAuthnCreateCredential(publicKeyOptions: PublicKeyCredentialCreationOptionsWithoutExtensions): Promise<WebAuthnCreateCredentialReturn>;
declare class WebAuthnAbortService {
    private controller;
    private __abort;
    createAbortSignal(): AbortSignal;
    abort(): void;
}
declare const __internal_WebAuthnAbortService: WebAuthnAbortService;
declare function webAuthnGetCredential({ publicKeyOptions, conditionalUI, }: {
    publicKeyOptions: PublicKeyCredentialRequestOptionsWithoutExtensions;
    conditionalUI: boolean;
}): Promise<WebAuthnGetCredentialReturn>;
/**
 * Map webauthn errors from `navigator.credentials.create()` to Clerk-js errors
 * @param error
 */
declare function handlePublicKeyCreateError(error: Error): ClerkWebAuthnError | ClerkRuntimeError | Error;
declare function convertJSONToPublicKeyCreateOptions(jsonPublicKey: PublicKeyCredentialCreationOptionsJSON): PublicKeyCredentialCreationOptionsWithoutExtensions;
declare function convertJSONToPublicKeyRequestOptions(jsonPublicKey: PublicKeyCredentialRequestOptionsJSON): PublicKeyCredentialRequestOptionsWithoutExtensions;
declare function serializePublicKeyCredential(pkc: PublicKeyCredentialWithAuthenticatorAttestationResponse): {
    response: {
        clientDataJSON: string;
        attestationObject: string;
        transports: string[];
    };
    type: string;
    id: string;
    rawId: string;
    authenticatorAttachment: string | null;
};
declare function serializePublicKeyCredentialAssertion(pkc: PublicKeyCredentialWithAuthenticatorAssertionResponse): {
    response: {
        clientDataJSON: string;
        authenticatorData: string;
        signature: string;
        userHandle: string | null;
    };
    type: string;
    id: string;
    rawId: string;
    authenticatorAttachment: string | null;
};
declare const bufferToBase64Url: typeof Base64Converter.encode;
declare const base64UrlToBuffer: typeof Base64Converter.decode;
export { base64UrlToBuffer, bufferToBase64Url, handlePublicKeyCreateError, webAuthnCreateCredential, webAuthnGetCredential, convertJSONToPublicKeyCreateOptions, convertJSONToPublicKeyRequestOptions, serializePublicKeyCredential, serializePublicKeyCredentialAssertion, __internal_WebAuthnAbortService, };
