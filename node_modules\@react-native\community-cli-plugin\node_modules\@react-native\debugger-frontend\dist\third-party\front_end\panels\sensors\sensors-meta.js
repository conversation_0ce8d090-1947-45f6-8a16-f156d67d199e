import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as o from"../../ui/legacy/legacy.js";const n={sensors:"Sensors",geolocation:"geolocation",timezones:"timezones",locale:"locale",locales:"locales",accelerometer:"accelerometer",deviceOrientation:"device orientation",locations:"Locations",touch:"Touch",devicebased:"Device-based",forceEnabled:"Force enabled",emulateIdleDetectorState:"Emulate Idle Detector state",noIdleEmulation:"No idle emulation",userActiveScreenUnlocked:"User active, screen unlocked",userActiveScreenLocked:"User active, screen locked",userIdleScreenUnlocked:"User idle, screen unlocked",userIdleScreenLocked:"User idle, screen locked",showSensors:"Show Sensors",showLocations:"Show Locations"},i=t.i18n.registerUIStrings("panels/sensors/sensors-meta.ts",n),l=t.i18n.getLazilyComputedLocalizedString.bind(void 0,i);let s;async function a(){return s||(s=await import("./sensors.js")),s}o.ViewManager.registerViewExtension({location:"drawer-view",commandPrompt:l(n.showSensors),title:l(n.sensors),id:"sensors",persistence:"closeable",order:100,loadView:async()=>new((await a()).SensorsView.SensorsView),tags:[l(n.geolocation),l(n.timezones),l(n.locale),l(n.locales),l(n.accelerometer),l(n.deviceOrientation)]}),o.ViewManager.registerViewExtension({location:"settings-view",id:"emulation-locations",commandPrompt:l(n.showLocations),title:l(n.locations),order:40,loadView:async()=>new((await a()).LocationsSettingsTab.LocationsSettingsTab),settings:["emulation.locations"],iconName:"location-on"}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"emulation.locations",settingType:"array",defaultValue:[{title:"Berlin",lat:52.520007,long:13.404954,timezoneId:"Europe/Berlin",locale:"de-DE"},{title:"London",lat:51.507351,long:-.127758,timezoneId:"Europe/London",locale:"en-GB"},{title:"Moscow",lat:55.755826,long:37.6173,timezoneId:"Europe/Moscow",locale:"ru-RU"},{title:"Mountain View",lat:37.386052,long:-122.083851,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Mumbai",lat:19.075984,long:72.877656,timezoneId:"Asia/Kolkata",locale:"mr-IN"},{title:"San Francisco",lat:37.774929,long:-122.419416,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Shanghai",lat:31.230416,long:121.473701,timezoneId:"Asia/Shanghai",locale:"zh-Hans-CN"},{title:"São Paulo",lat:-23.55052,long:-46.633309,timezoneId:"America/Sao_Paulo",locale:"pt-BR"},{title:"Tokyo",lat:35.689487,long:139.691706,timezoneId:"Asia/Tokyo",locale:"ja-JP"}]}),e.Settings.registerSettingExtension({title:l(n.touch),reloadRequired:!0,settingName:"emulation.touch",settingType:"enum",defaultValue:"none",options:[{value:"none",title:l(n.devicebased),text:l(n.devicebased)},{value:"force",title:l(n.forceEnabled),text:l(n.forceEnabled)}]}),e.Settings.registerSettingExtension({title:l(n.emulateIdleDetectorState),settingName:"emulation.idle-detection",settingType:"enum",defaultValue:"none",options:[{value:"none",title:l(n.noIdleEmulation),text:l(n.noIdleEmulation)},{value:'{"isUserActive":true,"isScreenUnlocked":true}',title:l(n.userActiveScreenUnlocked),text:l(n.userActiveScreenUnlocked)},{value:'{"isUserActive":true,"isScreenUnlocked":false}',title:l(n.userActiveScreenLocked),text:l(n.userActiveScreenLocked)},{value:'{"isUserActive":false,"isScreenUnlocked":true}',title:l(n.userIdleScreenUnlocked),text:l(n.userIdleScreenUnlocked)},{value:'{"isUserActive":false,"isScreenUnlocked":false}',title:l(n.userIdleScreenLocked),text:l(n.userIdleScreenLocked)}]});
