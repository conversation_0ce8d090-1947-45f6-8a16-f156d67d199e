/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateStateH.js
 */
#pragma once

#ifdef ANDROID
#include <folly/dynamic.h>
#endif

namespace facebook::react {

class ActivityIndicatorViewState {
public:
  ActivityIndicatorViewState() = default;

#ifdef ANDROID
  ActivityIndicatorViewState(ActivityIndicatorViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class AndroidDrawerLayoutState {
public:
  AndroidDrawerLayoutState() = default;

#ifdef ANDROID
  AndroidDrawerLayoutState(AndroidDrawerLayoutState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class AndroidSwipeRefreshLayoutState {
public:
  AndroidSwipeRefreshLayoutState() = default;

#ifdef ANDROID
  AndroidSwipeRefreshLayoutState(AndroidSwipeRefreshLayoutState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class DebuggingOverlayState {
public:
  DebuggingOverlayState() = default;

#ifdef ANDROID
  DebuggingOverlayState(DebuggingOverlayState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class PullToRefreshViewState {
public:
  PullToRefreshViewState() = default;

#ifdef ANDROID
  PullToRefreshViewState(PullToRefreshViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class SwitchState {
public:
  SwitchState() = default;

#ifdef ANDROID
  SwitchState(SwitchState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class UnimplementedNativeViewState {
public:
  UnimplementedNativeViewState() = default;

#ifdef ANDROID
  UnimplementedNativeViewState(UnimplementedNativeViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

} // namespace facebook::react