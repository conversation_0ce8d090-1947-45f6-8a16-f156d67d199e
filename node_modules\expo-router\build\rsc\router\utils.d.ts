/**
 * Copyright © 2024 650 Industries.
 * Copyright © 2024 2023 <PERSON><PERSON>
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * https://github.com/dai-shi/waku/blob/32d52242c1450b5f5965860e671ff73c42da8bd0/packages/waku/src/client.ts#L1
 */
export declare const filePathToFileURL: (filePath: string) => string;
export declare const encodeInput: (input: string) => string;
export declare const encodeActionId: (actionId: string) => string;
export declare const decodeActionId: (encoded: string) => string | null;
//# sourceMappingURL=utils.d.ts.map