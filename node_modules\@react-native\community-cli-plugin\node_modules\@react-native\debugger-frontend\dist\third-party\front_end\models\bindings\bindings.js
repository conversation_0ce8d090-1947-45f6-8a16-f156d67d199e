import*as e from"../../core/common/common.js";import*as t from"../../core/platform/platform.js";import{assertNotNullOrUndefined as o}from"../../core/platform/platform.js";import*as r from"../../core/sdk/sdk.js";import*as s from"../text_utils/text_utils.js";import*as i from"../workspace/workspace.js";import*as n from"../../core/i18n/i18n.js";const a={unknownErrorLoadingFile:"Unknown error loading file"},c=n.i18n.registerUIStrings("models/bindings/ContentProviderBasedProject.ts",a),u=n.i18n.getLocalizedString.bind(void 0,c);class l extends i.Workspace.ProjectStore{#e;#t;constructor(e,t,o,r,s){super(e,t,o,r),this.#e=s,this.#t=new WeakMap,e.addProject(this)}async requestFileContent(e){const{contentProvider:t}=this.#t.get(e);try{return await t.requestContentData()}catch(e){return{error:e?String(e):u(a.unknownErrorLoadingFile)}}}isServiceProject(){return this.#e}async requestMetadata(e){const{metadata:t}=this.#t.get(e);return t}canSetFileContent(){return!1}async setFileContent(e,t,o){}fullDisplayName(e){let t=e.parentURL().replace(/^(?:https?|file)\:\/\//,"");try{t=decodeURI(t)}catch(e){}return t+"/"+e.displayName(!0)}mimeType(e){const{mimeType:t}=this.#t.get(e);return t}canRename(){return!1}rename(e,t,o){const r=e.url();this.performRename(r,t,((t,r)=>{t&&r&&this.renameUISourceCode(e,r),o(t,r)}))}excludeFolder(e){}canExcludeFolder(e){return!1}async createFile(e,t,o,r){return null}canCreateFile(){return!1}deleteFile(e){}remove(){}performRename(e,t,o){o(!1)}searchInFileContent(e,t,o,r){const{contentProvider:s}=this.#t.get(e);return s.searchInContent(t,o,r)}async findFilesMatchingSearchRequest(e,o,r){const i=new Map;return r.setTotalWork(o.length),await Promise.all(o.map(async function(o){let n=!0,a=[];for(const r of e.queries().slice()){const i=await this.searchInFileContent(o,r,!e.ignoreCase(),e.isRegex());if(!i.length){n=!1;break}a=t.ArrayUtilities.mergeOrdered(a,i,s.ContentProvider.SearchMatch.comparator)}n&&i.set(o,a);r.incrementWorked(1)}.bind(this))),r.done(),i}indexContent(e){queueMicrotask(e.done.bind(e))}addUISourceCodeWithProvider(e,t,o,r){this.#t.set(e,{mimeType:r,metadata:o,contentProvider:t}),this.addUISourceCode(e)}addContentProvider(e,t,o){const r=this.createUISourceCode(e,t.contentType());return this.addUISourceCodeWithProvider(r,t,null,o),r}reset(){this.removeProject(),this.workspace().addProject(this)}dispose(){this.removeProject()}}var d=Object.freeze({__proto__:null,ContentProviderBasedProject:l});const g={removeFromIgnoreList:"Remove from ignore list",addScriptToIgnoreList:"Add script to ignore list",addDirectoryToIgnoreList:"Add directory to ignore list",addAllContentScriptsToIgnoreList:"Add all extension scripts to ignore list",addAllThirdPartyScriptsToIgnoreList:"Add all third-party scripts to ignore list"},p=n.i18n.registerUIStrings("models/bindings/IgnoreListManager.ts",g),h=n.i18n.getLocalizedString.bind(void 0,p);let m;class S{#o;#r;#s;constructor(t){this.#o=t,r.TargetManager.TargetManager.instance().addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.GlobalObjectCleared,this.clearCacheIfNeeded.bind(this),this),e.Settings.Settings.instance().moduleSetting("skip-stack-frames-pattern").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("skip-content-scripts").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("enable-ignore-listing").addChangeListener(this.patternChanged.bind(this)),this.#r=new Set,this.#s=new Map,r.TargetManager.TargetManager.instance().observeModels(r.DebuggerModel.DebuggerModel,this)}static instance(e={forceNew:null,debuggerWorkspaceBinding:null}){const{forceNew:t,debuggerWorkspaceBinding:o}=e;if(!m||t){if(!o)throw new Error(`Unable to create settings: debuggerWorkspaceBinding must be provided: ${(new Error).stack}`);m=new S(o)}return m}static removeInstance(){m=void 0}addChangeListener(e){this.#r.add(e)}removeChangeListener(e){this.#r.delete(e)}modelAdded(e){this.setIgnoreListPatterns(e);const t=e.sourceMapManager();t.addEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),t.addEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)}modelRemoved(e){this.clearCacheIfNeeded();const t=e.sourceMapManager();t.removeEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),t.removeEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)}clearCacheIfNeeded(){this.#s.size>1024&&this.#s.clear()}getSkipStackFramesPatternSetting(){return e.Settings.Settings.instance().moduleSetting("skip-stack-frames-pattern")}setIgnoreListPatterns(e){const t=this.enableIgnoreListing?this.getSkipStackFramesPatternSetting().getAsArray():[],o=[];for(const e of t)!e.disabled&&e.pattern&&o.push(e.pattern);return e.setBlackboxPatterns(o)}getGeneralRulesForUISourceCode(e){return{isContentScript:e.project().type()===i.Workspace.projectTypes.ContentScripts,isKnownThirdParty:e.isKnownThirdParty()}}isUserOrSourceMapIgnoreListedUISourceCode(e){if(e.isUnconditionallyIgnoreListed())return!0;const t=this.uiSourceCodeURL(e);return this.isUserIgnoreListedURL(t,this.getGeneralRulesForUISourceCode(e))}isUserIgnoreListedURL(e,t){if(!this.enableIgnoreListing)return!1;if(t?.isContentScript&&this.skipContentScripts)return!0;if(t?.isKnownThirdParty&&this.automaticallyIgnoreListKnownThirdPartyScripts)return!0;if(!e)return!1;if(this.#s.has(e))return Boolean(this.#s.get(e));const o=this.getSkipStackFramesPatternSetting().asRegExp(),r=o&&o.test(e)||!1;return this.#s.set(e,r),r}sourceMapAttached(e){const t=e.data.client,o=e.data.sourceMap;this.updateScriptRanges(t,o)}sourceMapDetached(e){const t=e.data.client;this.updateScriptRanges(t,void 0)}async updateScriptRanges(e,t){let o=!1;if(S.instance().isUserIgnoreListedURL(e.sourceURL,{isContentScript:e.isContentScript()})||(o=t?.sourceURLs().some((e=>this.isUserIgnoreListedURL(e,{isKnownThirdParty:t.hasIgnoreListHint(e)})))??!1),!o)return L.get(e)&&await e.setBlackboxedRanges([])&&L.delete(e),void await this.#o.updateLocations(e);if(!t)return;const r=t.findRanges((e=>this.isUserIgnoreListedURL(e,{isKnownThirdParty:t.hasIgnoreListHint(e)})),{isStartMatching:!0}).flatMap((e=>[e.start,e.end]));!function(e,t){if(e.length!==t.length)return!1;for(let o=0;o<e.length;++o)if(e[o].lineNumber!==t[o].lineNumber||e[o].columnNumber!==t[o].columnNumber)return!1;return!0}(L.get(e)||[],r)&&await e.setBlackboxedRanges(r)&&L.set(e,r),this.#o.updateLocations(e)}uiSourceCodeURL(e){return e.project().type()===i.Workspace.projectTypes.Debugger?null:e.url()}canIgnoreListUISourceCode(e){const t=this.uiSourceCodeURL(e);return!!t&&Boolean(this.urlToRegExpString(t))}ignoreListUISourceCode(e){const t=this.uiSourceCodeURL(e);t&&this.ignoreListURL(t)}unIgnoreListUISourceCode(e){this.unIgnoreListURL(this.uiSourceCodeURL(e),this.getGeneralRulesForUISourceCode(e))}get enableIgnoreListing(){return e.Settings.Settings.instance().moduleSetting("enable-ignore-listing").get()}set enableIgnoreListing(t){e.Settings.Settings.instance().moduleSetting("enable-ignore-listing").set(t)}get skipContentScripts(){return this.enableIgnoreListing&&e.Settings.Settings.instance().moduleSetting("skip-content-scripts").get()}get automaticallyIgnoreListKnownThirdPartyScripts(){return this.enableIgnoreListing&&e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").get()}ignoreListContentScripts(){this.enableIgnoreListing||(this.enableIgnoreListing=!0),e.Settings.Settings.instance().moduleSetting("skip-content-scripts").set(!0)}unIgnoreListContentScripts(){e.Settings.Settings.instance().moduleSetting("skip-content-scripts").set(!1)}ignoreListThirdParty(){this.enableIgnoreListing||(this.enableIgnoreListing=!0),e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").set(!0)}unIgnoreListThirdParty(){e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").set(!1)}ignoreListURL(e){const t=this.urlToRegExpString(e);t&&this.ignoreListRegex(t,e)}ignoreListRegex(e,t){const o=this.getSkipStackFramesPatternSetting().getAsArray();let r=!1;for(let s=0;s<o.length;++s){const i=o[s];(i.pattern===e||t&&i.disabledForUrl===t)&&(i.disabled=!1,i.disabledForUrl=void 0,r=!0)}r||o.push({pattern:e,disabled:!1}),this.enableIgnoreListing||(this.enableIgnoreListing=!0),this.getSkipStackFramesPatternSetting().setAsArray(o)}unIgnoreListURL(e,t){if(t?.isContentScript&&this.unIgnoreListContentScripts(),t?.isKnownThirdParty&&this.unIgnoreListThirdParty(),!e)return;let o=this.getSkipStackFramesPatternSetting().getAsArray();const r=S.instance().urlToRegExpString(e);if(r){o=o.filter((function(e){return e.pattern!==r}));for(let t=0;t<o.length;++t){const r=o[t];if(!r.disabled)try{new RegExp(r.pattern).test(e)&&(r.disabled=!0,r.disabledForUrl=e)}catch(e){}}this.getSkipStackFramesPatternSetting().setAsArray(o)}}removeIgnoreListPattern(e){let t=this.getSkipStackFramesPatternSetting().getAsArray();t=t.filter((function(t){return t.pattern!==e})),this.getSkipStackFramesPatternSetting().setAsArray(t)}ignoreListHasPattern(e,t){return this.getSkipStackFramesPatternSetting().getAsArray().some((o=>!(t&&o.disabled)&&o.pattern===e))}async patternChanged(){this.#s.clear();const e=[];for(const t of r.TargetManager.TargetManager.instance().models(r.DebuggerModel.DebuggerModel)){e.push(this.setIgnoreListPatterns(t));const o=t.sourceMapManager();for(const r of t.scripts())e.push(this.updateScriptRanges(r,o.sourceMapForClient(r)))}await Promise.all(e);const t=Array.from(this.#r);for(const e of t)e();this.patternChangeFinishedForTests()}patternChangeFinishedForTests(){}urlToRegExpString(o){const r=new e.ParsedURL.ParsedURL(o);if(r.isAboutBlank()||r.isDataURL())return"";if(!r.isValid)return"^"+t.StringUtilities.escapeForRegExp(o)+"$";let s=r.lastPathComponent;if(s?s="/"+s:r.folderPathComponents&&(s=r.folderPathComponents+"/"),s||(s=r.host),!s)return"";const i=r.scheme;let n="";return i&&"http"!==i&&"https"!==i&&(n="^"+i+"://","chrome-extension"===i&&(n+=r.host+"\\b"),n+=".*"),n+t.StringUtilities.escapeForRegExp(s)+(o.endsWith(s)?"$":"\\b")}getIgnoreListURLContextMenuItems(e){if(e.project().type()===i.Workspace.projectTypes.FileSystem)return[];const t=[],o=this.canIgnoreListUISourceCode(e),r=this.isUserOrSourceMapIgnoreListedUISourceCode(e),{isContentScript:s,isKnownThirdParty:n}=this.getGeneralRulesForUISourceCode(e);return r?(o||s||n)&&t.push({text:h(g.removeFromIgnoreList),callback:this.unIgnoreListUISourceCode.bind(this,e),jslogContext:"remove-script-from-ignorelist"}):(o&&t.push({text:h(g.addScriptToIgnoreList),callback:this.ignoreListUISourceCode.bind(this,e),jslogContext:"add-script-to-ignorelist"}),t.push(...this.getIgnoreListGeneralContextMenuItems({isContentScript:s,isKnownThirdParty:n}))),t}getIgnoreListGeneralContextMenuItems(e){const t=[];return e?.isContentScript&&t.push({text:h(g.addAllContentScriptsToIgnoreList),callback:this.ignoreListContentScripts.bind(this),jslogContext:"add-content-scripts-to-ignorelist"}),e?.isKnownThirdParty&&t.push({text:h(g.addAllThirdPartyScriptsToIgnoreList),callback:this.ignoreListThirdParty.bind(this),jslogContext:"add-3p-scripts-to-ignorelist"}),t}getIgnoreListFolderContextMenuItems(e,o){const r=[],s="^"+t.StringUtilities.escapeForRegExp(e)+"/";return this.ignoreListHasPattern(s,!0)?r.push({text:h(g.removeFromIgnoreList),callback:this.removeIgnoreListPattern.bind(this,s),jslogContext:"remove-from-ignore-list"}):this.isUserIgnoreListedURL(e,o)?r.push({text:h(g.removeFromIgnoreList),callback:this.unIgnoreListURL.bind(this,e,o),jslogContext:"remove-from-ignore-list"}):o?.isCurrentlyIgnoreListed||(r.push({text:h(g.addDirectoryToIgnoreList),callback:this.ignoreListRegex.bind(this,s),jslogContext:"add-directory-to-ignore-list"}),r.push(...this.getIgnoreListGeneralContextMenuItems(o))),r}}const L=new WeakMap;var M=Object.freeze({__proto__:null,IgnoreListManager:S});const f=new WeakMap,b=new WeakMap;let C;class v extends e.ObjectWrapper.ObjectWrapper{constructor(){super()}static instance({forceNew:e}={forceNew:!1}){return C&&!e||(C=new v),C}}class w{static resolveFrame(e,t){const o=w.targetForUISourceCode(e),s=o&&o.model(r.ResourceTreeModel.ResourceTreeModel);return s?s.frameForId(t):null}static setInitialFrameAttribution(e,t){if(!t)return;const o=w.resolveFrame(e,t);if(!o)return;const r=new Map;r.set(t,{frame:o,count:1}),f.set(e,r)}static cloneInitialFrameAttribution(e,t){const o=f.get(e);if(!o)return;const r=new Map;for(const e of o.keys()){const t=o.get(e);void 0!==t&&r.set(e,{frame:t.frame,count:t.count})}f.set(t,r)}static addFrameAttribution(e,t){const o=w.resolveFrame(e,t);if(!o)return;const r=f.get(e);if(!r)return;const s=r.get(t)||{frame:o,count:0};if(s.count+=1,r.set(t,s),1!==s.count)return;const i={uiSourceCode:e,frame:o};v.instance().dispatchEventToListeners("FrameAttributionAdded",i)}static removeFrameAttribution(e,t){const o=f.get(e);if(!o)return;const r=o.get(t);if(console.assert(Boolean(r),"Failed to remove frame attribution for url: "+e.url()),!r)return;if(r.count-=1,r.count>0)return;o.delete(t);const s={uiSourceCode:e,frame:r.frame};v.instance().dispatchEventToListeners("FrameAttributionRemoved",s)}static targetForUISourceCode(e){return b.get(e.project())||null}static setTargetForProject(e,t){b.set(e,t)}static getTargetForProject(e){return b.get(e)||null}static framesForUISourceCode(e){const t=w.targetForUISourceCode(e),o=t&&t.model(r.ResourceTreeModel.ResourceTreeModel),s=f.get(e);if(!o||!s)return[];return Array.from(s.keys()).map((e=>o.frameForId(e))).filter((e=>Boolean(e)))}}var I=Object.freeze({__proto__:null,NetworkProjectManager:v,NetworkProject:w});class T{#i;#o;#n=new Map;#a;#c;#u=new Map;#l=new Map;#d=new t.MapUtilities.Multimap;constructor(e,t,o){this.#i=e.sourceMapManager(),this.#o=o,this.#a=new l(t,"jsSourceMaps:stub:"+e.target().id(),i.Workspace.projectTypes.Service,"",!0),this.#c=[this.#i.addEventListener(r.SourceMapManager.Events.SourceMapWillAttach,this.sourceMapWillAttach,this),this.#i.addEventListener(r.SourceMapManager.Events.SourceMapFailedToAttach,this.sourceMapFailedToAttach,this),this.#i.addEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),this.#i.addEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)]}addStubUISourceCode(t){const o=this.#a.addContentProvider(e.ParsedURL.ParsedURL.concatenate(t.sourceURL,":sourcemap"),s.StaticContentProvider.StaticContentProvider.fromString(t.sourceURL,e.ResourceType.resourceTypes.Script,"\n\n\n\n\n// Please wait a bit.\n// Compiled script is not shown while source map is being loaded!"),"text/javascript");this.#n.set(t,o)}removeStubUISourceCode(e){const t=this.#n.get(e);this.#n.delete(e),t&&this.#a.removeUISourceCode(t.url())}getLocationRangesForSameSourceLocation(e){const t=e.debuggerModel,o=e.script();if(!o)return[];const r=this.#i.sourceMapForClient(o);if(!r)return[];const{lineNumber:s,columnNumber:i}=o.rawLocationToRelativeLocation(e),n=r.findEntry(s,i);if(!n||!n.sourceURL)return[];const a=this.#l.get(r);if(!a)return[];const c=a.uiSourceCodeForURL(n.sourceURL);if(!c)return[];if(!this.#d.hasValue(c,r))return[];return r.findReverseRanges(n.sourceURL,n.sourceLineNumber,n.sourceColumnNumber).map((({startLine:e,startColumn:r,endLine:s,endColumn:i})=>{const n=o.relativeLocationToRawLocation({lineNumber:e,columnNumber:r}),a=o.relativeLocationToRawLocation({lineNumber:s,columnNumber:i});return{start:t.createRawLocation(o,n.lineNumber,n.columnNumber),end:t.createRawLocation(o,a.lineNumber,a.columnNumber)}}))}uiSourceCodeForURL(e,t){const o=t?i.Workspace.projectTypes.ContentScripts:i.Workspace.projectTypes.Network;for(const t of this.#u.values()){if(t.type()!==o)continue;const r=t.uiSourceCodeForURL(e);if(r)return r}return null}rawLocationToUILocation(e){const t=e.script();if(!t)return null;const{lineNumber:o,columnNumber:r}=t.rawLocationToRelativeLocation(e),s=this.#n.get(t);if(s)return new i.UISourceCode.UILocation(s,o,r);const n=this.#i.sourceMapForClient(t);if(!n)return null;const a=this.#l.get(n);if(!a)return null;const c=n.findEntry(o,r,e.inlineFrameIndex);if(!c||!c.sourceURL)return null;const u=a.uiSourceCodeForURL(c.sourceURL);return u&&this.#d.hasValue(u,n)?u.uiLocation(c.sourceLineNumber,c.sourceColumnNumber):null}uiLocationToRawLocations(e,t,o){const r=[];for(const s of this.#d.get(e)){const i=s.sourceLineMapping(e.url(),t,o);if(!i)continue;const n=this.#i.clientForSourceMap(s);if(!n)continue;const a=n.relativeLocationToRawLocation(i);r.push(n.debuggerModel.createRawLocation(n,a.lineNumber,a.columnNumber))}return r}uiLocationRangeToRawLocationRanges(e,t){if(!this.#d.has(e))return null;const o=[];for(const r of this.#d.get(e)){const s=this.#i.clientForSourceMap(r);if(s)for(const i of r.reverseMapTextRanges(e.url(),t)){const e=s.relativeLocationToRawLocation(i.start),t=s.relativeLocationToRawLocation(i.end),r=s.debuggerModel.createRawLocation(s,e.lineNumber,e.columnNumber),n=s.debuggerModel.createRawLocation(s,t.lineNumber,t.columnNumber);o.push({start:r,end:n})}}return o}getMappedLines(e){if(!this.#d.has(e))return null;const t=new Set;for(const o of this.#d.get(e))for(const r of o.mappings())r.sourceURL===e.url()&&t.add(r.sourceLineNumber);return t}sourceMapWillAttach(e){const{client:t}=e.data;this.addStubUISourceCode(t),this.#o.updateLocations(t),S.instance().isUserIgnoreListedURL(t.sourceURL,{isContentScript:t.isContentScript()})&&this.#i.cancelAttachSourceMap(t)}sourceMapFailedToAttach(e){const{client:t}=e.data;this.removeStubUISourceCode(t),this.#o.updateLocations(t)}sourceMapAttached(t){const{client:o,sourceMap:n}=t.data,a=new Set([o]);this.removeStubUISourceCode(o);const c=o.target(),u=`jsSourceMaps:${o.isContentScript()?"extensions":""}:${c.id()}`;let d=this.#u.get(u);if(!d){const e=o.isContentScript()?i.Workspace.projectTypes.ContentScripts:i.Workspace.projectTypes.Network;d=new l(this.#a.workspace(),u,e,"",!1),w.setTargetForProject(d,c),this.#u.set(u,d)}this.#l.set(n,d);for(const t of n.sourceURLs()){const c=e.ResourceType.resourceTypes.SourceMapScript,u=d.createUISourceCode(t,c);n.hasIgnoreListHint(t)&&u.markKnownThirdParty();const l=n.embeddedContentByURL(t),g=null!==l?s.StaticContentProvider.StaticContentProvider.fromString(t,c,l):new r.CompilerSourceMappingContentProvider.CompilerSourceMappingContentProvider(t,c,o.createPageResourceLoadInitiator());let p=null;if(null!==l){const e=new TextEncoder;p=new i.UISourceCode.UISourceCodeMetadata(null,e.encode(l).length)}const h=e.ResourceType.ResourceType.mimeFromURL(t)??c.canonicalMimeType();this.#d.set(u,n),w.setInitialFrameAttribution(u,o.frameId);const m=d.uiSourceCodeForURL(t);if(null!==m){for(const e of this.#d.get(m)){this.#d.delete(m,e);const o=this.#i.clientForSourceMap(e);o&&(w.removeFrameAttribution(m,o.frameId),n.compatibleForURL(t,e)&&(this.#d.set(u,e),w.addFrameAttribution(u,o.frameId)),a.add(o))}d.removeUISourceCode(t)}d.addUISourceCodeWithProvider(u,g,p,h)}Promise.all([...a].map((e=>this.#o.updateLocations(e)))).then((()=>this.sourceMapAttachedForTest(n)))}sourceMapDetached(e){const{client:t,sourceMap:o}=e.data,r=this.#l.get(o);if(r){for(const e of r.uiSourceCodes())this.#d.delete(e,o)&&(w.removeFrameAttribution(e,t.frameId),this.#d.has(e)||r.removeUISourceCode(e.url()));this.#l.delete(o),this.#o.updateLocations(t)}}scriptsForUISourceCode(e){const t=[];for(const o of this.#d.get(e)){const e=this.#i.clientForSourceMap(o);e&&t.push(e)}return t}sourceMapAttachedForTest(e){}dispose(){e.EventTarget.removeEventListeners(this.#c);for(const e of this.#u.values())e.dispose();this.#a.dispose()}}var R=Object.freeze({__proto__:null,CompilerScriptMapping:T});class y{#g;#p;#h;constructor(e,t){this.#g=e,this.#p=t,this.#p.add(this),this.#h=null}async update(){this.#g&&(this.#h?await this.#h.then((()=>this.update())):(this.#h=this.#g(this),await this.#h,this.#h=null))}async uiLocation(){throw"Not implemented"}dispose(){this.#p.delete(this),this.#g=null}isDisposed(){return!this.#p.has(this)}async isIgnoreListed(){throw"Not implemented"}}class F{#m;constructor(){this.#m=new Set}add(e){this.#m.add(e)}delete(e){this.#m.delete(e)}has(e){return this.#m.has(e)}disposeAll(){for(const e of this.#m)e.dispose()}}var U=Object.freeze({__proto__:null,LiveLocationWithPool:y,LiveLocationPool:F});class P{#i;#S;#c;#L;constructor(e,t,o){this.#i=t,this.#S=new l(o,"cssSourceMaps:"+e.id(),i.Workspace.projectTypes.Network,"",!1),w.setTargetForProject(this.#S,e),this.#L=new Map,this.#c=[this.#i.addEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),this.#i.addEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)]}sourceMapAttachedForTest(e){}async sourceMapAttached(e){const t=e.data.client,o=e.data.sourceMap,r=this.#S,s=this.#L;for(const e of o.sourceURLs()){let i=s.get(e);i||(i=new j(r,e,t.createPageResourceLoadInitiator()),s.set(e,i)),i.addSourceMap(o,t.frameId)}await z.instance().updateLocations(t),this.sourceMapAttachedForTest(o)}async sourceMapDetached(e){const t=e.data.client,o=e.data.sourceMap,r=this.#L;for(const e of o.sourceURLs()){const s=r.get(e);s&&(s.removeSourceMap(o,t.frameId),s.getUiSourceCode()||r.delete(e))}await z.instance().updateLocations(t)}rawLocationToUILocation(e){const t=e.header();if(!t)return null;const o=this.#i.sourceMapForClient(t);if(!o)return null;let{lineNumber:r,columnNumber:s}=e;o.mapsOrigin()&&t.isInline&&(r-=t.startLine,0===r&&(s-=t.startColumn));const i=o.findEntry(r,s);if(!i||!i.sourceURL)return null;const n=this.#S.uiSourceCodeForURL(i.sourceURL);return n?n.uiLocation(i.sourceLineNumber,i.sourceColumnNumber):null}uiLocationToRawLocations(e){const{uiSourceCode:t,lineNumber:o,columnNumber:s=0}=e,i=k.get(t);if(!i)return[];const n=[];for(const e of i.getReferringSourceMaps()){const i=e.findReverseEntries(t.url(),o,s),a=this.#i.clientForSourceMap(e);a&&n.push(...i.map((e=>new r.CSSModel.CSSLocation(a,e.lineNumber,e.columnNumber))))}return n}static uiSourceOrigin(e){const t=k.get(e);return t?t.getReferringSourceMaps().map((e=>e.compiledURL())):[]}dispose(){e.EventTarget.removeEventListeners(this.#c),this.#S.dispose()}}const k=new WeakMap;class j{#S;#M;#f;referringSourceMaps;uiSourceCode;constructor(e,t,o){this.#S=e,this.#M=t,this.#f=o,this.referringSourceMaps=[],this.uiSourceCode=null}recreateUISourceCodeIfNeeded(t){const o=this.referringSourceMaps[this.referringSourceMaps.length-1],n=e.ResourceType.resourceTypes.SourceMapStyleSheet,a=o.embeddedContentByURL(this.#M),c=null!==a?s.StaticContentProvider.StaticContentProvider.fromString(this.#M,n,a):new r.CompilerSourceMappingContentProvider.CompilerSourceMappingContentProvider(this.#M,n,this.#f),u=this.#S.createUISourceCode(this.#M,n);k.set(u,this);const l=e.ResourceType.ResourceType.mimeFromURL(this.#M)||n.canonicalMimeType(),d="string"==typeof a?new i.UISourceCode.UISourceCodeMetadata(null,a.length):null;this.uiSourceCode?(w.cloneInitialFrameAttribution(this.uiSourceCode,u),this.#S.removeUISourceCode(this.uiSourceCode.url())):w.setInitialFrameAttribution(u,t),this.uiSourceCode=u,this.#S.addUISourceCodeWithProvider(this.uiSourceCode,c,d,l)}addSourceMap(e,t){this.uiSourceCode&&w.addFrameAttribution(this.uiSourceCode,t),this.referringSourceMaps.push(e),this.recreateUISourceCodeIfNeeded(t)}removeSourceMap(e,t){const o=this.uiSourceCode;w.removeFrameAttribution(o,t);const r=this.referringSourceMaps.lastIndexOf(e);-1!==r&&this.referringSourceMaps.splice(r,1),this.referringSourceMaps.length?this.recreateUISourceCodeIfNeeded(t):(this.#S.removeUISourceCode(o.url()),this.uiSourceCode=null)}getReferringSourceMaps(){return this.referringSourceMaps}getUiSourceCode(){return this.uiSourceCode}}var D=Object.freeze({__proto__:null,SASSSourceMapping:P});function N(e){for(const t of r.TargetManager.TargetManager.instance().models(r.ResourceTreeModel.ResourceTreeModel)){const o=t.resourceForURL(e);if(o)return o}return null}function E(e,t,o){const s=e.model(r.ResourceTreeModel.ResourceTreeModel);if(!s)return null;const i=s.frameForId(t);return i?x(i.resourceForURL(o)):null}function x(e){return!e||"number"!=typeof e.contentSize()&&!e.lastModified()?null:new i.UISourceCode.UISourceCodeMetadata(e.lastModified(),e.contentSize())}var A=Object.freeze({__proto__:null,resourceForURL:N,displayNameForURL:function(o){if(!o)return"";const s=N(o);if(s)return s.displayName;const n=i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(o);if(n)return n.displayName();const a=r.TargetManager.TargetManager.instance().inspectedURL();if(!a)return t.StringUtilities.trimURL(o,"");const c=e.ParsedURL.ParsedURL.fromString(a);if(!c)return o;const u=c.lastPathComponent,l=a.indexOf(u);if(-1!==l&&l+u.length===a.length){const e=a.substring(0,l);if(o.startsWith(e)&&o.length>l)return o.substring(l)}const d=t.StringUtilities.trimURL(o,c.host);return"/"===d?c.host+"/":d},metadataForURL:E,resourceMetadata:x});const O=new WeakMap;class W{#b;#S;#C;#c;constructor(e,t){this.#b=e;const o=this.#b.target();this.#S=new l(t,"css:"+o.id(),i.Workspace.projectTypes.Network,"",!1),w.setTargetForProject(this.#S,o),this.#C=new Map,this.#c=[this.#b.addEventListener(r.CSSModel.Events.StyleSheetAdded,this.styleSheetAdded,this),this.#b.addEventListener(r.CSSModel.Events.StyleSheetRemoved,this.styleSheetRemoved,this),this.#b.addEventListener(r.CSSModel.Events.StyleSheetChanged,this.styleSheetChanged,this)]}addSourceMap(e,t){this.#C.get(e)?.addSourceMap(e,t)}rawLocationToUILocation(e){const t=e.header();if(!t||!this.acceptsHeader(t))return null;const o=this.#C.get(t.resourceURL());if(!o)return null;let r=e.lineNumber,s=e.columnNumber;if(t.isInline&&t.hasSourceURL){r-=t.lineNumberInSource(0);const e=t.columnNumberInSource(r,0);void 0===e?s=e:s-=e}return o.getUiSourceCode().uiLocation(r,s)}uiLocationToRawLocations(e){const t=O.get(e.uiSourceCode);if(!t)return[];const o=[];for(const s of t.getHeaders()){let t=e.lineNumber,i=e.columnNumber;s.isInline&&s.hasSourceURL&&(i=s.columnNumberInSource(t,e.columnNumber||0),t=s.lineNumberInSource(t)),o.push(new r.CSSModel.CSSLocation(s,t,i))}return o}acceptsHeader(e){return!e.isConstructedByNew()&&(!(e.isInline&&!e.hasSourceURL&&"inspector"!==e.origin)&&!!e.resourceURL())}styleSheetAdded(e){const t=e.data;if(!this.acceptsHeader(t))return;const o=t.resourceURL();let r=this.#C.get(o);r?r.addHeader(t):(r=new B(this.#b,this.#S,t),this.#C.set(o,r))}styleSheetRemoved(e){const t=e.data;if(!this.acceptsHeader(t))return;const o=t.resourceURL(),r=this.#C.get(o);r&&(1===r.getHeaders().size?(r.dispose(),this.#C.delete(o)):r.removeHeader(t))}styleSheetChanged(e){const t=this.#b.styleSheetHeaderForId(e.data.styleSheetId);if(!t||!this.acceptsHeader(t))return;const o=this.#C.get(t.resourceURL());o&&o.styleSheetChanged(t)}dispose(){for(const e of this.#C.values())e.dispose();this.#C.clear(),e.EventTarget.removeEventListeners(this.#c),this.#S.removeProject()}}class B{#b;#S;headers;uiSourceCode;#c;#v;#w;#I;#T;constructor(t,o,r){this.#b=t,this.#S=o,this.headers=new Set([r]);const s=t.target(),n=r.resourceURL(),a=E(s,r.frameId,n);this.uiSourceCode=this.#S.createUISourceCode(n,r.contentType()),O.set(this.uiSourceCode,this),w.setInitialFrameAttribution(this.uiSourceCode,r.frameId),this.#S.addUISourceCodeWithProvider(this.uiSourceCode,this,a,"text/css"),this.#c=[this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this)],this.#v=new e.Throttler.Throttler(B.updateTimeout),this.#w=!1}addHeader(e){this.headers.add(e),w.addFrameAttribution(this.uiSourceCode,e.frameId)}removeHeader(e){this.headers.delete(e),w.removeFrameAttribution(this.uiSourceCode,e.frameId)}styleSheetChanged(e){if(console.assert(this.headers.has(e)),this.#T||!this.headers.has(e))return;const t=this.mirrorContent.bind(this,e,!0);this.#v.schedule(t,"Default")}workingCopyCommitted(){if(this.#I)return;const e=this.mirrorContent.bind(this,this.uiSourceCode,!0);this.#v.schedule(e,"AsSoonAsPossible")}workingCopyChanged(){if(this.#I)return;const e=this.mirrorContent.bind(this,this.uiSourceCode,!1);this.#v.schedule(e,"Default")}async mirrorContent(e,t){if(this.#w)return void this.styleFileSyncedForTest();let o=null;if(o=e===this.uiSourceCode?this.uiSourceCode.workingCopy():s.ContentData.ContentData.textOr(await e.requestContentData(),null),null===o||this.#w)return void this.styleFileSyncedForTest();e!==this.uiSourceCode&&(this.#I=!0,this.uiSourceCode.addRevision(o),this.#I=!1),this.#T=!0;const r=[];for(const s of this.headers)s!==e&&r.push(this.#b.setStyleSheetText(s.id,o,t));await Promise.all(r),this.#T=!1,this.styleFileSyncedForTest()}styleFileSyncedForTest(){}dispose(){this.#w||(this.#w=!0,this.#S.removeUISourceCode(this.uiSourceCode.url()),e.EventTarget.removeEventListeners(this.#c))}contentURL(){return console.assert(this.headers.size>0),this.headers.values().next().value.originalContentProvider().contentURL()}contentType(){return console.assert(this.headers.size>0),this.headers.values().next().value.originalContentProvider().contentType()}requestContent(){return console.assert(this.headers.size>0),this.headers.values().next().value.originalContentProvider().requestContent()}requestContentData(){return console.assert(this.headers.size>0),this.headers.values().next().value.originalContentProvider().requestContentData()}searchInContent(e,t,o){return console.assert(this.headers.size>0),this.headers.values().next().value.originalContentProvider().searchInContent(e,t,o)}static updateTimeout=200;getHeaders(){return this.headers}getUiSourceCode(){return this.uiSourceCode}addSourceMap(e,t){const o=this.#b.sourceMapManager();this.headers.forEach((r=>{o.detachSourceMap(r),o.attachSourceMap(r,e,t)}))}}var H=Object.freeze({__proto__:null,StylesSourceMapping:W,StyleFile:B});let _;class z{#R;#y;#F;constructor(e,t){this.#R=e,this.#y=new Map,t.observeModels(r.CSSModel.CSSModel,this),this.#F=new Set}static instance(e={forceNew:null,resourceMapping:null,targetManager:null}){const{forceNew:t,resourceMapping:o,targetManager:r}=e;if(!_||t){if(!o||!r)throw new Error(`Unable to create CSSWorkspaceBinding: resourceMapping and targetManager must be provided: ${(new Error).stack}`);_=new z(o,r)}return _}static removeInstance(){_=void 0}get modelToInfo(){return this.#y}getCSSModelInfo(e){return this.#y.get(e)}modelAdded(e){this.#y.set(e,new V(e,this.#R))}modelRemoved(e){this.getCSSModelInfo(e).dispose(),this.#y.delete(e)}async pendingLiveLocationChangesPromise(){await Promise.all(this.#F)}recordLiveLocationChange(e){e.then((()=>{this.#F.delete(e)})),this.#F.add(e)}async updateLocations(e){const t=this.getCSSModelInfo(e.cssModel()).updateLocations(e);this.recordLiveLocationChange(t),await t}createLiveLocation(e,t,o){const r=this.getCSSModelInfo(e.cssModel()).createLiveLocation(e,t,o);return this.recordLiveLocationChange(r),r}propertyRawLocation(e,t){const o=e.ownerStyle;if(!o||o.type!==r.CSSStyleDeclaration.Type.Regular||!o.styleSheetId)return null;const s=o.cssModel().styleSheetHeaderForId(o.styleSheetId);if(!s)return null;const i=t?e.nameRange():e.valueRange();if(!i)return null;const n=i.startLine,a=i.startColumn;return new r.CSSModel.CSSLocation(s,s.lineNumberInSource(n),s.columnNumberInSource(n,a))}propertyUILocation(e,t){const o=this.propertyRawLocation(e,t);return o?this.rawLocationToUILocation(o):null}rawLocationToUILocation(e){return this.getCSSModelInfo(e.cssModel()).rawLocationToUILocation(e)}uiLocationToRawLocations(e){const t=[];for(const o of this.#y.values())t.push(...o.uiLocationToRawLocations(e));return t}}class V{#c;#R;#U;#P;#m;#k;constructor(e,o){this.#c=[e.addEventListener(r.CSSModel.Events.StyleSheetAdded,(e=>{this.styleSheetAdded(e)}),this),e.addEventListener(r.CSSModel.Events.StyleSheetRemoved,(e=>{this.styleSheetRemoved(e)}),this)],this.#R=o,this.#U=new W(e,o.workspace);const s=e.sourceMapManager();this.#P=new P(e.target(),s,o.workspace),this.#m=new t.MapUtilities.Multimap,this.#k=new t.MapUtilities.Multimap}get locations(){return this.#m}async createLiveLocation(e,t,o){const r=new q(e,this,t,o),s=e.header();return s?(r.setHeader(s),this.#m.set(s,r),await r.update()):this.#k.set(e.url,r),r}disposeLocation(e){const t=e.header();t?this.#m.delete(t,e):this.#k.delete(e.url,e)}updateLocations(e){const t=[];for(const o of this.#m.get(e))t.push(o.update());return Promise.all(t)}async styleSheetAdded(e){const t=e.data;if(!t.sourceURL)return;const o=[];for(const e of this.#k.get(t.sourceURL))e.setHeader(t),this.#m.set(t,e),o.push(e.update());await Promise.all(o),this.#k.deleteAll(t.sourceURL)}async styleSheetRemoved(e){const t=e.data,o=[];for(const e of this.#m.get(t))e.setHeader(t),this.#k.set(e.url,e),o.push(e.update());await Promise.all(o),this.#m.deleteAll(t)}addSourceMap(e,t){this.#U.addSourceMap(e,t)}rawLocationToUILocation(e){let t=null;return t=t||this.#P.rawLocationToUILocation(e),t=t||this.#U.rawLocationToUILocation(e),t=t||this.#R.cssLocationToUILocation(e),t}uiLocationToRawLocations(e){let t=this.#P.uiLocationToRawLocations(e);return t.length?t:(t=this.#U.uiLocationToRawLocations(e),t.length?t:this.#R.uiLocationToCSSLocations(e))}dispose(){e.EventTarget.removeEventListeners(this.#c),this.#U.dispose(),this.#P.dispose()}}class q extends y{url;#j;#D;#N;headerInternal;constructor(e,t,o,r){super(o,r),this.url=e.url,this.#j=e.lineNumber,this.#D=e.columnNumber,this.#N=t,this.headerInternal=null}header(){return this.headerInternal}setHeader(e){this.headerInternal=e}async uiLocation(){if(!this.headerInternal)return null;const e=new r.CSSModel.CSSLocation(this.headerInternal,this.#j,this.#D);return z.instance().rawLocationToUILocation(e)}dispose(){super.dispose(),this.#N.disposeLocation(this)}async isIgnoreListed(){return!1}}var G=Object.freeze({__proto__:null,CSSWorkspaceBinding:z,ModelInfo:V,LiveLocation:q});const K={errorInDebuggerLanguagePlugin:"Error in debugger language plugin: {PH1}",loadingDebugSymbolsForVia:"[{PH1}] Loading debug symbols for {PH2} (via {PH3})...",loadingDebugSymbolsFor:"[{PH1}] Loading debug symbols for {PH2}...",loadedDebugSymbolsForButDidnt:"[{PH1}] Loaded debug symbols for {PH2}, but didn't find any source files",loadedDebugSymbolsForFound:"[{PH1}] Loaded debug symbols for {PH2}, found {PH3} source file(s)",failedToLoadDebugSymbolsFor:"[{PH1}] Failed to load debug symbols for {PH2} ({PH3})",failedToLoadDebugSymbolsForFunction:'No debug information for function "{PH1}"',debugSymbolsIncomplete:"The debug information for function {PH1} is incomplete"},$=n.i18n.registerUIStrings("models/bindings/DebuggerLanguagePlugins.ts",K),J=n.i18n.getLocalizedString.bind(void 0,$);function X(e){return`${e.sourceURL}@${e.hash}`}function Q(e){const{script:t}=e;return{rawModuleId:X(t),codeOffset:e.location().columnNumber-(t.codeOffset()||0),inlineFrameIndex:e.inlineFrameIndex}}class Y extends Error{exception;exceptionDetails;constructor(e,t){const{description:o}=t.exception||{};super(o||t.text),this.exception=e,this.exceptionDetails=t}static makeLocal(e,t){const o={type:"object",subtype:"error",description:t},r={text:"Uncaught",exceptionId:-1,columnNumber:0,lineNumber:0,exception:o},s=e.debuggerModel.runtimeModel().createRemoteObject(o);return new Y(s,r)}}class Z extends r.RemoteObject.LocalJSONObject{constructor(e){super(e)}get description(){return this.type}get type(){return"namespace"}}async function ee(e,t,o){if("reftype"===t.type){const o=await async function(e,t){if(!/^(local|global|operand)$/.test(t.valueClass))return{type:"undefined"};const o=Number(t.index),r=`${t.valueClass}s[${o}]`,s=await e.debuggerModel.agent.invoke_evaluateOnCallFrame({callFrameId:e.id,expression:r,silent:!0,generatePreview:!0,throwOnSideEffect:!0});return s.getError()||s.exceptionDetails?{type:"undefined"}:s.result}(e,t);return e.debuggerModel.runtimeModel().createRemoteObject(o)}return new re(e,t,o)}class te extends r.RemoteObject.RemoteObjectImpl{variables;#E;#x;stopId;constructor(e,t,o){super(e.debuggerModel.runtimeModel(),void 0,"object",void 0,null),this.variables=[],this.#E=e,this.#x=o,this.stopId=t}async doGetProperties(e,t,o){if(t)return{properties:[],internalProperties:[]};const s=[],i={};function n(e,t){return new r.RemoteObject.RemoteObjectProperty(e,t,!1,!1,!0,!1)}for(const e of this.variables){let t;try{const o=await this.#x.evaluate(e.name,Q(this.#E),this.stopId);t=o?await ee(this.#E,o,this.#x):new r.RemoteObject.LocalJSONObject(void 0)}catch(e){console.warn(e),t=new r.RemoteObject.LocalJSONObject(void 0)}if(e.nestedName&&e.nestedName.length>1){let o=i;for(let t=0;t<e.nestedName.length-1;t++){const r=e.nestedName[t];let s=o[r];s||(s=new Z({}),o[r]=s),o=s.value}o[e.nestedName[e.nestedName.length-1]]=t}else s.push(n(e.name,t))}for(const e in i)s.push(n(e,i[e]));return{properties:s,internalProperties:[]}}}class oe{#A;#O;#W;#B;#H;constructor(e,t,o,r,s,i){if(s&&"data:"!==new URL(s).protocol)throw new Error("The icon must be a data:-URL");this.#A=e,this.#O=o,this.#W=r,this.#B=s,this.#H=new te(e,t,i)}async getVariableValue(e){for(let t=0;t<this.#H.variables.length;++t){if(this.#H.variables[t].name!==e)continue;const o=await this.#H.getAllProperties(!1,!1);if(!o.properties)continue;const{value:r}=o.properties[t];if(r)return r}return null}callFrame(){return this.#A}type(){return this.#O}typeName(){return this.#W}name(){}range(){return null}object(){return this.#H}description(){return""}icon(){return this.#B}extraProperties(){return[]}}class re extends r.RemoteObject.RemoteObject{extensionObject;plugin;callFrame;constructor(e,t,o){super(),this.extensionObject=t,this.plugin=o,this.callFrame=e}get linearMemoryAddress(){return this.extensionObject.linearMemoryAddress}get linearMemorySize(){return this.extensionObject.linearMemorySize}get objectId(){return this.extensionObject.objectId}get type(){return"array"===this.extensionObject.type||"null"===this.extensionObject.type?"object":this.extensionObject.type}get subtype(){if("array"===this.extensionObject.type||"null"===this.extensionObject.type)return this.extensionObject.type}get value(){return this.extensionObject.value}unserializableValue(){}get description(){return this.extensionObject.description}set description(e){}get hasChildren(){return this.extensionObject.hasChildren}get preview(){}get className(){return this.extensionObject.className??null}arrayLength(){return 0}arrayBufferByteLength(){return 0}getOwnProperties(e,t){return this.getAllProperties(!1,e,t)}async getAllProperties(e,t,s){const{objectId:i}=this.extensionObject;if(i){o(this.plugin.getProperties);const e=await this.plugin.getProperties(i);return{properties:await Promise.all(e.map((async e=>new r.RemoteObject.RemoteObjectProperty(e.name,await ee(this.callFrame,e.value,this.plugin))))),internalProperties:null}}return{properties:null,internalProperties:null}}release(){const{objectId:e}=this.extensionObject;e&&(o(this.plugin.releaseObject),this.plugin.releaseObject(e))}debuggerModel(){return this.callFrame.debuggerModel}runtimeModel(){return this.callFrame.debuggerModel.runtimeModel()}isLinearMemoryInspectable(){return void 0!==this.extensionObject.linearMemoryAddress}}class se{#_;#o;#z;#V;#q;callFrameByStopId=new Map;stopIdByCallFrame=new Map;nextStopId=0n;constructor(e,t,o){this.#_=t,this.#o=o,this.#z=[],this.#V=new Map,e.observeModels(r.DebuggerModel.DebuggerModel,this),this.#q=new Map}async evaluateOnCallFrame(e,t){const{script:o}=e,{expression:s,returnByValue:i,throwOnSideEffect:n}=t,{plugin:a}=await this.rawModuleIdAndPluginForScript(o);if(!a)return null;const c=Q(e);if(0===(await a.rawLocationToSourceLocation(c)).length)return null;if(i)return{error:"Cannot return by value"};if(n)return{error:"Cannot guarantee side-effect freedom"};try{const t=await a.evaluate(s,c,this.stopIdForCallFrame(e));return t?{object:await ee(e,t,a),exceptionDetails:void 0}:{object:new r.RemoteObject.LocalJSONObject(void 0),exceptionDetails:void 0}}catch(t){if(t instanceof Y){const{exception:e,exceptionDetails:o}=t;return{object:e,exceptionDetails:o}}const{exception:o,exceptionDetails:r}=Y.makeLocal(e,t.message);return{object:o,exceptionDetails:r}}}stopIdForCallFrame(e){let t=this.stopIdByCallFrame.get(e);return void 0!==t||(t=this.nextStopId++,this.stopIdByCallFrame.set(e,t),this.callFrameByStopId.set(t,e)),t}callFrameForStopId(e){return this.callFrameByStopId.get(e)}expandCallFrames(e){return Promise.all(e.map((async e=>{const t=await this.getFunctionInfo(e.script,e.location());if(t){if("frames"in t&&t.frames.length)return t.frames.map((({name:t},o)=>e.createVirtualCallFrame(o,t)));if("missingSymbolFiles"in t&&t.missingSymbolFiles.length){const o=t.missingSymbolFiles,r=J(K.debugSymbolsIncomplete,{PH1:e.functionName});e.missingDebugInfoDetails={details:r,resources:o}}else e.missingDebugInfoDetails={details:J(K.failedToLoadDebugSymbolsForFunction,{PH1:e.functionName}),resources:[]}}return e}))).then((e=>e.flat()))}modelAdded(e){this.#V.set(e,new ie(e,this.#_)),e.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),e.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,this.parsedScriptSource,this),e.addEventListener(r.DebuggerModel.Events.DebuggerResumed,this.debuggerResumed,this),e.setEvaluateOnCallFrameCallback(this.evaluateOnCallFrame.bind(this)),e.setExpandCallFramesCallback(this.expandCallFrames.bind(this))}modelRemoved(t){t.removeEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),t.removeEventListener(r.DebuggerModel.Events.ParsedScriptSource,this.parsedScriptSource,this),t.removeEventListener(r.DebuggerModel.Events.DebuggerResumed,this.debuggerResumed,this),t.setEvaluateOnCallFrameCallback(null),t.setExpandCallFramesCallback(null);const o=this.#V.get(t);o&&(o.dispose(),this.#V.delete(t)),this.#q.forEach(((o,r)=>{const s=o.scripts.filter((e=>e.debuggerModel!==t));0===s.length?(o.plugin.removeRawModule(r).catch((t=>{e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message}))})),this.#q.delete(r)):o.scripts=s}))}globalObjectCleared(e){const t=e.data;this.modelRemoved(t),this.modelAdded(t)}addPlugin(e){this.#z.push(e);for(const e of this.#V.keys())for(const t of e.scripts())this.hasPluginForScript(t)||this.parsedScriptSource({data:t})}removePlugin(e){this.#z=this.#z.filter((t=>t!==e));const t=new Set;this.#q.forEach(((o,r)=>{o.plugin===e&&(o.scripts.forEach((e=>t.add(e))),this.#q.delete(r))}));for(const e of t){this.#V.get(e.debuggerModel).removeScript(e),this.parsedScriptSource({data:e})}}hasPluginForScript(e){const t=X(e),o=this.#q.get(t);return void 0!==o&&o.scripts.includes(e)}async rawModuleIdAndPluginForScript(e){const t=X(e),o=this.#q.get(t);return o&&(await o.addRawModulePromise,o===this.#q.get(t))?{rawModuleId:t,plugin:o.plugin}:{rawModuleId:t,plugin:null}}uiSourceCodeForURL(e,t){const o=this.#V.get(e);return o?o.getProject().uiSourceCodeForURL(t):null}async rawLocationToUILocation(t){const o=t.script();if(!o)return null;const{rawModuleId:r,plugin:s}=await this.rawModuleIdAndPluginForScript(o);if(!s)return null;const i={rawModuleId:r,codeOffset:t.columnNumber-(o.codeOffset()||0),inlineFrameIndex:t.inlineFrameIndex};try{const e=await s.rawLocationToSourceLocation(i);for(const t of e){const e=this.uiSourceCodeForURL(o.debuggerModel,t.sourceFileURL);if(e)return e.uiLocation(t.lineNumber,t.columnNumber>=0?t.columnNumber:void 0)}}catch(t){e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message}))}return null}uiLocationToRawLocationRanges(t,o,s=-1){const i=[];return this.scriptsForUISourceCode(t).forEach((e=>{const n=X(e),a=this.#q.get(n);if(!a)return;const{plugin:c}=a;i.push(async function(e,i,n){const a={rawModuleId:e,sourceFileURL:t.url(),lineNumber:o,columnNumber:s},c=await i.sourceLocationToRawLocation(a);if(!c)return[];return c.map((e=>({start:new r.DebuggerModel.Location(n.debuggerModel,n.scriptId,0,Number(e.startOffset)+(n.codeOffset()||0)),end:new r.DebuggerModel.Location(n.debuggerModel,n.scriptId,0,Number(e.endOffset)+(n.codeOffset()||0))})))}(n,c,e))})),0===i.length?Promise.resolve(null):Promise.all(i).then((e=>e.flat())).catch((t=>(e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),null)))}async uiLocationToRawLocations(e,t,o){const r=await this.uiLocationToRawLocationRanges(e,t,o);return r?r.map((({start:e})=>e)):null}async uiLocationRangeToRawLocationRanges(e,t){const o=[];for(let r=t.startLine;r<=t.endLine;++r)o.push(this.uiLocationToRawLocationRanges(e,r));const r=[];for(const e of await Promise.all(o)){if(null===e)return null;for(const o of e){const[e,i]=await Promise.all([this.rawLocationToUILocation(o.start),this.rawLocationToUILocation(o.end)]);if(null===e||null===i)continue;t.intersection(new s.TextRange.TextRange(e.lineNumber,e.columnNumber??0,i.lineNumber,i.columnNumber??1/0)).isEmpty()||r.push(o)}}return r}scriptsForUISourceCode(e){for(const t of this.#V.values()){const o=t.uiSourceCodeToScripts.get(e);if(o)return o}return[]}setDebugInfoURL(e,t){this.hasPluginForScript(e)||(e.debugSymbols={type:"ExternalDWARF",externalURL:t},this.parsedScriptSource({data:e}),e.debuggerModel.setDebugInfoURL(e,t))}parsedScriptSource(t){const o=t.data;if(o.sourceURL)for(const t of this.#z){if(!t.handleScript(o))continue;const r=X(o);let s=this.#q.get(r);if(s)s.scripts.push(o);else{const i=(async()=>{const i=e.Console.Console.instance(),n=o.sourceURL,a=o.debugSymbols&&o.debugSymbols.externalURL||"";a?i.log(J(K.loadingDebugSymbolsForVia,{PH1:t.name,PH2:n,PH3:a})):i.log(J(K.loadingDebugSymbolsFor,{PH1:t.name,PH2:n}));try{const c=!a&&e.ParsedURL.schemeIs(n,"wasm:")?await o.getWasmBytecode():void 0,u=await t.addRawModule(r,a,{url:n,code:c});if(s!==this.#q.get(r))return[];if("missingSymbolFiles"in u){const e=t.createPageResourceLoadInitiator();return{missingSymbolFiles:u.missingSymbolFiles.map((t=>({resourceUrl:t,initiator:e})))}}const l=u;return 0===l.length?i.warn(J(K.loadedDebugSymbolsForButDidnt,{PH1:t.name,PH2:n})):i.log(J(K.loadedDebugSymbolsForFound,{PH1:t.name,PH2:n,PH3:l.length})),l}catch(e){return i.error(J(K.failedToLoadDebugSymbolsFor,{PH1:t.name,PH2:n,PH3:e.message})),this.#q.delete(r),[]}})();s={rawModuleId:r,plugin:t,scripts:[o],addRawModulePromise:i},this.#q.set(r,s)}return void s.addRawModulePromise.then((e=>{if(!("missingSymbolFiles"in e)&&o.debuggerModel.scriptForId(o.scriptId)===o){const t=this.#V.get(o.debuggerModel);t&&(t.addSourceFiles(o,e),this.#o.updateLocations(o))}}))}}debuggerResumed(e){const t=Array.from(this.callFrameByStopId.values()).filter((t=>t.debuggerModel===e.data));for(const e of t){const t=this.stopIdByCallFrame.get(e);o(t),this.stopIdByCallFrame.delete(e),this.callFrameByStopId.delete(t)}}getSourcesForScript(e){const t=X(e),o=this.#q.get(t);return o?o.addRawModulePromise:Promise.resolve(void 0)}async resolveScopeChain(t){const o=t.script,{rawModuleId:r,plugin:s}=await this.rawModuleIdAndPluginForScript(o);if(!s)return null;const i={rawModuleId:r,codeOffset:t.location().columnNumber-(o.codeOffset()||0),inlineFrameIndex:t.inlineFrameIndex},n=this.stopIdForCallFrame(t);try{if(0===(await s.rawLocationToSourceLocation(i)).length)return null;const e=new Map,o=await s.listVariablesInScope(i);for(const r of o||[]){let o=e.get(r.scope);if(!o){const{type:i,typeName:a,icon:c}=await s.getScopeInfo(r.scope);o=new oe(t,n,i,a,c,s),e.set(r.scope,o)}o.object().variables.push(r)}return Array.from(e.values())}catch(t){return e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),null}}async getFunctionInfo(t,o){const{rawModuleId:r,plugin:s}=await this.rawModuleIdAndPluginForScript(t);if(!s)return null;const i={rawModuleId:r,codeOffset:o.columnNumber-(t.codeOffset()||0),inlineFrameIndex:0};try{const e=await s.getFunctionInfo(i);if("missingSymbolFiles"in e){const t=s.createPageResourceLoadInitiator();return{missingSymbolFiles:e.missingSymbolFiles.map((e=>({resourceUrl:e,initiator:t}))),..."frames"in e&&{frames:e.frames}}}return e}catch(t){return e.Console.Console.instance().warn(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),{frames:[]}}}async getInlinedFunctionRanges(t){const o=t.script();if(!o)return[];const{rawModuleId:s,plugin:i}=await this.rawModuleIdAndPluginForScript(o);if(!i)return[];const n={rawModuleId:s,codeOffset:t.columnNumber-(o.codeOffset()||0)};try{return(await i.getInlinedFunctionRanges(n)).map((e=>({start:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.startOffset)+(o.codeOffset()||0)),end:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.endOffset)+(o.codeOffset()||0))})))}catch(t){return e.Console.Console.instance().warn(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),[]}}async getInlinedCalleesRanges(t){const o=t.script();if(!o)return[];const{rawModuleId:s,plugin:i}=await this.rawModuleIdAndPluginForScript(o);if(!i)return[];const n={rawModuleId:s,codeOffset:t.columnNumber-(o.codeOffset()||0)};try{return(await i.getInlinedCalleesRanges(n)).map((e=>({start:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.startOffset)+(o.codeOffset()||0)),end:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.endOffset)+(o.codeOffset()||0))})))}catch(t){return e.Console.Console.instance().warn(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),[]}}async getMappedLines(e){const t=await Promise.all(this.scriptsForUISourceCode(e).map((e=>this.rawModuleIdAndPluginForScript(e))));let o=null;for(const{rawModuleId:r,plugin:s}of t){if(!s)continue;const t=await s.getMappedLines(r,e.url());void 0!==t&&(null===o?o=new Set(t):t.forEach((e=>o.add(e))))}return o}}class ie{project;uiSourceCodeToScripts;constructor(e,t){this.project=new l(t,"language_plugins::"+e.target().id(),i.Workspace.projectTypes.Network,"",!1),w.setTargetForProject(this.project,e.target()),this.uiSourceCodeToScripts=new Map}addSourceFiles(t,o){const s=t.createPageResourceLoadInitiator();for(const i of o){let o=this.project.uiSourceCodeForURL(i);if(o){const e=this.uiSourceCodeToScripts.get(o);e.includes(t)||e.push(t)}else{o=this.project.createUISourceCode(i,e.ResourceType.resourceTypes.SourceMapScript),w.setInitialFrameAttribution(o,t.frameId),this.uiSourceCodeToScripts.set(o,[t]);const n=new r.CompilerSourceMappingContentProvider.CompilerSourceMappingContentProvider(i,e.ResourceType.resourceTypes.SourceMapScript,s),a=e.ResourceType.ResourceType.mimeFromURL(i)||"text/javascript";this.project.addUISourceCodeWithProvider(o,n,null,a)}}}removeScript(e){this.uiSourceCodeToScripts.forEach(((t,o)=>{0===(t=t.filter((t=>t!==e))).length?(this.uiSourceCodeToScripts.delete(o),this.project.removeUISourceCode(o.url())):this.uiSourceCodeToScripts.set(o,t)}))}dispose(){this.project.dispose()}getProject(){return this.project}}var ne=Object.freeze({__proto__:null,SourceScope:oe,ExtensionRemoteObject:re,DebuggerLanguagePluginManager:se});class ae{#o;#S;#c;#G;#K;constructor(e,t,o){ce.add(this),this.#o=o,this.#S=new l(t,"debugger:"+e.target().id(),i.Workspace.projectTypes.Debugger,"",!0),this.#c=[e.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),e.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,this.parsedScriptSource,this),e.addEventListener(r.DebuggerModel.Events.DiscardedAnonymousScriptSource,this.discardedScriptSource,this)],this.#G=new Map,this.#K=new Map}static createV8ScriptURL(t){const o=e.ParsedURL.ParsedURL.extractName(t.sourceURL);return"debugger:///VM"+t.scriptId+(o?" "+o:"")}static scriptForUISourceCode(e){for(const t of ce){const o=t.#G.get(e);if(void 0!==o)return o}return null}uiSourceCodeForScript(e){return this.#K.get(e)??null}rawLocationToUILocation(e){const t=e.script();if(!t)return null;const o=this.#K.get(t);if(!o)return null;const{lineNumber:r,columnNumber:s}=t.rawLocationToRelativeLocation(e);return o.uiLocation(r,s)}uiLocationToRawLocations(e,t,o){const r=this.#G.get(e);return r?(({lineNumber:t,columnNumber:o}=r.relativeLocationToRawLocation({lineNumber:t,columnNumber:o})),[r.debuggerModel.createRawLocation(r,t,o??0)]):[]}uiLocationRangeToRawLocationRanges(e,{startLine:t,startColumn:o,endLine:r,endColumn:s}){const i=this.#G.get(e);if(!i)return[];({lineNumber:t,columnNumber:o}=i.relativeLocationToRawLocation({lineNumber:t,columnNumber:o})),({lineNumber:r,columnNumber:s}=i.relativeLocationToRawLocation({lineNumber:r,columnNumber:s}));return[{start:i.debuggerModel.createRawLocation(i,t,o),end:i.debuggerModel.createRawLocation(i,r,s)}]}parsedScriptSource(t){const o=t.data,r=ae.createV8ScriptURL(o),s=this.#S.createUISourceCode(r,e.ResourceType.resourceTypes.Script);o.isBreakpointCondition&&s.markAsUnconditionallyIgnoreListed(),this.#G.set(s,o),this.#K.set(o,s),this.#S.addUISourceCodeWithProvider(s,o,null,"text/javascript"),this.#o.updateLocations(o)}discardedScriptSource(e){const t=e.data,o=this.#K.get(t);void 0!==o&&(this.#K.delete(t),this.#G.delete(o),this.#S.removeUISourceCode(o.url()))}globalObjectCleared(){this.#K.clear(),this.#G.clear(),this.#S.reset()}dispose(){ce.delete(this),e.EventTarget.removeEventListeners(this.#c),this.globalObjectCleared(),this.#S.dispose()}}const ce=new Set;var ue=Object.freeze({__proto__:null,DefaultScriptMapping:ae});const le={liveEditFailed:"`LiveEdit` failed: {PH1}",liveEditCompileFailed:"`LiveEdit` compile failed: {PH1}"},de=n.i18n.registerUIStrings("models/bindings/ResourceScriptMapping.ts",le),ge=n.i18n.getLocalizedString.bind(void 0,de);class pe{debuggerModel;#_;debuggerWorkspaceBinding;#$;#u;#K;#c;constructor(e,t,o){this.debuggerModel=e,this.#_=t,this.debuggerWorkspaceBinding=o,this.#$=new Map,this.#u=new Map,this.#K=new Map;const s=e.runtimeModel();this.#c=[this.debuggerModel.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,(e=>this.addScript(e.data)),this),this.debuggerModel.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),s.addEventListener(r.RuntimeModel.Events.ExecutionContextDestroyed,this.executionContextDestroyed,this),s.target().targetManager().addEventListener("InspectedURLChanged",this.inspectedURLChanged,this)]}project(e){const t=(e.isContentScript()?"js:extensions:":"js::")+this.debuggerModel.target().id()+":"+e.frameId;let o=this.#u.get(t);if(!o){const r=e.isContentScript()?i.Workspace.projectTypes.ContentScripts:i.Workspace.projectTypes.Network;o=new l(this.#_,t,r,"",!1),w.setTargetForProject(o,this.debuggerModel.target()),this.#u.set(t,o)}return o}uiSourceCodeForScript(e){return this.#K.get(e)??null}rawLocationToUILocation(e){const t=e.script();if(!t)return null;const o=this.#K.get(t);if(!o)return null;const r=this.#$.get(o);if(!r)return null;if(r.hasDivergedFromVM()&&!r.isMergingToVM()||r.isDivergingFromVM())return null;if(r.script!==t)return null;const{lineNumber:s,columnNumber:i=0}=e;return o.uiLocation(s,i)}uiLocationToRawLocations(e,t,o){const r=this.#$.get(e);if(!r)return[];const{script:s}=r;return s?[this.debuggerModel.createRawLocation(s,t,o)]:[]}uiLocationRangeToRawLocationRanges(e,{startLine:t,startColumn:o,endLine:r,endColumn:s}){const i=this.#$.get(e);if(!i)return null;const{script:n}=i;if(!n)return null;return[{start:this.debuggerModel.createRawLocation(n,t,o),end:this.debuggerModel.createRawLocation(n,r,s)}]}inspectedURLChanged(e){for(let t=this.debuggerModel.target();t!==e.data;t=t.parentTarget())if(null===t)return;for(const e of Array.from(this.#K.keys()))this.removeScripts([e]),this.addScript(e)}addScript(t){if(t.isLiveEdit()||t.isBreakpointCondition)return;let o=t.sourceURL;if(!o)return;if(t.hasSourceURL)o=r.SourceMapManager.SourceMapManager.resolveRelativeSourceURL(t.debuggerModel.target(),o);else{if(t.isInlineScript())return;if(t.isContentScript()){if(!new e.ParsedURL.ParsedURL(o).isValid)return}}const s=this.project(t),i=s.uiSourceCodeForURL(o);if(i){const e=this.#$.get(i);e&&e.script&&this.removeScripts([e.script])}const n=t.originalContentProvider(),a=s.createUISourceCode(o,n.contentType());w.setInitialFrameAttribution(a,t.frameId);const c=E(this.debuggerModel.target(),t.frameId,o),u=new he(this,a,t);this.#$.set(a,u),this.#K.set(t,a);const l=t.isWasm()?"application/wasm":"text/javascript";s.addUISourceCodeWithProvider(a,n,c,l),this.debuggerWorkspaceBinding.updateLocations(t)}scriptFile(e){return this.#$.get(e)||null}removeScripts(e){const o=new t.MapUtilities.Multimap;for(const t of e){const e=this.#K.get(t);if(!e)continue;const r=this.#$.get(e);r&&r.dispose(),this.#$.delete(e),this.#K.delete(t),o.set(e.project(),e),this.debuggerWorkspaceBinding.updateLocations(t)}for(const e of o.keysArray()){const t=o.get(e);let r=!0;for(const o of e.uiSourceCodes())if(!t.has(o)){r=!1;break}r?(this.#u.delete(e.id()),e.removeProject()):t.forEach((t=>e.removeUISourceCode(t.url())))}}executionContextDestroyed(e){const t=e.data;this.removeScripts(this.debuggerModel.scriptsForExecutionContext(t))}globalObjectCleared(){const e=Array.from(this.#K.keys());this.removeScripts(e)}resetForTest(){this.globalObjectCleared()}dispose(){e.EventTarget.removeEventListeners(this.#c),this.globalObjectCleared()}}class he extends e.ObjectWrapper.ObjectWrapper{#J;uiSourceCode;script;#X;#Q;#Y;#Z;#ee=new e.Mutex.Mutex;constructor(e,t,o){super(),this.#J=e,this.uiSourceCode=t,this.script=this.uiSourceCode.contentType().isScript()?o:null,this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this)}isDiverged(){if(this.uiSourceCode.isDirty())return!0;if(!this.script)return!1;if(void 0===this.#X||null===this.#X)return!1;const e=this.uiSourceCode.workingCopy();if(!e)return!1;if(!e.startsWith(this.#X.trimEnd()))return!0;const t=this.uiSourceCode.workingCopy().substr(this.#X.length);return Boolean(t.length)&&!t.match(r.Script.sourceURLRegex)}workingCopyChanged(){this.update()}workingCopyCommitted(){if(this.uiSourceCode.project().canSetFileContent())return;if(!this.script)return;const e=this.uiSourceCode.workingCopy();this.script.editSource(e).then((({status:t,exceptionDetails:o})=>{this.scriptSourceWasSet(e,t,o)}))}async scriptSourceWasSet(t,o,r){if("Ok"===o&&(this.#X=t),await this.update(),"Ok"===o)return;if(!r)return void e.Console.Console.instance().addMessage(ge(le.liveEditFailed,{PH1:function(e){switch(e){case"BlockedByActiveFunction":return"Functions that are on the stack (currently being executed) can not be edited";case"BlockedByActiveGenerator":return"Async functions/generators that are active can not be edited";case"BlockedByTopLevelEsModuleChange":return"The top-level of ES modules can not be edited";case"CompileError":case"Ok":throw new Error("Compile errors and Ok status must not be reported on the console")}}(o)}),"warning");const s=ge(le.liveEditCompileFailed,{PH1:r.text});this.uiSourceCode.addLineMessage("Error",s,r.lineNumber,r.columnNumber)}async update(){const e=await this.#ee.acquire(),t=this.isDiverged();t&&!this.#Y?await this.divergeFromVM():!t&&this.#Y&&await this.mergeToVM(),e()}async divergeFromVM(){this.script&&(this.#Q=!0,await this.#J.debuggerWorkspaceBinding.updateLocations(this.script),this.#Q=void 0,this.#Y=!0,this.dispatchEventToListeners("DidDivergeFromVM"))}async mergeToVM(){this.script&&(this.#Y=void 0,this.#Z=!0,await this.#J.debuggerWorkspaceBinding.updateLocations(this.script),this.#Z=void 0,this.dispatchEventToListeners("DidMergeToVM"))}hasDivergedFromVM(){return Boolean(this.#Y)}isDivergingFromVM(){return Boolean(this.#Q)}isMergingToVM(){return Boolean(this.#Z)}checkMapping(){this.script&&void 0===this.#X?this.script.requestContentData().then((e=>{this.#X=s.ContentData.ContentData.textOr(e,null),this.update().then((()=>this.mappingCheckedForTest()))})):this.mappingCheckedForTest()}mappingCheckedForTest(){}dispose(){this.uiSourceCode.removeEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.removeEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this)}addSourceMapURL(e){this.script&&this.script.debuggerModel.setSourceMapURL(this.script,e)}addDebugInfoURL(e){if(!this.script)return;const{pluginManager:t}=Le.instance();t.setDebugInfoURL(this.script,e)}hasSourceMapURL(){return Boolean(this.script?.sourceMapURL)}async missingSymbolFiles(){if(!this.script)return null;const{pluginManager:e}=this.#J.debuggerWorkspaceBinding,t=await e.getSourcesForScript(this.script);return t&&"missingSymbolFiles"in t?t.missingSymbolFiles:null}}var me=Object.freeze({__proto__:null,ResourceScriptMapping:pe,ResourceScriptFile:he});let Se;class Le{resourceMapping;#te;#V;#F;pluginManager;constructor(e,t){this.resourceMapping=e,this.#te=[],this.#V=new Map,t.addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),t.addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.DebuggerResumed,this.debuggerResumed,this),t.observeModels(r.DebuggerModel.DebuggerModel,this),this.#F=new Set,this.pluginManager=new se(t,e.workspace,this)}static instance(e={forceNew:null,resourceMapping:null,targetManager:null}){const{forceNew:t,resourceMapping:o,targetManager:r}=e;if(!Se||t){if(!o||!r)throw new Error(`Unable to create DebuggerWorkspaceBinding: resourceMapping and targetManager must be provided: ${(new Error).stack}`);Se=new Le(o,r)}return Se}static removeInstance(){Se=void 0}addSourceMapping(e){this.#te.push(e)}removeSourceMapping(e){const t=this.#te.indexOf(e);-1!==t&&this.#te.splice(t,1)}async computeAutoStepRanges(e,t){function o(e,t){const{start:o,end:r}=t;return o.scriptId===e.scriptId&&(!(e.lineNumber<o.lineNumber||e.lineNumber>r.lineNumber)&&(!(e.lineNumber===o.lineNumber&&e.columnNumber<o.columnNumber)&&!(e.lineNumber===r.lineNumber&&e.columnNumber>=r.columnNumber)))}const r=t.location();if(!r)return[];const s=this.pluginManager;let i=[];if("StepOut"===e)return await s.getInlinedFunctionRanges(r);const n=await s.rawLocationToUILocation(r);if(n)return i=await s.uiLocationToRawLocationRanges(n.uiSourceCode,n.lineNumber,n.columnNumber)||[],i=i.filter((e=>o(r,e))),"StepOver"===e&&(i=i.concat(await s.getInlinedCalleesRanges(r))),i;const a=this.#V.get(r.debuggerModel)?.compilerMapping;return a?(i=a.getLocationRangesForSameSourceLocation(r),i=i.filter((e=>o(r,e))),i):[]}modelAdded(e){e.setBeforePausedCallback(this.shouldPause.bind(this)),this.#V.set(e,new Me(e,this)),e.setComputeAutoStepRangesCallback(this.computeAutoStepRanges.bind(this))}modelRemoved(e){e.setComputeAutoStepRangesCallback(null);const t=this.#V.get(e);t&&(t.dispose(),this.#V.delete(e))}async pendingLiveLocationChangesPromise(){await Promise.all(this.#F)}recordLiveLocationChange(e){e.then((()=>{this.#F.delete(e)})),this.#F.add(e)}async updateLocations(e){const t=this.#V.get(e.debuggerModel);if(t){const o=t.updateLocations(e);this.recordLiveLocationChange(o),await o}}async createLiveLocation(e,t,o){const r=this.#V.get(e.debuggerModel);if(!r)return null;const s=r.createLiveLocation(e,t,o);return this.recordLiveLocationChange(s),s}async createStackTraceTopFrameLiveLocation(e,t,o){console.assert(e.length>0);const r=be.createStackTraceTopFrameLocation(e,this,t,o);return this.recordLiveLocationChange(r),r}async createCallFrameLiveLocation(e,t,o){if(!e.script())return null;const r=e.debuggerModel,s=this.createLiveLocation(e,t,o);this.recordLiveLocationChange(s);const i=await s;return i?(this.registerCallFrameLiveLocation(r,i),i):null}async rawLocationToUILocation(e){for(const t of this.#te){const o=t.rawLocationToUILocation(e);if(o)return o}const t=await this.pluginManager.rawLocationToUILocation(e);if(t)return t;const o=this.#V.get(e.debuggerModel);return o?o.rawLocationToUILocation(e):null}uiSourceCodeForSourceMapSourceURL(e,t,o){const r=this.#V.get(e);return r?r.compilerMapping.uiSourceCodeForURL(t,o):null}async uiSourceCodeForSourceMapSourceURLPromise(e,t,o){return this.uiSourceCodeForSourceMapSourceURL(e,t,o)||this.waitForUISourceCodeAdded(t,e.target())}async uiSourceCodeForDebuggerLanguagePluginSourceURLPromise(e,t){return this.pluginManager.uiSourceCodeForURL(e,t)||this.waitForUISourceCodeAdded(t,e.target())}uiSourceCodeForScript(e){const t=this.#V.get(e.debuggerModel);return t?t.uiSourceCodeForScript(e):null}waitForUISourceCodeAdded(e,t){return new Promise((o=>{const r=i.Workspace.WorkspaceImpl.instance(),s=r.addEventListener(i.Workspace.Events.UISourceCodeAdded,(n=>{const a=n.data;a.url()===e&&w.targetForUISourceCode(a)===t&&(r.removeEventListener(i.Workspace.Events.UISourceCodeAdded,s.listener),o(a))}))}))}async uiLocationToRawLocations(e,t,o){for(const r of this.#te){const s=r.uiLocationToRawLocations(e,t,o);if(s.length)return s}const r=await this.pluginManager.uiLocationToRawLocations(e,t,o);if(r)return r;for(const r of this.#V.values()){const s=r.uiLocationToRawLocations(e,t,o);if(s.length)return s}return[]}async uiLocationRangeToRawLocationRanges(e,t){for(const o of this.#te){const r=o.uiLocationRangeToRawLocationRanges(e,t);if(r)return r}const o=await this.pluginManager.uiLocationRangeToRawLocationRanges(e,t);if(o)return o;for(const o of this.#V.values()){const r=o.uiLocationRangeToRawLocationRanges(e,t);if(r)return r}return[]}uiLocationToRawLocationsForUnformattedJavaScript(e,t,o){console.assert(e.contentType().isScript());const r=[];for(const s of this.#V.values())r.push(...s.uiLocationToRawLocations(e,t,o));return r}async normalizeUILocation(e){const t=await this.uiLocationToRawLocations(e.uiSourceCode,e.lineNumber,e.columnNumber);for(const e of t){const t=await this.rawLocationToUILocation(e);if(t)return t}return e}async getMappedLines(e){for(const t of this.#V.values()){const o=t.getMappedLines(e);if(null!==o)return o}return await this.pluginManager.getMappedLines(e)}scriptFile(e,t){const o=this.#V.get(t);return o?o.getResourceScriptMapping().scriptFile(e):null}scriptsForUISourceCode(e){const t=new Set;this.pluginManager.scriptsForUISourceCode(e).forEach((e=>t.add(e)));for(const o of this.#V.values()){const r=o.getResourceScriptMapping().scriptFile(e);r&&r.script&&t.add(r.script),o.compilerMapping.scriptsForUISourceCode(e).forEach((e=>t.add(e)))}return[...t]}supportsConditionalBreakpoints(e){return this.pluginManager.scriptsForUISourceCode(e).every((e=>e.isJavaScript()))}globalObjectCleared(e){this.reset(e.data)}reset(e){const t=this.#V.get(e);if(t){for(const e of t.callFrameLocations.values())this.removeLiveLocation(e);t.callFrameLocations.clear()}}resetForTest(e){const t=e.model(r.DebuggerModel.DebuggerModel),o=this.#V.get(t);o&&o.getResourceScriptMapping().resetForTest()}registerCallFrameLiveLocation(e,t){const o=this.#V.get(e);if(o){o.callFrameLocations.add(t)}}removeLiveLocation(e){const t=this.#V.get(e.rawLocation.debuggerModel);t&&t.disposeLocation(e)}debuggerResumed(e){this.reset(e.data)}async shouldPause(t,o){const{callFrames:[r]}=t;if(!r)return!1;const s=r.functionLocation();if(!(o&&"step"===t.reason&&s&&r.script.isWasm()&&e.Settings.moduleSetting("wasm-auto-stepping").get()&&this.pluginManager.hasPluginForScript(r.script)))return!0;return!!await this.pluginManager.rawLocationToUILocation(r.location())||(o.script()!==s.script()||o.columnNumber!==s.columnNumber||o.lineNumber!==s.lineNumber)}}class Me{#oe;#o;callFrameLocations;#re;#R;#J;compilerMapping;#m;constructor(e,o){this.#oe=e,this.#o=o,this.callFrameLocations=new Set;const{workspace:r}=o.resourceMapping;this.#re=new ae(e,r,o),this.#R=o.resourceMapping,this.#J=new pe(e,r,o),this.compilerMapping=new T(e,r,o),this.#m=new t.MapUtilities.Multimap}async createLiveLocation(e,t,o){console.assert(""!==e.scriptId);const r=e.scriptId,s=new fe(r,e,this.#o,t,o);return this.#m.set(r,s),await s.update(),s}disposeLocation(e){this.#m.delete(e.scriptId,e)}async updateLocations(e){const t=[];for(const o of this.#m.get(e.scriptId))t.push(o.update());await Promise.all(t)}rawLocationToUILocation(e){let t=this.compilerMapping.rawLocationToUILocation(e);return t=t||this.#J.rawLocationToUILocation(e),t=t||this.#R.jsLocationToUILocation(e),t=t||this.#re.rawLocationToUILocation(e),t}uiSourceCodeForScript(e){let t=null;return t=t||this.#J.uiSourceCodeForScript(e),t=t||this.#R.uiSourceCodeForScript(e),t=t||this.#re.uiSourceCodeForScript(e),t}uiLocationToRawLocations(e,t,o=0){let r=this.compilerMapping.uiLocationToRawLocations(e,t,o);return r=r.length?r:this.#J.uiLocationToRawLocations(e,t,o),r=r.length?r:this.#R.uiLocationToJSLocations(e,t,o),r=r.length?r:this.#re.uiLocationToRawLocations(e,t,o),r}uiLocationRangeToRawLocationRanges(e,t){let o=this.compilerMapping.uiLocationRangeToRawLocationRanges(e,t);return o??=this.#J.uiLocationRangeToRawLocationRanges(e,t),o??=this.#R.uiLocationRangeToJSLocationRanges(e,t),o??=this.#re.uiLocationRangeToRawLocationRanges(e,t),o}getMappedLines(e){return this.compilerMapping.getMappedLines(e)}dispose(){this.#oe.setBeforePausedCallback(null),this.compilerMapping.dispose(),this.#J.dispose(),this.#re.dispose()}getResourceScriptMapping(){return this.#J}}class fe extends y{scriptId;rawLocation;#se;constructor(e,t,o,r,s){super(r,s),this.scriptId=e,this.rawLocation=t,this.#se=o}async uiLocation(){const e=this.rawLocation;return this.#se.rawLocationToUILocation(e)}dispose(){super.dispose(),this.#se.removeLiveLocation(this)}async isIgnoreListed(){const e=await this.uiLocation();return!!e&&S.instance().isUserOrSourceMapIgnoreListedUISourceCode(e.uiSourceCode)}}class be extends y{#ie;#ne;#m;constructor(e,t){super(e,t),this.#ie=!0,this.#ne=null,this.#m=null}static async createStackTraceTopFrameLocation(e,t,o,r){const s=new be(o,r),i=e.map((e=>t.createLiveLocation(e,s.scheduleUpdate.bind(s),r)));return s.#m=(await Promise.all(i)).filter((e=>Boolean(e))),await s.updateLocation(),s}async uiLocation(){return this.#ne?this.#ne.uiLocation():null}async isIgnoreListed(){return!!this.#ne&&this.#ne.isIgnoreListed()}dispose(){if(super.dispose(),this.#m)for(const e of this.#m)e.dispose();this.#m=null,this.#ne=null}async scheduleUpdate(){this.#ie||(this.#ie=!0,queueMicrotask((()=>{this.updateLocation()})))}async updateLocation(){if(this.#ie=!1,this.#m&&0!==this.#m.length){this.#ne=this.#m[0];for(const e of this.#m)if(!await e.isIgnoreListed()){this.#ne=e;break}this.update()}}}var Ce=Object.freeze({__proto__:null,DebuggerWorkspaceBinding:Le,Location:fe});class ve{#ae;#ce;#ue;#le;#de;#ge;#pe;#he;#me;#Se;#Le;#Me;constructor(e,t,o){this.#ae=e,this.#ce=e.size,this.#ue=0,this.#de=t||Number.MAX_VALUE,this.#ge=o,this.#pe=new TextDecoder,this.#he=!1,this.#me=null,this.#le=null}async read(e){if(this.#ge&&this.#ge(this),this.#ae?.type.endsWith("gzip")){const e=this.#ae.stream(),t=this.decompressStream(e);this.#le=t.getReader()}else this.#Me=new FileReader,this.#Me.onload=this.onChunkLoaded.bind(this),this.#Me.onerror=this.onError.bind(this);return this.#Le=e,this.loadChunk(),new Promise((e=>{this.#Se=e}))}cancel(){this.#he=!0}loadedSize(){return this.#ue}fileSize(){return this.#ce}fileName(){return this.#ae?this.#ae.name:""}error(){return this.#me}decompressStream(e){const t=new DecompressionStream("gzip");return e.pipeThrough(t)}onChunkLoaded(e){if(this.#he)return;if(e.target.readyState!==FileReader.DONE)return;if(!this.#Me)return;const t=this.#Me.result;this.#ue+=t.byteLength;const o=this.#ue===this.#ce;this.decodeChunkBuffer(t,o)}async decodeChunkBuffer(e,t){if(!this.#Le)return;const o=this.#pe.decode(e,{stream:!t});await this.#Le.write(o,t),this.#he||(this.#ge&&this.#ge(this),t?this.finishRead():this.loadChunk())}async finishRead(){this.#Le&&(this.#ae=null,this.#Me=null,await this.#Le.close(),this.#Se(!this.#me))}async loadChunk(){if(this.#Le&&this.#ae){if(this.#le){const{value:e,done:t}=await this.#le.read();if(t||!e)return await this.#Le.write("",!0),this.finishRead();this.decodeChunkBuffer(e.buffer,!1)}if(this.#Me){const e=this.#ue,t=Math.min(this.#ce,e+this.#de),o=this.#ae.slice(e,t);this.#Me.readAsArrayBuffer(o)}}}onError(e){const t=e.target;this.#me=t.error,this.#Se(!1)}}var we=Object.freeze({__proto__:null,ChunkedFileReader:ve,FileOutputStream:class{#fe;#be;#Ce;constructor(){this.#fe=[]}async open(e){this.#Ce=!1,this.#fe=[],this.#be=e;const t=await i.FileManager.FileManager.instance().save(this.#be,"",!0,!1);return t&&i.FileManager.FileManager.instance().addEventListener("AppendedToURL",this.onAppendDone,this),Boolean(t)}write(e){return new Promise((t=>{this.#fe.push(t),i.FileManager.FileManager.instance().append(this.#be,e)}))}async close(){this.#Ce=!0,this.#fe.length||(i.FileManager.FileManager.instance().removeEventListener("AppendedToURL",this.onAppendDone,this),i.FileManager.FileManager.instance().close(this.#be))}onAppendDone(e){if(e.data!==this.#be)return;const t=this.#fe.shift();t&&t(),this.#fe.length||this.#Ce&&(i.FileManager.FileManager.instance().removeEventListener("AppendedToURL",this.onAppendDone,this),i.FileManager.FileManager.instance().close(this.#be))}}});class Ie{#ve=new WeakMap;constructor(){r.TargetManager.TargetManager.instance().observeModels(r.DebuggerModel.DebuggerModel,this),r.TargetManager.TargetManager.instance().observeModels(r.CSSModel.CSSModel,this)}modelAdded(e){const t=e.target(),o=this.#ve.get(t)??new Te;e instanceof r.DebuggerModel.DebuggerModel?o.setDebuggerModel(e):o.setCSSModel(e),this.#ve.set(t,o)}modelRemoved(e){const t=e.target(),o=this.#ve.get(t);o?.clear()}addMessage(e,t,o){const r=this.#ve.get(o);r?.addMessage(e,t)}clear(){for(const e of r.TargetManager.TargetManager.instance().targets()){const t=this.#ve.get(e);t?.clear()}}}class Te{#oe;#b;#we=new Map;#p;constructor(){this.#p=new F,i.Workspace.WorkspaceImpl.instance().addEventListener(i.Workspace.Events.UISourceCodeAdded,this.#Ie.bind(this))}setDebuggerModel(e){if(this.#oe)throw new Error("Cannot set DebuggerModel twice");this.#oe=e,e.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,(e=>{queueMicrotask((()=>{this.#Te(e)}))})),e.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.#Re,this)}setCSSModel(e){if(this.#b)throw new Error("Cannot set CSSModel twice");this.#b=e,e.addEventListener(r.CSSModel.Events.StyleSheetAdded,(e=>queueMicrotask((()=>this.#ye(e)))))}async addMessage(e,t){const o=new ye(e,this.#p),r=this.#Fe(t)??this.#Ue(t)??this.#Pe(t);if(r&&await o.updateLocationSource(r),t.url){let e=this.#we.get(t.url);e||(e=[],this.#we.set(t.url,e)),e.push({source:t,presentation:o})}}#Pe(e){if(!e.url)return null;const t=i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e.url);return t?new i.UISourceCode.UILocation(t,e.line,e.column):null}#Ue(e){if(!this.#b||!e.url)return null;return this.#b.createRawLocationsByURL(e.url,e.line,e.column)[0]??null}#Fe(e){if(!this.#oe)return null;if(e.scriptId)return this.#oe.createRawLocationByScriptId(e.scriptId,e.line,e.column);const t=e.stackTrace&&e.stackTrace.callFrames?e.stackTrace.callFrames[0]:null;return t?this.#oe.createRawLocationByScriptId(t.scriptId,t.lineNumber,t.columnNumber):e.url?this.#oe.createRawLocationByURL(e.url,e.line,e.column):null}#Te(e){const t=e.data,o=this.#we.get(t.sourceURL),r=[];for(const{presentation:e,source:s}of o??[]){const o=this.#Fe(s);o&&t.scriptId===o.scriptId&&r.push(e.updateLocationSource(o))}Promise.all(r).then(this.parsedScriptSourceForTest.bind(this))}parsedScriptSourceForTest(){}#Ie(e){const t=e.data,o=this.#we.get(t.url()),r=[];for(const{presentation:e,source:s}of o??[])r.push(e.updateLocationSource(new i.UISourceCode.UILocation(t,s.line,s.column)));Promise.all(r).then(this.uiSourceCodeAddedForTest.bind(this))}uiSourceCodeAddedForTest(){}#ye(e){const t=e.data,o=this.#we.get(t.sourceURL),s=[];for(const{source:e,presentation:i}of o??[])t.containsLocation(e.line,e.column)&&s.push(i.updateLocationSource(new r.CSSModel.CSSLocation(t,e.line,e.column)));Promise.all(s).then(this.styleSheetAddedForTest.bind(this))}styleSheetAddedForTest(){}clear(){this.#Re()}#Re(){const e=Array.from(this.#we.values()).flat();for(const{presentation:t}of e)t.dispose();this.#we.clear(),this.#p.disposeAll()}}class Re extends y{#Pe;constructor(e,t,o){super(t,o),this.#Pe=e}async isIgnoreListed(){return!1}async uiLocation(){return this.#Pe}}class ye{#ke;#je;#p;#De;constructor(e,t){this.#De=e,this.#p=t}async updateLocationSource(e){e instanceof r.DebuggerModel.Location?await Le.instance().createLiveLocation(e,this.#Ne.bind(this),this.#p):e instanceof r.CSSModel.CSSLocation?await z.instance().createLiveLocation(e,this.#Ne.bind(this),this.#p):e instanceof i.UISourceCode.UILocation&&(this.#je||(this.#je=new Re(e,this.#Ne.bind(this),this.#p),await this.#je.update()))}async#Ne(e){this.#ke&&this.#ke.removeMessage(this.#De),e!==this.#je&&(this.#ke?.removeMessage(this.#De),this.#je?.dispose(),this.#je=e);const t=await e.uiLocation();t&&(this.#De.range=s.TextRange.TextRange.createFromLocation(t.lineNumber,t.columnNumber||0),this.#ke=t.uiSourceCode,this.#ke.addMessage(this.#De))}dispose(){this.#ke?.removeMessage(this.#De),this.#je?.dispose()}}var Fe=Object.freeze({__proto__:null,PresentationSourceFrameMessageManager:Ie,PresentationConsoleMessageManager:class{#Ee=new Ie;constructor(){r.TargetManager.TargetManager.instance().addModelListener(r.ConsoleModel.ConsoleModel,r.ConsoleModel.Events.MessageAdded,(e=>this.consoleMessageAdded(e.data))),r.ConsoleModel.ConsoleModel.allMessagesUnordered().forEach(this.consoleMessageAdded,this),r.TargetManager.TargetManager.instance().addModelListener(r.ConsoleModel.ConsoleModel,r.ConsoleModel.Events.ConsoleCleared,(()=>this.#Ee.clear()))}consoleMessageAdded(e){const t=e.runtimeModel();if(!e.isErrorOrWarning()||!e.runtimeModel()||"violation"===e.source||!t)return;const o="error"===e.level?"Error":"Warning";this.#Ee.addMessage(new i.UISourceCode.Message(o,e.messageText),e,t.target())}},PresentationSourceFrameMessageHelper:Te,PresentationSourceFrameMessage:ye});const Ue=new WeakMap,Pe=new WeakMap,ke=new WeakSet;function je(e){return new s.TextRange.TextRange(e.lineOffset,e.columnOffset,e.endLine,e.endColumn)}function De(e){return new s.TextRange.TextRange(e.startLine,e.startColumn,e.endLine,e.endColumn)}class Ne{project;#L;#b;#c;constructor(e,t){const o=t.target();this.project=new l(e,"resources:"+o.id(),i.Workspace.projectTypes.Network,"",!1),w.setTargetForProject(this.project,o),this.#L=new Map;const s=o.model(r.CSSModel.CSSModel);console.assert(Boolean(s)),this.#b=s;for(const e of t.frames())for(const t of e.getResourcesMap().values())this.addResource(t);this.#c=[t.addEventListener(r.ResourceTreeModel.Events.ResourceAdded,this.resourceAdded,this),t.addEventListener(r.ResourceTreeModel.Events.FrameWillNavigate,this.frameWillNavigate,this),t.addEventListener(r.ResourceTreeModel.Events.FrameDetached,this.frameDetached,this),this.#b.addEventListener(r.CSSModel.Events.StyleSheetChanged,(e=>{this.styleSheetChanged(e)}),this)]}async styleSheetChanged(e){const t=this.#b.styleSheetHeaderForId(e.data.styleSheetId);if(!t||!t.isInline||t.isInline&&t.isMutable)return;const o=this.#L.get(t.resourceURL());o&&await o.styleSheetChanged(t,e.data.edit||null)}acceptsResource(t){const o=t.resourceType();return(o===e.ResourceType.resourceTypes.Image||o===e.ResourceType.resourceTypes.Font||o===e.ResourceType.resourceTypes.Document||o===e.ResourceType.resourceTypes.Manifest||o===e.ResourceType.resourceTypes.Fetch||o===e.ResourceType.resourceTypes.XHR)&&(!(o===e.ResourceType.resourceTypes.Image&&t.mimeType&&!t.mimeType.startsWith("image"))&&(!(o===e.ResourceType.resourceTypes.Font&&t.mimeType&&!t.mimeType.includes("font"))&&(o!==e.ResourceType.resourceTypes.Image&&o!==e.ResourceType.resourceTypes.Font||!e.ParsedURL.schemeIs(t.contentURL(),"data:"))))}resourceAdded(e){this.addResource(e.data)}addResource(e){if(!this.acceptsResource(e))return;let t=this.#L.get(e.url);t?t.addResource(e):(t=new Ee(this.project,e),this.#L.set(e.url,t))}removeFrameResources(e){for(const t of e.resources()){if(!this.acceptsResource(t))continue;const e=this.#L.get(t.url);e&&(1===e.resources.size?(e.dispose(),this.#L.delete(t.url)):e.removeResource(t))}}frameWillNavigate(e){this.removeFrameResources(e.data)}frameDetached(e){this.removeFrameResources(e.data.frame)}resetForTest(){for(const e of this.#L.values())e.dispose();this.#L.clear()}dispose(){e.EventTarget.removeEventListeners(this.#c);for(const e of this.#L.values())e.dispose();this.#L.clear(),this.project.removeProject()}getProject(){return this.project}}class Ee{resources;#S;#ke;#xe;constructor(e,t){this.resources=new Set([t]),this.#S=e,this.#ke=this.#S.createUISourceCode(t.url,t.contentType()),ke.add(this.#ke),t.frameId&&w.setInitialFrameAttribution(this.#ke,t.frameId),this.#S.addUISourceCodeWithProvider(this.#ke,this,x(t),t.mimeType),this.#xe=[],Promise.all([...this.inlineScripts().map((e=>Le.instance().updateLocations(e))),...this.inlineStyles().map((e=>z.instance().updateLocations(e)))])}inlineStyles(){const e=w.targetForUISourceCode(this.#ke),t=[];if(!e)return t;const o=e.model(r.CSSModel.CSSModel);if(o)for(const e of o.getStyleSheetIdsForURL(this.#ke.url())){const r=o.styleSheetHeaderForId(e);r&&t.push(r)}return t}inlineScripts(){const e=w.targetForUISourceCode(this.#ke);if(!e)return[];const t=e.model(r.DebuggerModel.DebuggerModel);return t?t.scripts().filter((e=>e.embedderName()===this.#ke.url())):[]}async styleSheetChanged(e,t){if(this.#xe.push({stylesheet:e,edit:t}),this.#xe.length>1)return;const o=await this.#ke.requestContentData();s.ContentData.ContentData.isError(o)||await this.innerStyleSheetChanged(o.text),this.#xe=[]}async innerStyleSheetChanged(e){const t=this.inlineScripts(),o=this.inlineStyles();let r=new s.Text.Text(e);for(const e of this.#xe){const i=e.edit;if(!i)continue;const n=e.stylesheet,a=Ue.get(n)??De(n),c=i.oldRange.relativeFrom(a.startLine,a.startColumn),u=i.newRange.relativeFrom(a.startLine,a.startColumn);r=new s.Text.Text(r.replaceRange(c,i.newText));const l=[];for(const e of t){const t=Pe.get(e)??je(e);t.follows(c)&&(Pe.set(e,t.rebaseAfterTextEdit(c,u)),l.push(Le.instance().updateLocations(e)))}for(const e of o){const t=Ue.get(e)??De(e);t.follows(c)&&(Ue.set(e,t.rebaseAfterTextEdit(c,u)),l.push(z.instance().updateLocations(e)))}await Promise.all(l)}this.#ke.addRevision(r.value())}addResource(e){this.resources.add(e),e.frameId&&w.addFrameAttribution(this.#ke,e.frameId)}removeResource(e){this.resources.delete(e),e.frameId&&w.removeFrameAttribution(this.#ke,e.frameId)}dispose(){this.#S.removeUISourceCode(this.#ke.url()),Promise.all([...this.inlineScripts().map((e=>Le.instance().updateLocations(e))),...this.inlineStyles().map((e=>z.instance().updateLocations(e)))])}firstResource(){return console.assert(this.resources.size>0),this.resources.values().next().value}contentURL(){return this.firstResource().contentURL()}contentType(){return this.firstResource().contentType()}requestContent(){return this.firstResource().requestContent()}requestContentData(){return this.firstResource().requestContentData()}searchInContent(e,t,o){return this.firstResource().searchInContent(e,t,o)}}var xe=Object.freeze({__proto__:null,ResourceMapping:class{workspace;#y;constructor(e,t){this.workspace=t,this.#y=new Map,e.observeModels(r.ResourceTreeModel.ResourceTreeModel,this)}modelAdded(e){const t=new Ne(this.workspace,e);this.#y.set(e,t)}modelRemoved(e){const t=this.#y.get(e);t&&(t.dispose(),this.#y.delete(e))}infoForTarget(e){const t=e.model(r.ResourceTreeModel.ResourceTreeModel);return t&&this.#y.get(t)||null}uiSourceCodeForScript(e){const t=this.infoForTarget(e.debuggerModel.target());if(!t)return null;return t.getProject().uiSourceCodeForURL(e.sourceURL)}cssLocationToUILocation(e){const t=e.header();if(!t)return null;const o=this.infoForTarget(e.cssModel().target());if(!o)return null;const r=o.getProject().uiSourceCodeForURL(e.url);if(!r)return null;const s=Ue.get(t)??De(t),i=e.lineNumber+s.startLine-t.startLine;let n=e.columnNumber;return e.lineNumber===t.startLine&&(n+=s.startColumn-t.startColumn),r.uiLocation(i,n)}jsLocationToUILocation(e){const t=e.script();if(!t)return null;const o=this.infoForTarget(e.debuggerModel.target());if(!o)return null;const r=t.embedderName();if(!r)return null;const s=o.getProject().uiSourceCodeForURL(r);if(!s)return null;const{startLine:i,startColumn:n}=Pe.get(t)??je(t);let{lineNumber:a,columnNumber:c}=e;return a===t.lineOffset&&(c+=n-t.columnOffset),a+=i-t.lineOffset,t.hasSourceURL&&(0===a&&(c+=t.columnOffset),a+=t.lineOffset),s.uiLocation(a,c)}uiLocationToJSLocations(e,t,o){if(!ke.has(e))return[];const s=w.targetForUISourceCode(e);if(!s)return[];const i=s.model(r.DebuggerModel.DebuggerModel);if(!i)return[];const n=[];for(const r of i.scripts()){if(r.embedderName()!==e.url())continue;const s=Pe.get(r)??je(r);if(!s.containsLocation(t,o))continue;let a=t,c=o;r.hasSourceURL&&(a-=s.startLine,0===a&&(c-=s.startColumn)),n.push(i.createRawLocation(r,a,c))}return n}uiLocationRangeToJSLocationRanges(e,t){if(!ke.has(e))return null;const o=w.targetForUISourceCode(e);if(!o)return null;const s=o.model(r.DebuggerModel.DebuggerModel);if(!s)return null;const i=[];for(const o of s.scripts()){if(o.embedderName()!==e.url())continue;const r=(Pe.get(o)??je(o)).intersection(t);if(r.isEmpty())continue;let{startLine:n,startColumn:a,endLine:c,endColumn:u}=r;o.hasSourceURL&&(n-=r.startLine,0===n&&(a-=r.startColumn),c-=r.startLine,0===c&&(u-=r.startColumn));const l=s.createRawLocation(o,n,a),d=s.createRawLocation(o,c,u);i.push({start:l,end:d})}return i}getMappedLines(e){if(!ke.has(e))return null;const t=w.targetForUISourceCode(e);if(!t)return null;const o=t.model(r.DebuggerModel.DebuggerModel);if(!o)return null;const s=new Set;for(const t of o.scripts()){if(t.embedderName()!==e.url())continue;const{startLine:o,endLine:r}=Pe.get(t)??je(t);for(let e=o;e<=r;++e)s.add(e)}return s}uiLocationToCSSLocations(e){if(!ke.has(e.uiSourceCode))return[];const t=w.targetForUISourceCode(e.uiSourceCode);if(!t)return[];const o=t.model(r.CSSModel.CSSModel);return o?o.createRawLocationsByURL(e.uiSourceCode.url(),e.lineNumber,e.columnNumber):[]}resetForTest(e){const t=e.model(r.ResourceTreeModel.ResourceTreeModel),o=t?this.#y.get(t):null;o&&o.resetForTest()}}});var Ae=Object.freeze({__proto__:null,TempFile:class{#Ae;constructor(){this.#Ae=null}write(e){this.#Ae&&e.unshift(this.#Ae),this.#Ae=new Blob(e,{type:"text/plain"})}read(){return this.readRange()}size(){return this.#Ae?this.#Ae.size:0}async readRange(t,o){if(!this.#Ae)return e.Console.Console.instance().error("Attempt to read a temp file that was never written"),"";const r="number"==typeof t||"number"==typeof o?this.#Ae.slice(t,o):this.#Ae,s=new FileReader;try{await new Promise(((e,t)=>{s.onloadend=e,s.onerror=t,s.readAsText(r)}))}catch(t){e.Console.Console.instance().error("Failed to read from temp file: "+t.message)}return s.result}async copyToOutputStream(e,t){if(!this.#Ae)return e.close(),null;const o=new ve(this.#Ae,1e7,t);return o.read(e).then((e=>e?null:o.error()))}remove(){this.#Ae=null}}});export{G as CSSWorkspaceBinding,R as CompilerScriptMapping,d as ContentProviderBasedProject,ne as DebuggerLanguagePlugins,Ce as DebuggerWorkspaceBinding,ue as DefaultScriptMapping,we as FileUtils,M as IgnoreListManager,U as LiveLocation,I as NetworkProject,Fe as PresentationConsoleMessageHelper,xe as ResourceMapping,me as ResourceScriptMapping,A as ResourceUtils,D as SASSSourceMapping,H as StylesSourceMapping,Ae as TempFile};
