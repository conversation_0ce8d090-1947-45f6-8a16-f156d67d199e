import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/sdk/sdk.js";import*as i from"../../core/common/common.js";import*as s from"../../core/host/host.js";import*as r from"../network/forward/forward.js";import*as n from"../../ui/components/icon_button/icon_button.js";import*as o from"../../ui/legacy/legacy.js";import*as a from"../../ui/visual_logging/visual_logging.js";const c={theSecurityOfThisPageIsUnknown:"The security of this page is unknown.",thisPageIsNotSecure:"This page is not secure.",thisPageIsSecureValidHttps:"This page is secure (valid HTTPS).",thisPageIsNotSecureBrokenHttps:"This page is not secure (broken HTTPS).",cipherWithMAC:"{PH1} with {PH2}",keyExchangeWithGroup:"{PH1} with {PH2}"},l=e.i18n.registerUIStrings("panels/security/SecurityModel.ts",c),u=e.i18n.getLocalizedString.bind(void 0,l),d=e.i18n.getLazilyComputedLocalizedString.bind(void 0,l);class h extends t.SDKModel.SDKModel{dispatcher;securityAgent;constructor(e){super(e),this.dispatcher=new f(this),this.securityAgent=e.securityAgent(),e.registerSecurityDispatcher(this.dispatcher),this.securityAgent.invoke_enable()}resourceTreeModel(){return this.target().model(t.ResourceTreeModel.ResourceTreeModel)}networkManager(){return this.target().model(t.NetworkManager.NetworkManager)}}function g(e,t){const i=["info","insecure-broken","insecure","neutral","secure","unknown"];return i.indexOf(e)-i.indexOf(t)}var p;t.SDKModel.SDKModel.register(h,{capabilities:512,autostart:!1}),function(e){e.VisibleSecurityStateChanged="VisibleSecurityStateChanged"}(p||(p={}));const y={unknown:d(c.theSecurityOfThisPageIsUnknown),insecure:d(c.thisPageIsNotSecure),neutral:d(c.thisPageIsNotSecure),secure:d(c.thisPageIsSecureValidHttps),"insecure-broken":d(c.thisPageIsNotSecureBrokenHttps)};class m{securityState;certificateSecurityState;safetyTipInfo;securityStateIssueIds;constructor(e,t,i,s){this.securityState=e,this.certificateSecurityState=t?new S(t):null,this.safetyTipInfo=i?new w(i):null,this.securityStateIssueIds=s}}class S{protocol;keyExchange;keyExchangeGroup;cipher;mac;certificate;subjectName;issuer;validFrom;validTo;certificateNetworkError;certificateHasWeakSignature;certificateHasSha1Signature;modernSSL;obsoleteSslProtocol;obsoleteSslKeyExchange;obsoleteSslCipher;obsoleteSslSignature;constructor(e){this.protocol=e.protocol,this.keyExchange=e.keyExchange,this.keyExchangeGroup=e.keyExchangeGroup||null,this.cipher=e.cipher,this.mac=e.mac||null,this.certificate=e.certificate,this.subjectName=e.subjectName,this.issuer=e.issuer,this.validFrom=e.validFrom,this.validTo=e.validTo,this.certificateNetworkError=e.certificateNetworkError||null,this.certificateHasWeakSignature=e.certificateHasWeakSignature,this.certificateHasSha1Signature=e.certificateHasSha1Signature,this.modernSSL=e.modernSSL,this.obsoleteSslProtocol=e.obsoleteSslProtocol,this.obsoleteSslKeyExchange=e.obsoleteSslKeyExchange,this.obsoleteSslCipher=e.obsoleteSslCipher,this.obsoleteSslSignature=e.obsoleteSslSignature}isCertificateExpiringSoon(){const e=new Date(1e3*this.validTo).getTime();return e<new Date(Date.now()).setHours(48)&&e>Date.now()}getKeyExchangeName(){return this.keyExchangeGroup?this.keyExchange?u(c.keyExchangeWithGroup,{PH1:this.keyExchange,PH2:this.keyExchangeGroup}):this.keyExchangeGroup:this.keyExchange}getCipherFullName(){return this.mac?u(c.cipherWithMAC,{PH1:this.cipher,PH2:this.mac}):this.cipher}}class w{safetyTipStatus;safeUrl;constructor(e){this.safetyTipStatus=e.safetyTipStatus,this.safeUrl=e.safeUrl||null}}class v{securityState;title;summary;description;certificate;mixedContentType;recommendations;constructor(e,t,i,s,r=[],n="none",o=[]){this.securityState=e,this.title=t,this.summary=i,this.description=s,this.certificate=r,this.mixedContentType=n,this.recommendations=o}}class f{model;constructor(e){this.model=e}securityStateChanged(e){}visibleSecurityStateChanged({visibleSecurityState:e}){const t=new m(e.securityState,e.certificateSecurityState||null,e.safetyTipInfo||null,e.securityStateIssueIds);this.model.dispatchEventToListeners(p.VisibleSecurityStateChanged,t)}certificateError(e){}}var b=Object.freeze({__proto__:null,SecurityModel:h,securityStateCompare:g,get Events(){return p},SummaryMessages:y,PageVisibleSecurityState:m,CertificateSecurityState:S,SecurityStyleExplanation:v});const x=new CSSStyleSheet;x.replaceSync(".lock-icon,\n.security-property{height:var(--sys-size-9);width:var(--sys-size-9)}.lock-icon-secure{color:var(--sys-color-green)}.lock-icon-insecure{color:var(--sys-color-error)}.lock-icon-insecure-broken{color:var(--sys-color-error)}.security-property-secure{color:var(--sys-color-green)}.security-property-neutral{color:var(--sys-color-error)}.security-property-insecure{color:var(--sys-color-error)}.security-property-insecure-broken{color:var(--sys-color-error)}.security-property-info{color:var(--sys-color-on-surface-subtle)}.security-property-unknown{color:var(--sys-color-on-surface-subtle)}.url-scheme-secure{color:var(--sys-color-green)}.url-scheme-neutral,\n.url-scheme-insecure,\n.url-scheme-insecure-broken{color:var(--sys-color-error)}\n/*# sourceURL=lockIcon.css */\n");const C=new CSSStyleSheet;C.replaceSync(".devtools-link{display:inline-block}.security-main-view{overflow-x:hidden;overflow-y:auto;background-color:var(--sys-color-cdt-base-container)}.security-main-view > div{flex-shrink:0}.security-summary-section-title{font-size:15px;margin:12px 16px;user-select:text}.lock-spectrum{margin:8px 16px;display:flex;align-items:flex-start}.security-summary .lock-icon{flex:none;width:16px;height:16px;margin:0}.security-summary .lock-icon-neutral{margin:0 16px}.security-summary:not(.security-summary-secure) .lock-icon-secure,\n.security-summary:not(.security-summary-neutral) .lock-icon-neutral,\n.security-summary:not(.security-summary-insecure) .lock-icon-insecure,\n.security-summary:not(.security-summary-insecure-broken) .lock-icon-insecure-broken{color:var(--sys-color-state-disabled)}@media (forced-colors: active){.security-summary-neutral .lock-icon-neutral{color:Highlight}.security-summary:not(.security-summary-secure) .lock-icon-secure,\n  .security-summary:not(.security-summary-neutral) .lock-icon-neutral,\n  .security-summary:not(.security-summary-insecure) .lock-icon-insecure,\n  .security-summary:not(.security-summary-insecure-broken) .lock-icon-insecure-broken{color:canvastext}}.triangle-pointer-container{margin:8px 24px 0;padding:0}.triangle-pointer-wrapper{transform:translateX(0);transition:transform 0.3s}.triangle-pointer{width:12px;height:12px;margin-bottom:-6px;margin-left:-6px;transform:rotate(-45deg);border-style:solid;border-width:1px 1px 0 0;background:var(--sys-color-cdt-base-container);border-color:var(--sys-color-neutral-outline)}.security-summary-secure .triangle-pointer-wrapper{transform:translateX(0)}.security-summary-neutral .triangle-pointer-wrapper{transform:translateX(32px)}.security-summary-insecure .triangle-pointer-wrapper{transform:translateX(64px)}.security-summary-insecure-broken .triangle-pointer-wrapper{transform:translateX(64px)}.security-summary-text{padding:16px 24px;border-style:solid;border-width:1px 0;font-size:15px;background:var(--sys-color-cdt-base-container);border-color:var(--sys-color-neutral-outline);user-select:text}.security-summary-secure .triangle-pointer,\n.security-summary-secure .security-summary-text,\n.security-explanation-title-secure{color:var(--sys-color-green)}.security-summary-insecure-broken .triangle-pointer,\n.security-summary-insecure-broken .security-summary-text,\n.security-explanation-title-neutral,\n.security-explanation-title-insecure,\n.security-explanation-title-insecure-broken{color:var(--sys-color-error)}.security-explanation-list{padding-bottom:16px}.security-explanation-list:empty{border-bottom:none;padding:0}.security-explanations-main{margin-top:-5px;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.security-explanations-extra{background-color:transparent}.security-explanation{padding:11px;display:flex;white-space:nowrap;border:none;color:var(--sys-color-token-subtle)}.security-explanation-text{flex:auto;white-space:normal;max-width:400px}.origin-button{margin-top:var(--sys-size-4)}.security-explanation .security-property{flex:none;width:16px;height:16px;margin-right:16px}.security-explanation-title{color:var(--sys-color-token-subtle);margin-top:1px;margin-bottom:8px}.security-mixed-content{margin-top:8px}.security-explanation-recommendations{padding-inline-start:16px}.security-explanation-recommendations > li{margin-bottom:4px}\n/*# sourceURL=mainView.css */\n");const k=new CSSStyleSheet;k.replaceSync(".title-section{padding:16px 0 24px;border-bottom:1px solid var(--sys-color-divider)}.title-section-header{padding-left:16px;padding-bottom:10px;font-size:14px}.security-origin-view{overflow-x:hidden;overflow-y:scroll;display:block;user-select:text}.security-origin-view .origin-view-section{border-bottom:1px solid var(--sys-color-divider);padding:12px 6px 12px 24px;font-size:12px}.origin-view-notes{margin-top:2px;color:var(--sys-color-token-subtle)}.origin-view-section-notes{margin-top:6px;color:var(--sys-color-token-subtle)}.security-origin-view .origin-display{font-size:12px;padding-left:var(--sys-size-8);display:flex;align-items:center}.title-section > .view-network-button{padding:6px 0 0 16px}.security-origin-view .origin-display devtools-icon{width:var(--sys-size-8);height:var(--sys-size-8);margin-right:var(--sys-size-6)}.security-origin-view .origin-view-section-title{margin-top:4px;margin-bottom:4px;font-weight:bold}.security-origin-view .details-table{border-spacing:0}.security-origin-view .details-table-row{white-space:nowrap;overflow:hidden;line-height:22px;vertical-align:top}.security-origin-view .details-table-row > td{padding:0}.security-origin-view .details-table-row > td:first-child{color:var(--sys-color-token-subtle);width:calc(120px + 1em);text-align:right;padding-right:1em}.security-origin-view .details-table-row > td:nth-child(2){white-space:normal}.security-origin-view .sct-details .details-table .details-table-row:last-child td:last-child{border-bottom:1px solid var(--sys-color-divider);padding-bottom:10px}.security-origin-view .sct-details .details-table:last-child .details-table-row:last-child td:last-child{border-bottom:none;padding-bottom:0}.security-origin-view .details-toggle{margin-left:126px}.security-origin-view .sct-toggle{margin-left:145px;padding-top:5px}.security-origin-view .details-table .empty-san{color:var(--sys-color-state-disabled)}.security-origin-view .details-table .san-entry{display:block}.security-origin-view .truncated-san .truncated-entry{display:none}.origin-view-section:last-child{border-bottom:none}.devtools-link{display:inline-flex}\n/*# sourceURL=originView.css */\n");const T=new CSSStyleSheet;T.replaceSync(':host{overflow-x:auto}.tree-outline-disclosure{width:100%}.tree-outline{padding:0;&:focus-visible{box-shadow:none}}.tree-outline li{display:flex;flex-direction:row;align-items:center;padding:2px 5px;overflow:hidden;margin:2px 0;white-space:nowrap;& .leading-icons{margin-right:var(--sys-size-6);flex:none}& .tree-element-title,\n  .highlighted-url,\n  .title{height:inherit;align-content:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}}.tree-outline ol{padding-left:0}.tree-outline li::before{content:none}.security-main-view-reload-message{color:var(--sys-color-token-subtle)}.tree-outline .security-main-view-sidebar-tree-item,\n.tree-outline .security-sidebar-origins,\n.tree-outline li.security-sidebar-origins + .children > li{height:var(--sys-size-12);padding:0 var(--sys-size-8) 0 var(--sys-size-7);margin:0 var(--sys-size-5) 0 0}.tree-outline .security-main-view-sidebar-tree-item,\n.tree-outline li.security-sidebar-origins + .children > li:not(.security-main-view-reload-message){border-radius:0 100px 100px 0;position:relative;&.selected{color:var(--app-color-navigation-drawer-label-selected);background-color:var(--app-color-navigation-drawer-background-selected);& .lock-icon,\n    .security-property{color:var(--app-color-navigation-drawer-label-selected)}& .url-scheme-secure,\n    .url-scheme-neutral,\n    .url-scheme-insecure,\n    .url-scheme-insecure-broken{color:var(--app-color-navigation-drawer-label-selected)}}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle);mask-image:none;content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring)}}@media (forced-colors: active){.tree-outline .security-main-view-sidebar-tree-item,\n  .tree-outline li.security-sidebar-origins + .children > li:not(.security-main-view-reload-message){&.selected{color:HighlightText;background-color:Highlight;& .url-scheme-secure,\n      .url-scheme-neutral,\n      .url-scheme-insecure,\n      .url-scheme-insecure-broken{color:HighlightText}}}}.security-main-view-reload-message,\n.tree-outline li.security-sidebar-origins{margin:var(--sys-size-6) 0 0 0;line-height:var(--sys-size-8);&:hover:not(:has(span[is="dt-checkbox"])) .selection{background:transparent}}\n/*# sourceURL=sidebar.css */\n');const I={overview:"Overview",mainOrigin:"Main origin",nonsecureOrigins:"Non-secure origins",secureOrigins:"Secure origins",unknownCanceled:"Unknown / canceled",reloadToViewDetails:"Reload to view details",mainOriginSecure:"Main origin (secure)",mainOriginNonsecure:"Main origin (non-secure)",securityOverview:"Security overview",secure:"Secure",info:"Info",notSecure:"Not secure",viewCertificate:"View certificate",notSecureBroken:"Not secure (broken)",thisPageIsDangerousFlaggedBy:"This page is dangerous (flagged by Google Safe Browsing).",flaggedByGoogleSafeBrowsing:"Flagged by Google Safe Browsing",toCheckThisPagesStatusVisit:"To check this page's status, visit g.co/safebrowsingstatus.",thisIsAnErrorPage:"This is an error page.",thisPageIsInsecureUnencrypted:"This page is insecure (unencrypted HTTP).",thisPageHasANonhttpsSecureOrigin:"This page has a non-HTTPS secure origin.",thisPageIsSuspicious:"This page is suspicious",chromeHasDeterminedThatThisSiteS:"Chrome has determined that this site could be fake or fraudulent.",ifYouBelieveThisIsShownIn:"If you believe this is shown in error please visit https://g.co/chrome/lookalike-warnings.",possibleSpoofingUrl:"Possible spoofing URL",thisSitesHostnameLooksSimilarToP:"This site's hostname looks similar to {PH1}. Attackers sometimes mimic sites by making small, hard-to-see changes to the domain name.",ifYouBelieveThisIsShownInErrorSafety:"If you believe this is shown in error please visit https://g.co/chrome/lookalike-warnings.",thisPageIsSuspiciousFlaggedBy:"This page is suspicious (flagged by Chrome).",certificate:"Certificate",insecureSha:"insecure (SHA-1)",theCertificateChainForThisSite:"The certificate chain for this site contains a certificate signed using SHA-1.",subjectAlternativeNameMissing:"`Subject Alternative Name` missing",theCertificateForThisSiteDoesNot:"The certificate for this site does not contain a `Subject Alternative Name` extension containing a domain name or IP address.",missing:"missing",thisSiteIsMissingAValidTrusted:"This site is missing a valid, trusted certificate ({PH1}).",validAndTrusted:"valid and trusted",theConnectionToThisSiteIsUsingA:"The connection to this site is using a valid, trusted server certificate issued by {PH1}.",publickeypinningBypassed:"Public-Key-Pinning bypassed",publickeypinningWasBypassedByA:"Public-Key-Pinning was bypassed by a local root certificate.",certificateExpiresSoon:"Certificate expires soon",theCertificateForThisSiteExpires:"The certificate for this site expires in less than 48 hours and needs to be renewed.",connection:"Connection",secureConnectionSettings:"secure connection settings",theConnectionToThisSiteIs:"The connection to this site is encrypted and authenticated using {PH1}, {PH2}, and {PH3}.",sIsObsoleteEnableTlsOrLater:"{PH1} is obsolete. Enable TLS 1.2 or later.",rsaKeyExchangeIsObsoleteEnableAn:"RSA key exchange is obsolete. Enable an ECDHE-based cipher suite.",sIsObsoleteEnableAnAesgcmbased:"{PH1} is obsolete. Enable an AES-GCM-based cipher suite.",theServerSignatureUsesShaWhichIs:"The server signature uses SHA-1, which is obsolete. Enable a SHA-2 signature algorithm instead. (Note this is different from the signature in the certificate.)",obsoleteConnectionSettings:"obsolete connection settings",resources:"Resources",activeMixedContent:"active mixed content",youHaveRecentlyAllowedNonsecure:"You have recently allowed non-secure content (such as scripts or iframes) to run on this site.",mixedContent:"mixed content",thisPageIncludesHttpResources:"This page includes HTTP resources.",nonsecureForm:"non-secure form",thisPageIncludesAFormWithA:'This page includes a form with a non-secure "action" attribute.',activeContentWithCertificate:"active content with certificate errors",youHaveRecentlyAllowedContent:"You have recently allowed content loaded with certificate errors (such as scripts or iframes) to run on this site.",contentWithCertificateErrors:"content with certificate errors",thisPageIncludesResourcesThat:"This page includes resources that were loaded with certificate errors.",allServedSecurely:"all served securely",allResourcesOnThisPageAreServed:"All resources on this page are served securely.",blockedMixedContent:"Blocked mixed content",yourPageRequestedNonsecure:"Your page requested non-secure resources that were blocked.",reloadThePageToRecordRequestsFor:"Reload the page to record requests for HTTP resources.",viewDRequestsInNetworkPanel:"{n, plural, =1 {View # request in Network Panel} other {View # requests in Network Panel}}",origin:"Origin",viewRequestsInNetworkPanel:"View requests in Network Panel",protocol:"Protocol",keyExchange:"Key exchange",cipher:"Cipher",serverSignature:"Server signature",encryptedClientHello:"Encrypted ClientHello",certificateTransparency:"Certificate Transparency",subject:"Subject",validFrom:"Valid from",validUntil:"Valid until",issuer:"Issuer",openFullCertificateDetails:"Open full certificate details",sct:"SCT",logName:"Log name",logId:"Log ID",validationStatus:"Validation status",source:"Source",issuedAt:"Issued at",hashAlgorithm:"Hash algorithm",signatureAlgorithm:"Signature algorithm",signatureData:"Signature data",showFullDetails:"Show full details",hideFullDetails:"Hide full details",thisRequestCompliesWithChromes:"This request complies with `Chrome`'s Certificate Transparency policy.",thisRequestDoesNotComplyWith:"This request does not comply with `Chrome`'s Certificate Transparency policy.",thisResponseWasLoadedFromCache:"This response was loaded from cache. Some security details might be missing.",theSecurityDetailsAboveAreFrom:"The security details above are from the first inspected response.",thisOriginIsANonhttpsSecure:"This origin is a non-HTTPS secure origin.",yourConnectionToThisOriginIsNot:"Your connection to this origin is not secure.",noSecurityInformation:"No security information",noSecurityDetailsAreAvailableFor:"No security details are available for this origin.",na:"(n/a)",showLess:"Show less",showMoreSTotal:"Show more ({PH1} total)",unknownField:"unknown",enabled:"enabled"},E=e.i18n.registerUIStrings("panels/security/SecurityPanel.ts",I),A=e.i18n.getLocalizedString.bind(void 0,E);let R;const P=new Map([[513,"RSA with SHA-1"],[1025,"RSA with SHA-256"],[1281,"RSA with SHA-384"],[1537,"RSA with SHA-512"],[1027,"ECDSA with SHA-256"],[1283,"ECDSA with SHA-384"],[2052,"RSA-PSS with SHA-256"],[2053,"RSA-PSS with SHA-384"],[2054,"RSA-PSS with SHA-512"]]),L="lock",H="warning",M="info";function D(e,t){let i;switch(e){case"neutral":case"insecure":case"insecure-broken":i=H;break;case"secure":i=L;break;case"info":case"unknown":i=M}return n.Icon.create(i,t)}function O(e,t){let i;switch(e){case"unknown":case"neutral":i=M;break;case"insecure":case"insecure-broken":i=H;break;case"secure":i=L;break;case"info":throw new Error(`Unexpected security state ${e}`)}return n.Icon.create(i,t)}class V extends o.Panel.PanelWithSidebar{mainView;sidebarMainViewElement;sidebarTree;lastResponseReceivedForLoaderId;origins;filterRequestCounts;visibleView;eventListeners;securityModel;constructor(){super("security"),this.mainView=new B(this);const e=document.createElement("span");e.classList.add("title"),e.textContent=A(I.overview);this.sidebarMainViewElement=new N({title:e,onSelect:this.setVisibleView.bind(this,this.mainView),getIconForSecurityState:e=>O(e,`lock-icon lock-icon-${e}`),className:"security-main-view-sidebar-tree-item"}),this.sidebarMainViewElement.tooltip=e.textContent,this.sidebarTree=new F(this.sidebarMainViewElement,this.showOrigin.bind(this)),this.panelSidebarElement().appendChild(this.sidebarTree.element),this.lastResponseReceivedForLoaderId=new Map,this.origins=new Map,this.filterRequestCounts=new Map,this.visibleView=null,this.eventListeners=[],this.securityModel=null,t.TargetManager.TargetManager.instance().observeModels(h,this,{scoped:!0}),t.TargetManager.TargetManager.instance().addModelListener(t.ResourceTreeModel.ResourceTreeModel,t.ResourceTreeModel.Events.PrimaryPageChanged,this.onPrimaryPageChanged,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return R&&!t||(R=new V),R}static createCertificateViewerButtonForOrigin(e,i){const r=o.UIUtils.createTextButton(e,(async e=>{e.consume();const r=await t.NetworkManager.MultitargetNetworkManager.instance().getCertificate(i);r.length>0&&s.InspectorFrontendHost.InspectorFrontendHostInstance.showCertificateViewer(r)}),{className:"origin-button",jslogContext:"security.view-certificate-for-origin",title:e});return o.ARIAUtils.markAsButton(r),r}static createCertificateViewerButtonForCert(e,t){const i=o.UIUtils.createTextButton(e,(e=>{e.consume(),s.InspectorFrontendHost.InspectorFrontendHostInstance.showCertificateViewer(t)}),{className:"origin-button",jslogContext:"security.view-certificate"});return o.ARIAUtils.markAsButton(i),i}static createHighlightedUrl(e,t){const i="://",s=e.indexOf(i);if(-1===s){const t=document.createElement("span");return t.textContent=e,t}const r=document.createElement("span");r.classList.add("highlighted-url");const n=e.substr(0,s),o=e.substr(s+3);return r.createChild("span","url-scheme-"+t).textContent=n,r.createChild("span","url-scheme-separator").textContent=i,r.createChild("span").textContent=o,r}updateVisibleSecurityState(e){this.sidebarMainViewElement.setSecurityState(e.securityState),this.mainView.updateVisibleSecurityState(e)}onVisibleSecurityStateChanged({data:e}){this.updateVisibleSecurityState(e)}selectAndSwitchToMainView(){this.sidebarMainViewElement.select(!0)}showOrigin(e){const t=this.origins.get(e);t&&(t.originView||(t.originView=new G(this,e,t)),this.setVisibleView(t.originView))}wasShown(){super.wasShown(),this.visibleView||this.selectAndSwitchToMainView()}focus(){this.sidebarTree.focus()}setVisibleView(e){this.visibleView!==e&&(this.visibleView&&this.visibleView.detach(),this.visibleView=e,e&&this.splitWidget().setMainWidget(e))}onResponseReceived(e){const t=e.data.request;t.resourceType()===i.ResourceType.resourceTypes.Document&&t.loaderId&&this.lastResponseReceivedForLoaderId.set(t.loaderId,t)}processRequest(e){const t=i.ParsedURL.ParsedURL.extractOrigin(e.url());if(!t)return;let s=e.securityState();"blockable"!==e.mixedContentType&&"optionally-blockable"!==e.mixedContentType||(s="insecure");const r=this.origins.get(t);if(r){if(g(s,r.securityState)<0){r.securityState=s;const i=e.securityDetails();i&&(r.securityDetails=i),this.sidebarTree.updateOrigin(t,s),r.originView&&r.originView.setSecurityState(s)}}else{const i={securityState:s,securityDetails:e.securityDetails(),loadedFromCache:e.cached(),originView:void 0};this.origins.set(t,i),this.sidebarTree.addOrigin(t,s)}}onRequestFinished(e){const t=e.data;this.updateFilterRequestCounts(t),this.processRequest(t)}updateFilterRequestCounts(e){if("none"===e.mixedContentType)return;let t="all";e.wasBlocked()?t="blocked":"blockable"===e.mixedContentType?t="block-overridden":"optionally-blockable"===e.mixedContentType&&(t="displayed");const i=this.filterRequestCounts.get(t);i?this.filterRequestCounts.set(t,i+1):this.filterRequestCounts.set(t,1),this.mainView.refreshExplanations()}filterRequestCount(e){return this.filterRequestCounts.get(e)||0}modelAdded(e){if(e.target()!==e.target().outermostTarget())return;this.securityModel=e;const s=e.resourceTreeModel(),r=e.networkManager();this.eventListeners.length&&i.EventTarget.removeEventListeners(this.eventListeners),this.eventListeners=[e.addEventListener(p.VisibleSecurityStateChanged,this.onVisibleSecurityStateChanged,this),s.addEventListener(t.ResourceTreeModel.Events.InterstitialShown,this.onInterstitialShown,this),s.addEventListener(t.ResourceTreeModel.Events.InterstitialHidden,this.onInterstitialHidden,this),r.addEventListener(t.NetworkManager.Events.ResponseReceived,this.onResponseReceived,this),r.addEventListener(t.NetworkManager.Events.RequestFinished,this.onRequestFinished,this)],s.isInterstitialShowing&&this.onInterstitialShown()}modelRemoved(e){this.securityModel===e&&(this.securityModel=null,i.EventTarget.removeEventListeners(this.eventListeners))}onPrimaryPageChanged(e){const{frame:t}=e.data,s=this.lastResponseReceivedForLoaderId.get(t.loaderId);this.selectAndSwitchToMainView(),this.sidebarTree.clearOrigins(),this.origins.clear(),this.lastResponseReceivedForLoaderId.clear(),this.filterRequestCounts.clear(),this.mainView.refreshExplanations();const r=i.ParsedURL.ParsedURL.extractOrigin(s?s.url():t.url);this.sidebarTree.setMainOrigin(r),s&&this.processRequest(s)}onInterstitialShown(){this.selectAndSwitchToMainView(),this.sidebarTree.toggleOriginsList(!0)}onInterstitialHidden(){this.sidebarTree.toggleOriginsList(!1)}}class F extends o.TreeOutline.TreeOutlineInShadow{showOriginInPanel;mainOrigin;originGroupTitles;originGroups;elementsByOrigin;mainViewReloadMessage;constructor(e,t){super(),this.appendChild(e),this.registerCSSFiles([x,T]),this.showOriginInPanel=t,this.mainOrigin=null,this.originGroupTitles=new Map([[U.MainOrigin,A(I.mainOrigin)],[U.NonSecure,A(I.nonsecureOrigins)],[U.Secure,A(I.secureOrigins)],[U.Unknown,A(I.unknownCanceled)]]),this.originGroups=new Map;for(const e of Object.values(U)){const t=this.createOriginGroupElement(this.originGroupTitles.get(e));this.originGroups.set(e,t),this.appendChild(t)}this.mainViewReloadMessage=new o.TreeOutline.TreeElement(A(I.reloadToViewDetails)),this.mainViewReloadMessage.selectable=!1,this.mainViewReloadMessage.listItemElement.classList.add("security-main-view-reload-message");this.originGroups.get(U.MainOrigin).appendChild(this.mainViewReloadMessage),this.clearOriginGroups(),this.elementsByOrigin=new Map}originGroupTitle(e){return this.originGroupTitles.get(e)}originGroupElement(e){return this.originGroups.get(e)}createOriginGroupElement(e){const t=new o.TreeOutline.TreeElement(e,!0);return t.selectable=!1,t.setCollapsible(!1),t.expand(),t.listItemElement.classList.add("security-sidebar-origins"),o.ARIAUtils.setLabel(t.childrenListElement,e),t}toggleOriginsList(e){for(const t of this.originGroups.values())t.hidden=e}addOrigin(e,t){this.mainViewReloadMessage.hidden=!0;const i=new N({title:V.createHighlightedUrl(e,t),onSelect:this.showOriginInPanel.bind(this,e),getIconForSecurityState:e=>D(e,`security-property security-property-${e}`),className:"security-sidebar-tree-item"});i.tooltip=e,this.elementsByOrigin.set(e,i),this.updateOrigin(e,t)}setMainOrigin(e){this.mainOrigin=e}updateOrigin(e,t){const i=this.elementsByOrigin.get(e);let s;if(i.setSecurityState(t),e===this.mainOrigin)s=this.originGroups.get(U.MainOrigin),s.title=A("secure"===t?I.mainOriginSecure:I.mainOriginNonsecure),o.ARIAUtils.setLabel(s.childrenListElement,s.title);else switch(t){case"secure":s=this.originGroupElement(U.Secure);break;case"unknown":s=this.originGroupElement(U.Unknown);break;default:s=this.originGroupElement(U.NonSecure)}const r=i.parent;r!==s&&(r&&(r.removeChild(i),0===r.childCount()&&(r.hidden=!0)),s.appendChild(i),s.hidden=!1)}clearOriginGroups(){for(const[e,t]of this.originGroups)if(e===U.MainOrigin){for(let e=t.childCount()-1;e>0;e--)t.removeChildAtIndex(e);t.title=this.originGroupTitle(U.MainOrigin),t.hidden=!1,this.mainViewReloadMessage.hidden=!1}else t.removeChildren(),t.hidden=!0}clearOrigins(){this.clearOriginGroups(),this.elementsByOrigin.clear()}wasShown(){}}var U;!function(e){e.MainOrigin="MainOrigin",e.NonSecure="NonSecure",e.Secure="Secure",e.Unknown="Unknown"}(U||(U={}));class N extends o.TreeOutline.TreeElement{selectCallback;securityStateInternal;#e;constructor(e){super("",!1),this.selectCallback=e.onSelect,this.listItemElement.appendChild(e.title),this.listItemElement.classList.add(e.className),this.#e=e.getIconForSecurityState,this.securityStateInternal=null,this.setSecurityState("unknown")}setSecurityState(e){this.securityStateInternal=e;const t=this.#e(e);t&&this.setLeadingIcons([t])}securityState(){return this.securityStateInternal}onselect(){return this.selectCallback(),!0}}class B extends o.Widget.VBox{panel;summarySection;securityExplanationsMain;securityExplanationsExtra;lockSpectrum;summaryText;explanations;securityState;constructor(e){super(!0),this.element.setAttribute("jslog",`${a.pane("security.main-view")}`),this.setMinimumSize(200,100),this.contentElement.classList.add("security-main-view"),this.panel=e,this.summarySection=this.contentElement.createChild("div","security-summary"),this.securityExplanationsMain=this.contentElement.createChild("div","security-explanation-list security-explanations-main"),this.securityExplanationsExtra=this.contentElement.createChild("div","security-explanation-list security-explanations-extra");const t=this.summarySection.createChild("div","security-summary-section-title");t.textContent=A(I.securityOverview),o.ARIAUtils.markAsHeading(t,1);const i=this.summarySection.createChild("div","lock-spectrum");this.lockSpectrum=new Map([["secure",i.appendChild(O("secure","lock-icon lock-icon-secure"))],["neutral",i.appendChild(O("neutral","lock-icon lock-icon-neutral"))],["insecure",i.appendChild(O("insecure","lock-icon lock-icon-insecure"))]]),o.Tooltip.Tooltip.install(this.getLockSpectrumDiv("secure"),A(I.secure)),o.Tooltip.Tooltip.install(this.getLockSpectrumDiv("neutral"),A(I.info)),o.Tooltip.Tooltip.install(this.getLockSpectrumDiv("insecure"),A(I.notSecure)),this.summarySection.createChild("div","triangle-pointer-container").createChild("div","triangle-pointer-wrapper").createChild("div","triangle-pointer"),this.summaryText=this.summarySection.createChild("div","security-summary-text"),o.ARIAUtils.markAsHeading(this.summaryText,2),this.explanations=null,this.securityState=null}getLockSpectrumDiv(e){const t=this.lockSpectrum.get(e);if(!t)throw new Error(`Invalid argument: ${e}`);return t}addExplanation(e,t){const i=e.createChild("div","security-explanation");i.classList.add("security-explanation-"+t.securityState);const s=D(t.securityState,"security-property security-property-"+t.securityState);i.appendChild(s);const r=i.createChild("div","security-explanation-text"),n=r.createChild("div","security-explanation-title");if(t.title?(n.createChild("span").textContent=t.title+" - ",n.createChild("span","security-explanation-title-"+t.securityState).textContent=t.summary):n.textContent=t.summary,r.createChild("div").textContent=t.description,t.certificate.length&&r.appendChild(V.createCertificateViewerButtonForCert(A(I.viewCertificate),t.certificate)),t.recommendations&&t.recommendations.length){const e=r.createChild("ul","security-explanation-recommendations");for(const i of t.recommendations)e.createChild("li").textContent=i}return r}updateVisibleSecurityState(e){this.summarySection.classList.remove("security-summary-"+this.securityState),this.securityState=e.securityState,this.summarySection.classList.add("security-summary-"+this.securityState),"insecure"===this.securityState?(this.getLockSpectrumDiv("insecure").classList.add("lock-icon-insecure"),this.getLockSpectrumDiv("insecure").classList.remove("lock-icon-insecure-broken"),o.Tooltip.Tooltip.install(this.getLockSpectrumDiv("insecure"),A(I.notSecure))):"insecure-broken"===this.securityState&&(this.getLockSpectrumDiv("insecure").classList.add("lock-icon-insecure-broken"),this.getLockSpectrumDiv("insecure").classList.remove("lock-icon-insecure"),o.Tooltip.Tooltip.install(this.getLockSpectrumDiv("insecure"),A(I.notSecureBroken)));const{summary:t,explanations:i}=this.getSecuritySummaryAndExplanations(e);this.summaryText.textContent=t||y[this.securityState](),this.explanations=this.orderExplanations(i),this.refreshExplanations()}getSecuritySummaryAndExplanations(e){const{securityState:t,securityStateIssueIds:i}=e;let s;const r=[];if(s=this.explainSafetyTipSecurity(e,s,r),i.includes("malicious-content"))s=A(I.thisPageIsDangerousFlaggedBy),r.unshift(new v("insecure",void 0,A(I.flaggedByGoogleSafeBrowsing),A(I.toCheckThisPagesStatusVisit)));else{if(i.includes("is-error-page")&&(null===e.certificateSecurityState||null===e.certificateSecurityState.certificateNetworkError))return s=A(I.thisIsAnErrorPage),{summary:s,explanations:r};"insecure-broken"===t&&i.includes("scheme-is-not-cryptographic")&&(s=s||A(I.thisPageIsInsecureUnencrypted))}return i.includes("scheme-is-not-cryptographic")?("neutral"!==t||i.includes("insecure-origin")||(s=A(I.thisPageHasANonhttpsSecureOrigin)),{summary:s,explanations:r}):(this.explainCertificateSecurity(e,r),this.explainConnectionSecurity(e,r),this.explainContentSecurity(e,r),{summary:s,explanations:r})}explainSafetyTipSecurity(e,t,i){const{securityStateIssueIds:s,safetyTipInfo:r}=e,n=[];if(s.includes("bad_reputation")){const e=`${A(I.chromeHasDeterminedThatThisSiteS)}\n\n${A(I.ifYouBelieveThisIsShownIn)}`;n.push({summary:A(I.thisPageIsSuspicious),description:e})}else if(s.includes("lookalike")&&r&&r.safeUrl){const e=new URL(r.safeUrl).hostname,t=`${A(I.thisSitesHostnameLooksSimilarToP,{PH1:e})}\n\n${A(I.ifYouBelieveThisIsShownInErrorSafety)}`;n.push({summary:A(I.possibleSpoofingUrl),description:t})}return n.length>0&&(t=t||A(I.thisPageIsSuspiciousFlaggedBy),i.push(new v("insecure",void 0,n[0].summary,n[0].description))),t}explainCertificateSecurity(e,t){const{certificateSecurityState:i,securityStateIssueIds:s}=e,r=A(I.certificate);if(i&&i.certificateHasSha1Signature){const e=A(I.insecureSha),s=A(I.theCertificateChainForThisSite);i.certificateHasWeakSignature?t.push(new v("insecure",r,e,s,i.certificate,"none")):t.push(new v("neutral",r,e,s,i.certificate,"none"))}i&&s.includes("cert-missing-subject-alt-name")&&t.push(new v("insecure",r,A(I.subjectAlternativeNameMissing),A(I.theCertificateForThisSiteDoesNot),i.certificate,"none")),i&&null!==i.certificateNetworkError?t.push(new v("insecure",r,A(I.missing),A(I.thisSiteIsMissingAValidTrusted,{PH1:i.certificateNetworkError}),i.certificate,"none")):i&&!i.certificateHasSha1Signature&&t.push(new v("secure",r,A(I.validAndTrusted),A(I.theConnectionToThisSiteIsUsingA,{PH1:i.issuer}),i.certificate,"none")),s.includes("pkp-bypassed")&&t.push(new v("info",r,A(I.publickeypinningBypassed),A(I.publickeypinningWasBypassedByA))),i&&i.isCertificateExpiringSoon()&&t.push(new v("info",void 0,A(I.certificateExpiresSoon),A(I.theCertificateForThisSiteExpires)))}explainConnectionSecurity(e,t){const i=e.certificateSecurityState;if(!i)return;const s=A(I.connection);if(i.modernSSL)return void t.push(new v("secure",s,A(I.secureConnectionSettings),A(I.theConnectionToThisSiteIs,{PH1:i.protocol,PH2:i.getKeyExchangeName(),PH3:i.getCipherFullName()})));const r=[];i.obsoleteSslProtocol&&r.push(A(I.sIsObsoleteEnableTlsOrLater,{PH1:i.protocol})),i.obsoleteSslKeyExchange&&r.push(A(I.rsaKeyExchangeIsObsoleteEnableAn)),i.obsoleteSslCipher&&r.push(A(I.sIsObsoleteEnableAnAesgcmbased,{PH1:i.cipher})),i.obsoleteSslSignature&&r.push(A(I.theServerSignatureUsesShaWhichIs)),t.push(new v("info",s,A(I.obsoleteConnectionSettings),A(I.theConnectionToThisSiteIs,{PH1:i.protocol,PH2:i.getKeyExchangeName(),PH3:i.getCipherFullName()}),void 0,void 0,r))}explainContentSecurity(e,t){let i=!0;const s=A(I.resources),r=e.securityStateIssueIds;r.includes("ran-mixed-content")&&(i=!1,t.push(new v("insecure",s,A(I.activeMixedContent),A(I.youHaveRecentlyAllowedNonsecure),[],"blockable"))),r.includes("displayed-mixed-content")&&(i=!1,t.push(new v("neutral",s,A(I.mixedContent),A(I.thisPageIncludesHttpResources),[],"optionally-blockable"))),r.includes("contained-mixed-form")&&(i=!1,t.push(new v("neutral",s,A(I.nonsecureForm),A(I.thisPageIncludesAFormWithA)))),null!==e.certificateSecurityState&&null!==e.certificateSecurityState.certificateNetworkError||(r.includes("ran-content-with-cert-error")&&(i=!1,t.push(new v("insecure",s,A(I.activeContentWithCertificate),A(I.youHaveRecentlyAllowedContent)))),r.includes("displayed-content-with-cert-errors")&&(i=!1,t.push(new v("neutral",s,A(I.contentWithCertificateErrors),A(I.thisPageIncludesResourcesThat))))),i&&(r.includes("scheme-is-not-cryptographic")||t.push(new v("secure",s,A(I.allServedSecurely),A(I.allResourcesOnThisPageAreServed))))}orderExplanations(e){if(0===e.length)return e;const t=["insecure","neutral","secure","info"],i=[];for(const s of t)i.push(...e.filter((e=>e.securityState===s)));return i}refreshExplanations(){if(this.securityExplanationsMain.removeChildren(),this.securityExplanationsExtra.removeChildren(),this.explanations){for(const e of this.explanations)if("info"===e.securityState)this.addExplanation(this.securityExplanationsExtra,e);else switch(e.mixedContentType){case"blockable":this.addMixedContentExplanation(this.securityExplanationsMain,e,"block-overridden");break;case"optionally-blockable":this.addMixedContentExplanation(this.securityExplanationsMain,e,"displayed");break;default:this.addExplanation(this.securityExplanationsMain,e)}if(this.panel.filterRequestCount("blocked")>0){const e={securityState:"info",summary:A(I.blockedMixedContent),description:A(I.yourPageRequestedNonsecure),mixedContentType:"blockable",certificate:[],title:""};this.addMixedContentExplanation(this.securityExplanationsMain,e,"blocked")}}}addMixedContentExplanation(e,t,i){const s=this.addExplanation(e,t),r=this.panel.filterRequestCount(i);if(!r){return void(s.createChild("div","security-mixed-content").textContent=A(I.reloadThePageToRecordRequestsFor))}const n=s.createChild("button","security-mixed-content devtools-link text-button link-style");o.ARIAUtils.markAsLink(n),n.tabIndex=0,n.textContent=A(I.viewDRequestsInNetworkPanel,{n:r}),n.addEventListener("click",this.showNetworkFilter.bind(this,i))}showNetworkFilter(e,t){t.consume(),i.Revealer.reveal(r.UIFilter.UIRequestFilter.filters([{filterType:r.UIFilter.FilterType.MixedContent,filterValue:e}]))}wasShown(){super.wasShown(),this.registerCSSFiles([x,C])}}class G extends o.Widget.VBox{panel;originLockIcon;constructor(t,s,n){super(),this.element.setAttribute("jslog",`${a.pane("security.origin-view")}`),this.panel=t,this.setMinimumSize(200,100),this.element.classList.add("security-origin-view");const c=this.element.createChild("div","title-section"),l=c.createChild("div","title-section-header");l.textContent=A(I.origin),o.ARIAUtils.markAsHeading(l,1);const u=c.createChild("div","origin-display");this.originLockIcon=u.createChild("span");const d=D(n.securityState,`security-property security-property-${n.securityState}`);this.originLockIcon.appendChild(d),u.appendChild(V.createHighlightedUrl(s,n.securityState));const h=c.createChild("div","view-network-button"),g=o.UIUtils.createTextButton(A(I.viewRequestsInNetworkPanel),(e=>{e.consume();const t=new i.ParsedURL.ParsedURL(s);i.Revealer.reveal(r.UIFilter.UIRequestFilter.filters([{filterType:r.UIFilter.FilterType.Domain,filterValue:t.host},{filterType:r.UIFilter.FilterType.Scheme,filterValue:t.scheme}]))}),{jslogContext:"reveal-in-network"});if(h.appendChild(g),o.ARIAUtils.markAsLink(g),n.securityDetails){const p=this.element.createChild("div","origin-view-section"),y=p.createChild("div","origin-view-section-title");y.textContent=A(I.connection),o.ARIAUtils.markAsHeading(y,2);let m=new q;if(p.appendChild(m.element()),m.addRow(A(I.protocol),n.securityDetails.protocol),n.securityDetails.keyExchange&&n.securityDetails.keyExchangeGroup?m.addRow(A(I.keyExchange),n.securityDetails.keyExchange+" with "+n.securityDetails.keyExchangeGroup):n.securityDetails.keyExchange?m.addRow(A(I.keyExchange),n.securityDetails.keyExchange):n.securityDetails.keyExchangeGroup&&m.addRow(A(I.keyExchange),n.securityDetails.keyExchangeGroup),n.securityDetails.serverSignatureAlgorithm){let L=P.get(n.securityDetails.serverSignatureAlgorithm);L??=A(I.unknownField)+" ("+n.securityDetails.serverSignatureAlgorithm+")",m.addRow(A(I.serverSignature),L)}m.addRow(A(I.cipher),n.securityDetails.cipher+(n.securityDetails.mac?" with "+n.securityDetails.mac:"")),n.securityDetails.encryptedClientHello&&m.addRow(A(I.encryptedClientHello),A(I.enabled));const S=this.element.createChild("div","origin-view-section"),w=S.createChild("div","origin-view-section-title");w.textContent=A(I.certificate),o.ARIAUtils.markAsHeading(w,2);const v=n.securityDetails.signedCertificateTimestampList.length,f=n.securityDetails.certificateTransparencyCompliance;let b;if(v||"unknown"!==f){b=this.element.createChild("div","origin-view-section");const H=b.createChild("div","origin-view-section-title");H.textContent=A(I.certificateTransparency),o.ARIAUtils.markAsHeading(H,2)}const x=this.createSanDiv(n.securityDetails.sanList),C=new Date(1e3*n.securityDetails.validFrom).toUTCString(),k=new Date(1e3*n.securityDetails.validTo).toUTCString();if(m=new q,S.appendChild(m.element()),m.addRow(A(I.subject),n.securityDetails.subjectName),m.addRow(e.i18n.lockedString("SAN"),x),m.addRow(A(I.validFrom),C),m.addRow(A(I.validUntil),k),m.addRow(A(I.issuer),n.securityDetails.issuer),m.addRow("",V.createCertificateViewerButtonForOrigin(A(I.openFullCertificateDetails),s)),!b)return;const T=new q;T.element().classList.add("sct-summary"),b.appendChild(T.element());for(let M=0;M<v;M++){const O=n.securityDetails.signedCertificateTimestampList[M];T.addRow(A(I.sct),O.logDescription+" ("+O.origin+", "+O.status+")")}const E=b.createChild("div","sct-details");E.classList.add("hidden");for(let F=0;F<v;F++){const U=new q;E.appendChild(U.element());const N=n.securityDetails.signedCertificateTimestampList[F];U.addRow(A(I.logName),N.logDescription),U.addRow(A(I.logId),N.logId.replace(/(.{2})/g,"$1 ")),U.addRow(A(I.validationStatus),N.status),U.addRow(A(I.source),N.origin),U.addRow(A(I.issuedAt),new Date(N.timestamp).toUTCString()),U.addRow(A(I.hashAlgorithm),N.hashAlgorithm),U.addRow(A(I.signatureAlgorithm),N.signatureAlgorithm),U.addRow(A(I.signatureData),N.signatureData.replace(/(.{2})/g,"$1 "))}if(v){function B(){let e;const t=!E.classList.contains("hidden");e=A(t?I.showFullDetails:I.hideFullDetails),G.textContent=e,o.ARIAUtils.setLabel(G,e),o.ARIAUtils.setExpanded(G,!t),T.element().classList.toggle("hidden"),E.classList.toggle("hidden")}const G=o.UIUtils.createTextButton(A(I.showFullDetails),B,{className:"details-toggle",jslogContext:"security.toggle-scts-details"});b.appendChild(G)}switch(f){case"compliant":b.createChild("div","origin-view-section-notes").textContent=A(I.thisRequestCompliesWithChromes);break;case"not-compliant":b.createChild("div","origin-view-section-notes").textContent=A(I.thisRequestDoesNotComplyWith)}const R=this.element.createChild("div","origin-view-section origin-view-notes");n.loadedFromCache&&(R.createChild("div").textContent=A(I.thisResponseWasLoadedFromCache)),R.createChild("div").textContent=A(I.theSecurityDetailsAboveAreFrom)}else if("secure"===n.securityState){const j=this.element.createChild("div","origin-view-section"),W=j.createChild("div","origin-view-section-title");W.textContent=A(I.secure),o.ARIAUtils.markAsHeading(W,2),j.createChild("div").textContent=A(I.thisOriginIsANonhttpsSecure)}else if("unknown"!==n.securityState){const z=this.element.createChild("div","origin-view-section"),K=z.createChild("div","origin-view-section-title");K.textContent=A(I.notSecure),o.ARIAUtils.markAsHeading(K,2),z.createChild("div").textContent=A(I.yourConnectionToThisOriginIsNot)}else{const $=this.element.createChild("div","origin-view-section"),_=$.createChild("div","origin-view-section-title");_.textContent=A(I.noSecurityInformation),o.ARIAUtils.markAsHeading(_,2),$.createChild("div").textContent=A(I.noSecurityDetailsAreAvailableFor)}}createSanDiv(e){const t=document.createElement("div");if(0===e.length)t.textContent=A(I.na),t.classList.add("empty-san");else{const i=2,s=e.length>i+1;for(let r=0;r<e.length;r++){const n=t.createChild("span","san-entry");n.textContent=e[r],s&&r>=i&&n.classList.add("truncated-entry")}if(s){function a(){const i=t.classList.contains("truncated-san");let s;i?(t.classList.remove("truncated-san"),s=A(I.showLess)):(t.classList.add("truncated-san"),s=A(I.showMoreSTotal,{PH1:e.length})),c.textContent=s,o.ARIAUtils.setLabel(c,s),o.ARIAUtils.setExpanded(c,i)}const c=o.UIUtils.createTextButton(A(I.showMoreSTotal,{PH1:e.length}),a,{jslogContext:"security.toggle-san-truncation"});t.appendChild(c),a()}}return t}setSecurityState(e){this.originLockIcon.removeChildren();const t=D(e,`security-property security-property-${e}`);this.originLockIcon.appendChild(t)}wasShown(){super.wasShown(),this.registerCSSFiles([k,x])}}class q{elementInternal;constructor(){this.elementInternal=document.createElement("table"),this.elementInternal.classList.add("details-table")}element(){return this.elementInternal}addRow(e,t){const i=this.elementInternal.createChild("tr","details-table-row");i.createChild("td").textContent=e;const s=i.createChild("td");"string"==typeof t?s.textContent=t:s.appendChild(t)}}var j=Object.freeze({__proto__:null,SecurityPanel:V,SecurityPanelSidebarTree:F,get OriginGroup(){return U},SecurityMainView:B,SecurityOriginView:G,SecurityDetailsTable:q});export{b as SecurityModel,j as SecurityPanel};
