(()=>{var A;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},U(B)}function x(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function Q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?x(Object(G),!0).forEach(function(H){N(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):x(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function N(B,C,G){if(C=z(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function z(B){var C=E(B,"string");return U(C)=="symbol"?C:String(C)}function E(B,C){if(U(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var W=Object.defineProperty,GB=function B(C,G){for(var H in G)W(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},D={lessThanXSeconds:{one:"minna en 1 sek\xFAnda",other:"minna en {{count}} sek\xFAndur"},xSeconds:{one:"1 sek\xFAnda",other:"{{count}} sek\xFAndur"},halfAMinute:"h\xE1lf m\xEDn\xFAta",lessThanXMinutes:{one:"minna en 1 m\xEDn\xFAta",other:"minna en {{count}} m\xEDn\xFAtur"},xMinutes:{one:"1 m\xEDn\xFAta",other:"{{count}} m\xEDn\xFAtur"},aboutXHours:{one:"u.\xFE.b. 1 klukkustund",other:"u.\xFE.b. {{count}} klukkustundir"},xHours:{one:"1 klukkustund",other:"{{count}} klukkustundir"},xDays:{one:"1 dagur",other:"{{count}} dagar"},aboutXWeeks:{one:"um viku",other:"um {{count}} vikur"},xWeeks:{one:"1 viku",other:"{{count}} vikur"},aboutXMonths:{one:"u.\xFE.b. 1 m\xE1nu\xF0ur",other:"u.\xFE.b. {{count}} m\xE1nu\xF0ir"},xMonths:{one:"1 m\xE1nu\xF0ur",other:"{{count}} m\xE1nu\xF0ir"},aboutXYears:{one:"u.\xFE.b. 1 \xE1r",other:"u.\xFE.b. {{count}} \xE1r"},xYears:{one:"1 \xE1r",other:"{{count}} \xE1r"},overXYears:{one:"meira en 1 \xE1r",other:"meira en {{count}} \xE1r"},almostXYears:{one:"n\xE6stum 1 \xE1r",other:"n\xE6stum {{count}} \xE1r"}},S=function B(C,G,H){var J,X=D[C];if(typeof X==="string")J=X;else if(G===1)J=X.one;else J=X.other.replace("{{count}}",G.toString());if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"\xED "+J;else return J+" s\xED\xF0an";return J};function $(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var M={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"d.MM.y"},R={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'s\xED\xF0asta' dddd 'kl.' p",yesterday:"'\xED g\xE6r kl.' p",today:"'\xED dag kl.' p",tomorrow:"'\xE1 morgun kl.' p",nextWeek:"dddd 'kl.' p",other:"P"},w=function B(C,G,H,J){return j[C]};function I(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var _={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["fyrir Krist","eftir Krist"]},f={narrow:["1","2","3","4"],abbreviated:["1F","2F","3F","4F"],wide:["1. fj\xF3r\xF0ungur","2. fj\xF3r\xF0ungur","3. fj\xF3r\xF0ungur","4. fj\xF3r\xF0ungur"]},F={narrow:["J","F","M","A","M","J","J","\xC1","S","\xD3","N","D"],abbreviated:["jan.","feb.","mars","apr\xEDl","ma\xED","j\xFAn\xED","j\xFAl\xED","\xE1g\xFAst","sept.","okt.","n\xF3v.","des."],wide:["jan\xFAar","febr\xFAar","mars","apr\xEDl","ma\xED","j\xFAn\xED","j\xFAl\xED","\xE1g\xFAst","september","okt\xF3ber","n\xF3vember","desember"]},v={narrow:["S","M","\xDE","M","F","F","L"],short:["Su","M\xE1","\xDEr","Mi","Fi","F\xF6","La"],abbreviated:["sun.","m\xE1n.","\xFEri.","mi\xF0.","fim.","f\xF6s.","lau."],wide:["sunnudagur","m\xE1nudagur","\xFEri\xF0judagur","mi\xF0vikudagur","fimmtudagur","f\xF6studagur","laugardagur"]},P={narrow:{am:"f",pm:"e",midnight:"mi\xF0n\xE6tti",noon:"h\xE1degi",morning:"morgunn",afternoon:"s\xED\xF0degi",evening:"kv\xF6ld",night:"n\xF3tt"},abbreviated:{am:"f.h.",pm:"e.h.",midnight:"mi\xF0n\xE6tti",noon:"h\xE1degi",morning:"morgunn",afternoon:"s\xED\xF0degi",evening:"kv\xF6ld",night:"n\xF3tt"},wide:{am:"fyrir h\xE1degi",pm:"eftir h\xE1degi",midnight:"mi\xF0n\xE6tti",noon:"h\xE1degi",morning:"morgunn",afternoon:"s\xED\xF0degi",evening:"kv\xF6ld",night:"n\xF3tt"}},k={narrow:{am:"f",pm:"e",midnight:"\xE1 mi\xF0n\xE6tti",noon:"\xE1 h\xE1degi",morning:"a\xF0 morgni",afternoon:"s\xED\xF0degis",evening:"um kv\xF6ld",night:"um n\xF3tt"},abbreviated:{am:"f.h.",pm:"e.h.",midnight:"\xE1 mi\xF0n\xE6tti",noon:"\xE1 h\xE1degi",morning:"a\xF0 morgni",afternoon:"s\xED\xF0degis",evening:"um kv\xF6ld",night:"um n\xF3tt"},wide:{am:"fyrir h\xE1degi",pm:"eftir h\xE1degi",midnight:"\xE1 mi\xF0n\xE6tti",noon:"\xE1 h\xE1degi",morning:"a\xF0 morgni",afternoon:"s\xED\xF0degis",evening:"um kv\xF6ld",night:"um n\xF3tt"}},b=function B(C,G){var H=Number(C);return H+"."},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?c(Z,function(K){return K.test(Y)}):m(Z,function(K){return K.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var CB=C.slice(Y.length);return{value:T,rest:CB}}}function m(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function c(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function y(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var p=/^(\d+)(\.)?/i,d=/\d+(\.)?/i,g={narrow:/^(f\.Kr\.|e\.Kr\.)/i,abbreviated:/^(f\.Kr\.|e\.Kr\.)/i,wide:/^(fyrir Krist|eftir Krist)/i},u={any:[/^(f\.Kr\.)/i,/^(e\.Kr\.)/i]},l={narrow:/^[1234]\.?/i,abbreviated:/^q[1234]\.?/i,wide:/^[1234]\.? fjórðungur/i},i={any:[/1\.?/i,/2\.?/i,/3\.?/i,/4\.?/i]},n={narrow:/^[jfmásónd]/i,abbreviated:/^(jan\.|feb\.|mars\.|apríl\.|maí|júní|júlí|águst|sep\.|oct\.|nov\.|dec\.)/i,wide:/^(januar|febrúar|mars|apríl|maí|júní|júlí|águst|september|október|nóvember|desember)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^á/i,/^s/i,/^ó/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maí/i,/^jún/i,/^júl/i,/^áu/i,/^s/i,/^ó/i,/^n/i,/^d/i]},o={narrow:/^[smtwf]/i,short:/^(su|má|þr|mi|fi|fö|la)/i,abbreviated:/^(sun|mán|þri|mið|fim|fös|lau)\.?/i,wide:/^(sunnudagur|mánudagur|þriðjudagur|miðvikudagur|fimmtudagur|föstudagur|laugardagur)/i},r={narrow:[/^s/i,/^m/i,/^þ/i,/^m/i,/^f/i,/^f/i,/^l/i],any:[/^su/i,/^má/i,/^þr/i,/^mi/i,/^fi/i,/^fö/i,/^la/i]},a={narrow:/^(f|e|síðdegis|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i,any:/^(fyrir hádegi|eftir hádegi|[ef]\.?h\.?|síðdegis|morgunn|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i},e={any:{am:/^f/i,pm:/^e/i,midnight:/^mi/i,noon:/^há/i,morning:/morgunn/i,afternoon:/síðdegi/i,evening:/kvöld/i,night:/nótt/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function B(C){return parseInt(C,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"is",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{is:BB})})})();

//# debugId=4894713DA2E7019964756E2164756E21
